#!/bin/bash

set -e

echo "withai@ruishu" | sudo -S apt update || true

echo "withai@ruishu" | sudo -S apt install -y evtest brightnessctl libfdk-aac2 libmpv-dev

echo "withai@ruishu" | sudo -S cp -f ../data/flutter_assets/packages/surgsmart/assets/texts/sudoers/evtest.txt /etc/sudoers.d/evtest

echo "withai@ruishu" | sudo -S cp -f ../data/flutter_assets/packages/surgsmart/assets/texts/sudoers/brightnessctl.txt /etc/sudoers.d/brightnessctl

echo "withai@ruishu" | sudo -S cp -f ../data/flutter_assets/packages/surgsmart/assets/texts/sudoers/nmcli.txt /etc/sudoers.d/nmcli

echo "withai@ruishu" | sudo -S cp -f ../data/flutter_assets/packages/surgsmart/assets/texts/polkit/10-networkmanager.pkla.txt /etc/polkit-1/localauthority/50-local.d/10-networkmanager.pkla

echo "withai@ruishu" | sudo -S brightnessctl set 80

if [ -f /etc/acpi/events/powerbtn ]; then
    echo "withai@ruishu" | sudo -S rm -f /etc/acpi/events/powerbtn
fi

gsettings set org.gnome.settings-daemon.plugins.power idle-dim false

#cd ../data/flutter_assets/packages/surgsmart/assets/texts/zips/ || exit
#
#mv bluez-5.64.txt bluez-5.64.zip
#
#unzip -q bluez-5.64.zip
#
#cd bluez-5.64 || exit
#
#chmod +x ./configure
#
#echo "withai@ruishu" | sudo -S apt install -y make libical-dev libreadline-dev python3-docutils
#
#echo "withai@ruishu" | sudo -S systemctl stop bluetooth
#
#echo "withai@ruishu" | sudo -S systemctl disable bluetooth
#
#echo "withai@ruishu" | sudo -S systemctl unmask bluetooth
#
#echo "withai@ruishu" | sudo -S apt remove -y bluez
#
#./configure --prefix=/usr --mandir=/usr/share/man --sysconfdir=/etc --localstatedir=/var
#
#echo "withai@ruishu" | sudo -S make install
#
#echo "export PATH=/usr/libexec/bluetooth:\$PATH" >>~/.bashrc
#
#echo "withai@ruishu" | sudo -S systemctl enable bluetooth
#
#echo "withai@ruishu" | sudo -S systemctl start bluetooth
