---
Language: Cpp # 设置为 C++ 语言
AlwaysBreakAfterReturnType: All # 始终在返回类型后换行
AllowShortEnumsOnASingleLine: false # 不允许将枚举类型放在一行
AlignAfterOpenBracket: BlockIndent # 括号后的内容块级对齐, 必须换行生效
AlignArrayOfStructures: Right # 数组或结构体对齐到右侧
AllowShortLambdasOnASingleLine: Empty # 允许将空lambda表达式放在一行
AllowShortFunctionsOnASingleLine: Empty # 允许将空函数放在一行
AllowShortIfStatementsOnASingleLine: WithoutElse # 允许将没有else的if语句放在一行
AlwaysBreakBeforeMultilineStrings: false # 不在多行字符串前换行
BinPackArguments: false # 实参一行或每行一个
BinPackParameters: false # 形参一行行或每行一个
ColumnLimit: 0 # 不限制列宽
ConstructorInitializerAllOnOneLineOrOnePerLine: true # 构造函数初始化列表每行一个
IndentWidth: 4 # 缩进宽度为 4 个空格
UseTab: Never # 使用空格代替 Tab
---

