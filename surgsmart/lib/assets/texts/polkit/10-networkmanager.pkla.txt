[Allow WiFi Scan for All Users]
Identity=unix-user:withai
Action=org.freedesktop.NetworkManager.wifi.scan
ResultAny=yes
ResultInactive=yes
ResultActive=yes

[Allow Network Control for WiFi Connection]
Identity=unix-user:withai
Action=org.freedesktop.NetworkManager.network-control
ResultAny=yes
ResultInactive=yes
ResultActive=yes

[Allow System WiFi Settings Modification]
Identity=unix-user:withai
Action=org.freedesktop.NetworkManager.settings.modify.system
ResultAny=yes
ResultInactive=yes
ResultActive=yes
