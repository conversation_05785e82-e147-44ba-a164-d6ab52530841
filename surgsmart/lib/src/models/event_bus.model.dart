class EventBusInfo<T> {
  final EventBusType type;
  final T? data;
  EventBusInfo({required this.type, this.data});
}

enum EventBusType {
  surgeryRoomState,
  surgeryLiveState,
  surgeryRoomDispose,
  startSurgery,
  loudspeakerDeviceChange,
  microphoneDeviceChange,
  taskStateChange,
  powerOff,
  closeSelect,
  openSelect,
  settingCardScroll,
  videoDeviceChange,
  videoSignalChange,
  screenEvent,
  onScreenRemove,
  netWorkChange,
  ethernetAvailable,
  staplingAnomaly,
}
