import 'package:surgsmart/src/models/mqtt/device_ai.model.dart';

class SurgeryEvent {
  EventInfo? clamping;
  EventInfo? amputation;
  EventInfo? suturing;
  EventInfo? bleeding;
}

class EventInfo {
  final EventAttrInfo attr;

  final EventInsightInfo insight;

  EventInfo({required this.attr, required this.insight});

  Map<String, dynamic> toMap() {
    return {'attr': attr.toMap(), 'insight': insight.toMap()};
  }
}

