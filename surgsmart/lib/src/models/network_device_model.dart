import 'package:dbus_wifi/models/wifi_network.dart';

class NetworkDevice {
  final String device;
  final String type;
  final String connection;
  String state;
  Map? extensions = {}; // 存储扩展信息，如业务逻辑状态标记等
  NetworkConfig? config; // 网络配置信息
  WifiNetwork? wifiNetwork; // wifi网络信息
  MobileNetWorkInfo? mobileNetWorkInfo; // 移动网络信息

  NetworkDevice({
    required this.device,
    required this.type,
    required this.state,
    required this.connection,
    this.extensions,
    this.config,
    this.wifiNetwork,
    this.mobileNetWorkInfo,
  });
  bool isConnected() {
    return state == 'connected' || state == '已连接'; //unmanaged 未托管
  }

  bool isAvailable() {
    return state != 'unavailable' &&
        state != '不可用' &&
        state != 'unmanaged' &&
        state != '未托管';
  }

  bool isDisconnected() {
    return state == 'disconnected' || state == '已断开';
  }

  @override
  String toString() {
    return 'Device: $device, Type: $type, State: $state, Connection: $connection , Extensions: $extensions, Config: $config, WifiNetwork: $wifiNetwork, MobileNetWorkInfo: $mobileNetWorkInfo';
  }

  NetworkDevice clone() {
    return NetworkDevice(
      connection: connection,
      type: type,
      device: device,
      state: state,
      extensions: Map.from(extensions ?? {}),
      config: config?.clone(),
      wifiNetwork:
          wifiNetwork != null
              ? WifiNetwork(
                ssid: wifiNetwork!.ssid,
                mac: wifiNetwork!.mac,
                strength: wifiNetwork!.strength,
                path: wifiNetwork!.path,
                security: wifiNetwork!.security,
                mode: wifiNetwork!.mode,
              )
              : null,
      mobileNetWorkInfo:
          mobileNetWorkInfo != null
              ? MobileNetWorkInfo(
                connection: mobileNetWorkInfo!.connection,
                apn: mobileNetWorkInfo!.apn,
                mode: mobileNetWorkInfo!.mode,
              )
              : null,
    );
  }
}

class NetworkConfig {
  String method = 'auto';
  List<String> ip = [];
  List<String> subnetMask = []; // 子网掩码（CIDR 格式，如 "24"）
  String gateway = '';
  List<String> dnsServers = [];
  NetworkConfig();
  NetworkConfig.clone({
    this.method = 'auto',
    this.ip = const [],
    this.subnetMask = const [],
    this.gateway = '',
    this.dnsServers = const [],
  });

  final cidrMap = {
    0: '0.0.0.0',
    1: '*********',
    2: '*********',
    3: '*********',
    4: '240.0.0.0',
    5: '*********',
    6: '*********',
    7: '*********',
    8: '*********',
    9: '***********',
    10: '***********',
    11: '***********',
    12: '***********',
    13: '***********',
    14: '***********',
    15: '***********',
    16: '***********',
    17: '*************',
    18: '*************',
    19: '*************',
    20: '*************',
    21: '*************',
    22: '*************',
    23: '*************',
    24: '*************',
    25: '***************',
    26: '***************',
    27: '***************',
    28: '***************',
    29: '***************',
    30: '***************',
    31: '***************',
    32: '***************',
  };

  @override
  String toString() {
    return '''
IP:          ${ip.join(', ')}/${subnetMask.join(', ')}
Gateway:     $gateway
DNS:         ${dnsServers.join(', ')}
''';
  }

  String subnetMaskDotDecimal(int cidr) {
    return cidrMap[cidr] ?? '';
  }

  bool isValidIP(String ip) {
    final ipRegex = RegExp(
      r'^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$',
    );
    return ipRegex.hasMatch(ip);
  }

  NetworkConfig clone() {
    return NetworkConfig.clone(
      method: method,
      ip: List.from(ip),
      subnetMask: List.from(subnetMask),
      gateway: gateway,
      dnsServers: List.from(dnsServers),
    );
  }
}

enum NetWorkType {
  none,
  ethernet,
  gsm,
  wifi;

  static Map<NetWorkType, String> labels = {
    NetWorkType.ethernet: '有线网络',
    NetWorkType.wifi: 'Wi-Fi',
    NetWorkType.gsm: '蜂窝网络',
  };

  static NetWorkType fromString(String type) {
    return NetWorkType.values.firstWhere((e) => e.name == type);
  }
}

enum MobileNetWorkMode {
  auto,
  only5G,
  only4G;

  static MobileNetWorkMode fromString(String mode) {
    return MobileNetWorkMode.values.firstWhere((e) => e.name == mode);
  }

  String getMode() {
    switch (this) {
      case MobileNetWorkMode.only5G:
        return '5g';
      case MobileNetWorkMode.only4G:
        return '4g';
      default:
        return 'any';
    }
  }
}

class MobileNetWorkInfo {
  final String connection;
  String apn;
  MobileNetWorkMode mode;

  MobileNetWorkInfo({
    required this.connection,
    required this.apn,
    required this.mode,
  });

  Map toMap() {
    return {'connection': connection, 'apn': apn, 'mode': mode.name};
  }

  @override
  String toString() {
    return '''
Connection: $connection
APN: $apn
Mode: ${mode.name}
''';
  }
}

enum NetWorkMethod {
  auto('自动'),
  manual('手动');

  final String label;

  const NetWorkMethod(this.label);
}
