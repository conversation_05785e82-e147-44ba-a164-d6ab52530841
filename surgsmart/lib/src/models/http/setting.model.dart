import 'package:app_foundation/app_foundation.dart';

/// 设置信息
@ModelDetector()
class SettingInfo extends _SettingInfo {
  late final List<SettingItem> datas;
}

/// 设置条目
@ModelDetector()
class SettingItem extends _SettingItem {
  late final String category;
  late final String key;
  late final String value;
}

class _SettingInfo extends AppModel {
  @override
  AppModel extractClone() => SettingInfo();

  @override
  SettingInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as SettingInfo;
    final datas0 = <SettingItem>[];
    self.datas = datas0;
    map["datas"]?.forEach((element) {
      datas0.add(SettingItem().initWith(element));
    });
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as SettingInfo;
    map["datas"] = self.datas.map((element) {
      return element.toMap();
    }).toList();
    return map;
  }
} // _SettingInfo

class _SettingItem extends AppModel {
  @override
  AppModel extractClone() => SettingItem();

  @override
  SettingItem initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as SettingItem;
    self.category = map["category"] ?? "";
    self.key = map["key"] ?? "";
    self.value = map["value"] ?? "";
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as SettingItem;
    map["category"] = self.category;
    map["key"] = self.key;
    map["value"] = self.value;
    return map;
  }
} // _SettingItem
