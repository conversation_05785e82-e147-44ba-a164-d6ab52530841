import 'package:app_foundation/app_foundation.dart';

@ModelDetector()
class FileStatus extends _FileStatus {
  ///是否存在未上传文件
  late final bool hasRequireUploadFile;
}

class _FileStatus extends AppModel {
  @override
  AppModel extractClone() => FileStatus();

  @override
  FileStatus initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as FileStatus;
    self.hasRequireUploadFile = map["has_require_upload_file"] ?? false;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as FileStatus;
    map["has_require_upload_file"] = self.hasRequireUploadFile;
    return map;
  }
} // _FileStatus
