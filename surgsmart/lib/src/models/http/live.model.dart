import 'package:app_foundation/app_foundation.dart';

/// 直播信息
@ModelDetector()
class LiveInfo extends _LiveInfo {
  ///直播人数
  late final int count;
}

/// 直播分享信息
@ModelDetector()
class LiveShareInfo extends _LiveShareInfo {
  ///分享密钥
  late final String shareKey;
}

/// 开始直播信息
@ModelDetector()
class StartLiveInfo extends _StartLiveInfo {
  late final String title;
  late final String description;
  late final int deviceId;
  late final int surgeryId;

  /// 腔镜推流地址
  late final String pushUrl;

  /// 外景推流地址
  late final String externalPushUrl;
}

class _LiveInfo extends AppModel {
  @override
  AppModel extractClone() => LiveInfo();

  @override
  LiveInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as LiveInfo;
    self.count = map["count"]?.toInt() ?? 0;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as LiveInfo;
    map["count"] = self.count;
    return map;
  }
} // _LiveInfo

class _LiveShareInfo extends AppModel {
  @override
  AppModel extractClone() => LiveShareInfo();

  @override
  LiveShareInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as LiveShareInfo;
    self.shareKey = map["share_key"] ?? "";
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as LiveShareInfo;
    map["share_key"] = self.shareKey;
    return map;
  }
} // _LiveShareInfo

class _StartLiveInfo extends AppModel {
  @override
  AppModel extractClone() => StartLiveInfo();

  @override
  StartLiveInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as StartLiveInfo;
    self.title = map["title"] ?? "";
    self.description = map["description"] ?? "";
    self.deviceId = map["device_id"]?.toInt() ?? 0;
    self.surgeryId = map["surgery_id"]?.toInt() ?? 0;
    self.pushUrl = map["push_url"] ?? "";
    self.externalPushUrl = map["external_push_url"] ?? "";
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as StartLiveInfo;
    map["title"] = self.title;
    map["description"] = self.description;
    map["device_id"] = self.deviceId;
    map["surgery_id"] = self.surgeryId;
    map["push_url"] = self.pushUrl;
    map["external_push_url"] = self.externalPushUrl;
    return map;
  }
} // _StartLiveInfo
