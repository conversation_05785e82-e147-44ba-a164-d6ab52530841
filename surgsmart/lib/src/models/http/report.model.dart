import 'package:app_foundation/app_foundation.dart';

class Report {
  int bodyCavityDuration;
  int surgeryDuration;
  List<SurgeryEventInfo> events;

  Report({
    required this.bodyCavityDuration,
    required this.surgeryDuration,
    required this.events,
  });

  factory Report.fromJson(Map<String, dynamic> json) {
    return Report(
      bodyCavityDuration: json['body_cavity_duration'],
      surgeryDuration: json['surgery_duration'],
      events: List<SurgeryEventInfo>.from(
          json['events'].map((x) => SurgeryEventInfo().initWith(x))),
    );
  }
}

/// 手术事件列表信息
@ModelDetector()
class SurgeryEventListInfo extends _SurgeryEventListInfo {
  late final List<SurgeryEventInfo> datas;
}

/// 手术事件
@ModelDetector()
class SurgeryEventInfo extends _SurgeryEventInfo {
  /// 事件 ID
  late final int value;

  /// 中文名称
  late final String label;

  /// 英文名称
  late final String name;

  /// 本次手术事件数
  late final int thisSurgeryCount;

  /// 本次手术时长
  late final int thisSurgeryDuration;

  /// 所有手术事件平均次数
  late final double overallAverageCount;

  /// 所有手术事件平均时长
  late final double overallAverageDuration;
}

class _SurgeryEventListInfo extends AppModel {
  @override
  AppModel extractClone() => SurgeryEventListInfo();

  @override
  SurgeryEventListInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as SurgeryEventListInfo;
    final datas0 = <SurgeryEventInfo>[];
    self.datas = datas0;
    map["datas"]?.forEach((element) {
      datas0.add(SurgeryEventInfo().initWith(element));
    });
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as SurgeryEventListInfo;
    map["datas"] = self.datas.map((element) {
      return element.toMap();
    }).toList();
    return map;
  }
} // _SurgeryEventListInfo

class _SurgeryEventInfo extends AppModel {
  @override
  AppModel extractClone() => SurgeryEventInfo();

  @override
  SurgeryEventInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as SurgeryEventInfo;
    self.value = map["value"]?.toInt() ?? 0;
    self.label = map["label"] ?? "";
    self.name = map["name"] ?? "";
    self.thisSurgeryCount = map["this_surgery_count"]?.toInt() ?? 0;
    self.thisSurgeryDuration = map["this_surgery_duration"]?.toInt() ?? 0;
    self.overallAverageCount = map["overall_average_count"]?.toDouble() ?? 0.0;
    self.overallAverageDuration = map["overall_average_duration"]?.toDouble() ?? 0.0;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as SurgeryEventInfo;
    map["value"] = self.value;
    map["label"] = self.label;
    map["name"] = self.name;
    map["this_surgery_count"] = self.thisSurgeryCount;
    map["this_surgery_duration"] = self.thisSurgeryDuration;
    map["overall_average_count"] = self.overallAverageCount;
    map["overall_average_duration"] = self.overallAverageDuration;
    return map;
  }
} // _SurgeryEventInfo
