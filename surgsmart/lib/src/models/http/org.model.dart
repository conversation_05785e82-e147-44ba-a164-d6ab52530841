import 'package:app_foundation/app_foundation.dart';

/// 组织列表
@ModelDetector()
class OrgListInfo extends _OrgListInfo {
  late final List<OrgInfo> datas;
}

/// 组织信息
@ModelDetector()
class OrgInfo extends _OrgInfo {
//组织类型
  late final String orgType;

  /// 组织ID
  late final int id;

  /// 组织名字
  late final String name;

  //完整名称
  late final String fullName;

  //层级
  late final int depth;

  //组织树id
  late final int treeId;

  //上级组织id
  late final int parentId;
}

class _OrgListInfo extends AppModel {
  @override
  AppModel extractClone() => OrgListInfo();

  @override
  OrgListInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as OrgListInfo;
    final datas0 = <OrgInfo>[];
    self.datas = datas0;
    map["datas"]?.forEach((element) {
      datas0.add(OrgInfo().initWith(element));
    });
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as OrgListInfo;
    map["datas"] = self.datas.map((element) {
      return element.toMap();
    }).toList();
    return map;
  }
} // _OrgListInfo

class _OrgInfo extends AppModel {
  @override
  AppModel extractClone() => OrgInfo();

  @override
  OrgInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as OrgInfo;
    self.orgType = map["org_type"] ?? "";
    self.id = map["id"]?.toInt() ?? 0;
    self.name = map["name"] ?? "";
    self.fullName = map["full_name"] ?? "";
    self.depth = map["depth"]?.toInt() ?? 0;
    self.treeId = map["tree_id"]?.toInt() ?? 0;
    self.parentId = map["parent_id"]?.toInt() ?? 0;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as OrgInfo;
    map["org_type"] = self.orgType;
    map["id"] = self.id;
    map["name"] = self.name;
    map["full_name"] = self.fullName;
    map["depth"] = self.depth;
    map["tree_id"] = self.treeId;
    map["parent_id"] = self.parentId;
    return map;
  }
} // _OrgInfo
