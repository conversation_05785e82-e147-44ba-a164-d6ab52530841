import 'package:app_foundation/app_foundation.dart';

@ModelDetector()
class VersionInfo extends _VersionInfo {
  ///有新版本
  late final bool hasNewVersion;

  ///新版本
  late final String newVersion;

  ///当前版本
  late final String version;

  ///  是否强制更新
  late final bool isForce;

  late final bool allowInstall;

  late final String newVersionIdentifier;
}

@ModelDetector()
class VersionData extends _VersionData {
  late final List<Version> datas;
}

@ModelDetector()
class Version extends _Version {
  late final int id;
  late final String name;
  late final int type;
  late final String version;
  late final int build;
  late final String description;
  late final String createdAt;
  late final String updatedAt;
}

class _VersionInfo extends AppModel {
  @override
  AppModel extractClone() => VersionInfo();

  @override
  VersionInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as VersionInfo;
    self.hasNewVersion = map["has_new_version"] ?? false;
    self.newVersion = map["new_version"] ?? "";
    self.version = map["version"] ?? "";
    self.isForce = map["is_force"] ?? false;
    self.allowInstall = map["allow_install"] ?? false;
    self.newVersionIdentifier = map["new_version_identifier"] ?? "";
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as VersionInfo;
    map["has_new_version"] = self.hasNewVersion;
    map["new_version"] = self.newVersion;
    map["version"] = self.version;
    map["is_force"] = self.isForce;
    map["allow_install"] = self.allowInstall;
    map["new_version_identifier"] = self.newVersionIdentifier;
    return map;
  }
} // _VersionInfo

class _VersionData extends AppModel {
  @override
  AppModel extractClone() => VersionData();

  @override
  VersionData initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as VersionData;
    final datas0 = <Version>[];
    self.datas = datas0;
    map["datas"]?.forEach((element) {
      datas0.add(Version().initWith(element));
    });
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as VersionData;
    map["datas"] = self.datas.map((element) {
      return element.toMap();
    }).toList();
    return map;
  }
} // _VersionData

class _Version extends AppModel {
  @override
  AppModel extractClone() => Version();

  @override
  Version initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as Version;
    self.id = map["id"]?.toInt() ?? 0;
    self.name = map["name"] ?? "";
    self.type = map["type"]?.toInt() ?? 0;
    self.version = map["version"] ?? "";
    self.build = map["build"]?.toInt() ?? 0;
    self.description = map["description"] ?? "";
    self.createdAt = map["created_at"] ?? "";
    self.updatedAt = map["updated_at"] ?? "";
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as Version;
    map["id"] = self.id;
    map["name"] = self.name;
    map["type"] = self.type;
    map["version"] = self.version;
    map["build"] = self.build;
    map["description"] = self.description;
    map["created_at"] = self.createdAt;
    map["updated_at"] = self.updatedAt;
    return map;
  }
} // _Version
