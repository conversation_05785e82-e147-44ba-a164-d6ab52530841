import 'package:app_foundation/app_foundation.dart';

/// 手术统计信息
@ModelDetector()
class SurgeryStatisticInfo extends _SurgeryStatisticInfo {
  /// 手术数量
  late final int surgeryCount;

  /// 手术时长
  late final int surgeryDuration;
}

/// 手术时长信息
@ModelDetector()
class SurgeryDurationInfo extends _SurgeryDurationInfo {
  /// 本场手术时长
  @FieldAttr(name: "this_surgery")
  late final int current;

  /// 平均手术时长
  @FieldAttr(name: "my_average")
  late final double average;
}

/// 体腔内时长信息
@ModelDetector()
class SurgeryInBodyDurationInfo extends _SurgeryInBodyDurationInfo {
  /// 本场手术体腔内时长
  @FieldAttr(name: "this_surgery")
  late final int current;
}

class _SurgeryStatisticInfo extends AppModel {
  @override
  AppModel extractClone() => SurgeryStatisticInfo();

  @override
  SurgeryStatisticInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as SurgeryStatisticInfo;
    self.surgeryCount = map["surgery_count"]?.toInt() ?? 0;
    self.surgeryDuration = map["surgery_duration"]?.toInt() ?? 0;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as SurgeryStatisticInfo;
    map["surgery_count"] = self.surgeryCount;
    map["surgery_duration"] = self.surgeryDuration;
    return map;
  }
} // _SurgeryStatisticInfo

class _SurgeryDurationInfo extends AppModel {
  @override
  AppModel extractClone() => SurgeryDurationInfo();

  @override
  SurgeryDurationInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as SurgeryDurationInfo;
    self.current = map["this_surgery"]?.toInt() ?? 0;
    self.average = map["my_average"]?.toDouble() ?? 0.0;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as SurgeryDurationInfo;
    map["this_surgery"] = self.current;
    map["my_average"] = self.average;
    return map;
  }
} // _SurgeryDurationInfo

class _SurgeryInBodyDurationInfo extends AppModel {
  @override
  AppModel extractClone() => SurgeryInBodyDurationInfo();

  @override
  SurgeryInBodyDurationInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as SurgeryInBodyDurationInfo;
    self.current = map["this_surgery"]?.toInt() ?? 0;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as SurgeryInBodyDurationInfo;
    map["this_surgery"] = self.current;
    return map;
  }
} // _SurgeryInBodyDurationInfo
