import 'package:app_foundation/app_foundation.dart';

/// 分页源数据信息
@ModelDetector()
class PageMetaInfo extends _PageMetaInfo {
  /// 页数
  late final int page;

  /// 每页数量
  late final int pageSize;

  /// 总数
  late final int count;
}

class _PageMetaInfo extends AppModel {
  @override
  AppModel extractClone() => PageMetaInfo();

  @override
  PageMetaInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as PageMetaInfo;
    self.page = map["page"]?.toInt() ?? 0;
    self.pageSize = map["page_size"]?.toInt() ?? 0;
    self.count = map["count"]?.toInt() ?? 0;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as PageMetaInfo;
    map["page"] = self.page;
    map["page_size"] = self.pageSize;
    map["count"] = self.count;
    return map;
  }
} // _PageMetaInfo
