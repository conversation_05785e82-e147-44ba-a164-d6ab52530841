class FileSearchValue {
  String? admissionNumber;
  String? procedureName;
  int? userId;
  int? startTime;
  int? endTime;

  FileSearchValue({
    this.admissionNumber,
    this.procedureName,
    this.userId,
    this.startTime,
    this.endTime,
  });

  factory FileSearchValue.fromJson(Map<String, dynamic> json) {
    return FileSearchValue(
      admissionNumber: json['admission_number__contains'],
      procedureName: json['procedure_name'],
      userId: json['user_id'],
      startTime: json['surgery_time__gte'],
      endTime: json['surgery_time__lte'],
    );
  }

  int? get total => null;

  Map<String, dynamic> toJson() {
    return {
      'admission_number__contains': admissionNumber,
      'procedure_name': procedureName,
      'user_id': userId,
      'surgery_time__gte': startTime,
      'surgery_time__lte': endTime,
    };
  }
}
