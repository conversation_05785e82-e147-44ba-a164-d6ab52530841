import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/models/http/meta.model.dart';
import 'package:surgsmart/src/models/http/procedure.model.dart';
import 'package:surgsmart/src/models/http/surgery.model.dart';

/// 手术文件列表信息
@ModelDetector()
class SurgeryFileStatusListInfo extends _SurgeryFileStatusListInfo {
  late final List<SurgeryFileStatus> datas;
}

/// 手术文件状态
@ModelDetector()
class SurgeryFileStatus extends _SurgeryFileStatus {
  /// 文件ID
  late final int id;

  /// 文件状态: 未同步 - 10, 同步中 - 20, 已同步 - 30
  late final int fileSyncStatus;
}

/// 手术文件列表信息
@ModelDetector()
class SurgeryFileListInfo extends _SurgeryFileListInfo {
  /// 手术文件分页信息
  late final PageMetaInfo meta;

  /// 手术文件数据列表
  late final List<SurgeryFileInfo> data;
}

/// 手术文件信息
@ModelDetector()
class SurgeryFileInfo extends _SurgeryFileInfo {
  @FieldIgnore()
  bool check = false;

  /// 手术文件ID
  late final int id;

  /// 术式信息
  late final OrgProcedureInfo? procedure;

  /// 主刀信息
  late final SurgeryUserInfo? user;

  /// 住院号
  late final String? admissionNumber;

  /// 手术开始时间
  late final int? surgeryTime;

  /// 文件大小
  late final int? totalFileSize;

  /// 视频宽度
  late final int? videoWidth;

  /// 视频高度
  late final int? videoHeight;

  /// 文件同步状态
  late final int? fileSyncStatus;
}

/// 文件属性列表信息
@ModelDetector()
class SurgeryFileAttributeListInfo extends _SurgeryFileAttributeListInfo {
  late final List<SurgeryFileAttribute> datas;
}

/// 文件属性
@ModelDetector()
class SurgeryFileAttribute extends _SurgeryFileAttribute {
  /// 文件ID
  late final int id;

  /// 文件大小 (字节)
  late final int fileSize;

  /// 文件路径
  late final String filePath;

  /// 文件导出名字
  late final String fileExportName;
}

class _SurgeryFileStatusListInfo extends AppModel {
  @override
  AppModel extractClone() => SurgeryFileStatusListInfo();

  @override
  SurgeryFileStatusListInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as SurgeryFileStatusListInfo;
    final datas0 = <SurgeryFileStatus>[];
    self.datas = datas0;
    map["datas"]?.forEach((element) {
      datas0.add(SurgeryFileStatus().initWith(element));
    });
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as SurgeryFileStatusListInfo;
    map["datas"] = self.datas.map((element) {
      return element.toMap();
    }).toList();
    return map;
  }
} // _SurgeryFileStatusListInfo

class _SurgeryFileStatus extends AppModel {
  @override
  AppModel extractClone() => SurgeryFileStatus();

  @override
  SurgeryFileStatus initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as SurgeryFileStatus;
    self.id = map["id"]?.toInt() ?? 0;
    self.fileSyncStatus = map["file_sync_status"]?.toInt() ?? 0;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as SurgeryFileStatus;
    map["id"] = self.id;
    map["file_sync_status"] = self.fileSyncStatus;
    return map;
  }
} // _SurgeryFileStatus

class _SurgeryFileListInfo extends AppModel {
  @override
  AppModel extractClone() => SurgeryFileListInfo();

  @override
  SurgeryFileListInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as SurgeryFileListInfo;
    self.meta = PageMetaInfo().initWith(map["meta"] ?? {});
    final data0 = <SurgeryFileInfo>[];
    self.data = data0;
    map["data"]?.forEach((element) {
      data0.add(SurgeryFileInfo().initWith(element));
    });
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as SurgeryFileListInfo;
    map["meta"] = self.meta.toMap();
    map["data"] = self.data.map((element) {
      return element.toMap();
    }).toList();
    return map;
  }
} // _SurgeryFileListInfo

class _SurgeryFileInfo extends AppModel {
  @override
  AppModel extractClone() => SurgeryFileInfo();

  @override
  SurgeryFileInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as SurgeryFileInfo;
    self.id = map["id"]?.toInt() ?? 0;
    if (map["procedure"] != null) {
      self.procedure = OrgProcedureInfo().initWith(map["procedure"]);
    } else {
      self.procedure = null;
    }
    if (map["user"] != null) {
      self.user = SurgeryUserInfo().initWith(map["user"]);
    } else {
      self.user = null;
    }
    self.admissionNumber = map["admission_number"];
    self.surgeryTime = map["surgery_time"]?.toInt();
    self.totalFileSize = map["total_file_size"]?.toInt();
    self.videoWidth = map["video_width"]?.toInt();
    self.videoHeight = map["video_height"]?.toInt();
    self.fileSyncStatus = map["file_sync_status"]?.toInt();
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as SurgeryFileInfo;
    map["id"] = self.id;
    map["procedure"] = self.procedure?.toMap();
    map["user"] = self.user?.toMap();
    map["admission_number"] = self.admissionNumber;
    map["surgery_time"] = self.surgeryTime;
    map["total_file_size"] = self.totalFileSize;
    map["video_width"] = self.videoWidth;
    map["video_height"] = self.videoHeight;
    map["file_sync_status"] = self.fileSyncStatus;
    return map;
  }
} // _SurgeryFileInfo

class _SurgeryFileAttributeListInfo extends AppModel {
  @override
  AppModel extractClone() => SurgeryFileAttributeListInfo();

  @override
  SurgeryFileAttributeListInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as SurgeryFileAttributeListInfo;
    final datas0 = <SurgeryFileAttribute>[];
    self.datas = datas0;
    map["datas"]?.forEach((element) {
      datas0.add(SurgeryFileAttribute().initWith(element));
    });
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as SurgeryFileAttributeListInfo;
    map["datas"] = self.datas.map((element) {
      return element.toMap();
    }).toList();
    return map;
  }
} // _SurgeryFileAttributeListInfo

class _SurgeryFileAttribute extends AppModel {
  @override
  AppModel extractClone() => SurgeryFileAttribute();

  @override
  SurgeryFileAttribute initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as SurgeryFileAttribute;
    self.id = map["id"]?.toInt() ?? 0;
    self.fileSize = map["file_size"]?.toInt() ?? 0;
    self.filePath = map["file_path"] ?? "";
    self.fileExportName = map["file_export_name"] ?? "";
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as SurgeryFileAttribute;
    map["id"] = self.id;
    map["file_size"] = self.fileSize;
    map["file_path"] = self.filePath;
    map["file_export_name"] = self.fileExportName;
    return map;
  }
} // _SurgeryFileAttribute
