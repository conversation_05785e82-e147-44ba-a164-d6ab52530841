class UpdateProgressMessage {
  String type;
  int duration;
  int createdAt;
  FileResult data;

  UpdateProgressMessage.fromJson(Map<String, dynamic> json)
      : type = json['type'],
        duration = json['duration'],
        createdAt = json['createdAt'],
        data = FileResult.fromJson(json['data']);
}

class FileResult {
  double progress;
  int remainingTime;

  FileResult.fromJson(Map<String, dynamic> json)
      : progress = json['progress'],
        remainingTime = json['remainingTime'];
}
