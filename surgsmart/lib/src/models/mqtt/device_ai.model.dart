import 'package:app_foundation/app_foundation.dart';

enum CvsStatus { pending, ongoing, stopped, expired }

@ModelDetector()
class AiFullMessage extends _AiFullMessage {
  late final String identifier;
  late final String type;
  late final int duration;
  late final int createdAt;
  late final AiFullData data;
}

@ModelDetector()
class AiFullData extends _AiFullData {
  late final int bleedingReplay;
  late final int bleeding;
  late final int bigBleeding;
  late final BleedingPoint bleedingPointDetection;
  late final int phase;
  late final List<InstrumentDet> instrumentDet;
  late final List<int> instrumentCls;
  late final List<double> cvs;
  late final int body;
  late final int amputation;
  late final int staplerAmputation;
  late final int scissorAmputation;
  late final int suturing;
  late final int clamping;
}

@ModelDetector()
class InstrumentDet extends _InstrumentDet {
  late final int classification;
  late final double score;
  late final List<double> bbox;
}

/// CVS评分信息
@ModelDetector()
class CvsInfo extends _CvsInfo {
  /// 状态
  @FieldAttr(convertEnumByString: true)
  late CvsStatus status;

  late final double cvs1;

  late final double cvs2;

  late final double cvs3;
}

/// 出血信息mqtt实体类
@ModelDetector()
class BleedingInfo extends _BleedingInfo {
  late final String type;

  late final int createdAt;

  late BleedingData data;
}

/// 出血信息
@ModelDetector()
class BleedingData extends _BleedingData {
  late final int bleeding;
  late final int bleedingReplay; //0:不变 1：大出血 9：止血
  late BleedingPoint bleedingPointDetection;
}

/// 出血数据
@ModelDetector()
class BleedingPoint extends _BleedingPoint {
  late final int startFrame;

  late List<List<PointsData>> positions;
}

/// 出血点位
@ModelDetector()
class PointsData extends _PointsData {
  late final double x;
  late final double y;
  late final int frame;
}

@ModelDetector()
class EventOperationListInfo extends _EventOperationListInfo {
  late final List<EventOperationInfo> datas;
}

/// 体腔内时长信息
@ModelDetector(nameType: NameType.camel)
class EventOperationInfo extends _EventOperationInfo {
  /// 事件ID
  late final int id;

  /// 事件时长
  late final double duration;

  /// 是否正在发生
  late final bool isHappening;
}

/// 事件属性信息
@ModelDetector()
class EventAttrInfo extends _EventAttrInfo {
  /// 事件ID
  late final int value;

  /// 事件代码(英文名)
  late final String name;

  /// 事件标签(中文名)
  late final String label;
}

@ModelDetector()
class EventInsightListInfo extends _EventInsightListInfo {
  late final List<EventInsightInfo> datas;
}

/// 事件统计信息
@ModelDetector(nameType: NameType.camel)
class EventInsightInfo extends _EventInsightInfo {
  /// 事件ID
  late final int id;

  /// 事件次数
  late final int count;

  /// 事件时长
  late final int duration;

  /// 是否正在发生
  late final bool isHappening;
}

class _AiFullMessage extends AppModel {
  @override
  AppModel extractClone() => AiFullMessage();

  @override
  AiFullMessage initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as AiFullMessage;
    self.identifier = map["identifier"] ?? "";
    self.type = map["type"] ?? "";
    self.duration = map["duration"]?.toInt() ?? 0;
    self.createdAt = map["created_at"]?.toInt() ?? 0;
    self.data = AiFullData().initWith(map["data"] ?? {});
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as AiFullMessage;
    map["identifier"] = self.identifier;
    map["type"] = self.type;
    map["duration"] = self.duration;
    map["created_at"] = self.createdAt;
    map["data"] = self.data.toMap();
    return map;
  }
} // _AiFullMessage

class _AiFullData extends AppModel {
  @override
  AppModel extractClone() => AiFullData();

  @override
  AiFullData initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as AiFullData;
    self.bleedingReplay = map["bleeding_replay"]?.toInt() ?? 0;
    self.bleeding = map["bleeding"]?.toInt() ?? 0;
    self.bigBleeding = map["big_bleeding"]?.toInt() ?? 0;
    self.bleedingPointDetection = BleedingPoint().initWith(map["bleeding_point_detection"] ?? {});
    self.phase = map["phase"]?.toInt() ?? 0;
    final instrumentDet0 = <InstrumentDet>[];
    self.instrumentDet = instrumentDet0;
    map["instrument_det"]?.forEach((element) {
      instrumentDet0.add(InstrumentDet().initWith(element));
    });
    final instrumentCls0 = <int>[];
    self.instrumentCls = instrumentCls0;
    map["instrument_cls"]?.forEach((element) {
      instrumentCls0.add(element?.toInt() ?? 0);
    });
    final cvs0 = <double>[];
    self.cvs = cvs0;
    map["cvs"]?.forEach((element) {
      cvs0.add(element?.toDouble() ?? 0.0);
    });
    self.body = map["body"]?.toInt() ?? 0;
    self.amputation = map["amputation"]?.toInt() ?? 0;
    self.staplerAmputation = map["stapler_amputation"]?.toInt() ?? 0;
    self.scissorAmputation = map["scissor_amputation"]?.toInt() ?? 0;
    self.suturing = map["suturing"]?.toInt() ?? 0;
    self.clamping = map["clamping"]?.toInt() ?? 0;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as AiFullData;
    map["bleeding_replay"] = self.bleedingReplay;
    map["bleeding"] = self.bleeding;
    map["big_bleeding"] = self.bigBleeding;
    map["bleeding_point_detection"] = self.bleedingPointDetection.toMap();
    map["phase"] = self.phase;
    map["instrument_det"] = self.instrumentDet.map((element) {
      return element.toMap();
    }).toList();
    map["instrument_cls"] = self.instrumentCls.map((element) {
      return element;
    }).toList();
    map["cvs"] = self.cvs.map((element) {
      return element;
    }).toList();
    map["body"] = self.body;
    map["amputation"] = self.amputation;
    map["stapler_amputation"] = self.staplerAmputation;
    map["scissor_amputation"] = self.scissorAmputation;
    map["suturing"] = self.suturing;
    map["clamping"] = self.clamping;
    return map;
  }
} // _AiFullData

class _InstrumentDet extends AppModel {
  @override
  AppModel extractClone() => InstrumentDet();

  @override
  InstrumentDet initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as InstrumentDet;
    self.classification = map["classification"]?.toInt() ?? 0;
    self.score = map["score"]?.toDouble() ?? 0.0;
    final bbox0 = <double>[];
    self.bbox = bbox0;
    map["bbox"]?.forEach((element) {
      bbox0.add(element?.toDouble() ?? 0.0);
    });
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as InstrumentDet;
    map["classification"] = self.classification;
    map["score"] = self.score;
    map["bbox"] = self.bbox.map((element) {
      return element;
    }).toList();
    return map;
  }
} // _InstrumentDet

class _CvsInfo extends AppModel {
  @override
  AppModel extractClone() => CvsInfo();

  @override
  CvsInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as CvsInfo;
    final statusIndexOrName = map["status"]?.toString() ?? CvsStatus.values.first.name;
    self.status = CvsStatus.values.where((e) => e.name == statusIndexOrName).first;
    self.cvs1 = map["cvs1"]?.toDouble() ?? 0.0;
    self.cvs2 = map["cvs2"]?.toDouble() ?? 0.0;
    self.cvs3 = map["cvs3"]?.toDouble() ?? 0.0;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as CvsInfo;
    map["status"] = self.status.name;
    map["cvs1"] = self.cvs1;
    map["cvs2"] = self.cvs2;
    map["cvs3"] = self.cvs3;
    return map;
  }
} // _CvsInfo

class _BleedingInfo extends AppModel {
  @override
  AppModel extractClone() => BleedingInfo();

  @override
  BleedingInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as BleedingInfo;
    self.type = map["type"] ?? "";
    self.createdAt = map["created_at"]?.toInt() ?? 0;
    self.data = BleedingData().initWith(map["data"] ?? {});
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as BleedingInfo;
    map["type"] = self.type;
    map["created_at"] = self.createdAt;
    map["data"] = self.data.toMap();
    return map;
  }
} // _BleedingInfo

class _BleedingData extends AppModel {
  @override
  AppModel extractClone() => BleedingData();

  @override
  BleedingData initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as BleedingData;
    self.bleeding = map["bleeding"]?.toInt() ?? 0;
    self.bleedingReplay = map["bleeding_replay"]?.toInt() ?? 0;
    self.bleedingPointDetection = BleedingPoint().initWith(map["bleeding_point_detection"] ?? {});
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as BleedingData;
    map["bleeding"] = self.bleeding;
    map["bleeding_replay"] = self.bleedingReplay;
    map["bleeding_point_detection"] = self.bleedingPointDetection.toMap();
    return map;
  }
} // _BleedingData

class _BleedingPoint extends AppModel {
  @override
  AppModel extractClone() => BleedingPoint();

  @override
  BleedingPoint initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as BleedingPoint;
    self.startFrame = map["start_frame"]?.toInt() ?? 0;
    final positions0 = <List<PointsData>>[];
    self.positions = positions0;
    map["positions"]?.forEach((element) {
      final positions1 = <PointsData>[];
      positions0.add(positions1);
      element?.forEach((element) {
        positions1.add(PointsData().initWith(element));
      });
    });
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as BleedingPoint;
    map["start_frame"] = self.startFrame;
    map["positions"] = self.positions.map((element) {
      return element.map((element) {
        return element.toMap();
      }).toList();
    }).toList();
    return map;
  }
} // _BleedingPoint

class _PointsData extends AppModel {
  @override
  AppModel extractClone() => PointsData();

  @override
  PointsData initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as PointsData;
    self.x = map["x"]?.toDouble() ?? 0.0;
    self.y = map["y"]?.toDouble() ?? 0.0;
    self.frame = map["frame"]?.toInt() ?? 0;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as PointsData;
    map["x"] = self.x;
    map["y"] = self.y;
    map["frame"] = self.frame;
    return map;
  }
} // _PointsData

class _EventOperationListInfo extends AppModel {
  @override
  AppModel extractClone() => EventOperationListInfo();

  @override
  EventOperationListInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as EventOperationListInfo;
    final datas0 = <EventOperationInfo>[];
    self.datas = datas0;
    map["datas"]?.forEach((element) {
      datas0.add(EventOperationInfo().initWith(element));
    });
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as EventOperationListInfo;
    map["datas"] = self.datas.map((element) {
      return element.toMap();
    }).toList();
    return map;
  }
} // _EventOperationListInfo

class _EventOperationInfo extends AppModel {
  @override
  AppModel extractClone() => EventOperationInfo();

  @override
  EventOperationInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as EventOperationInfo;
    self.id = map["id"]?.toInt() ?? 0;
    self.duration = map["duration"]?.toDouble() ?? 0.0;
    self.isHappening = map["isHappening"] ?? false;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as EventOperationInfo;
    map["id"] = self.id;
    map["duration"] = self.duration;
    map["isHappening"] = self.isHappening;
    return map;
  }
} // _EventOperationInfo

class _EventAttrInfo extends AppModel {
  @override
  AppModel extractClone() => EventAttrInfo();

  @override
  EventAttrInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as EventAttrInfo;
    self.value = map["value"]?.toInt() ?? 0;
    self.name = map["name"] ?? "";
    self.label = map["label"] ?? "";
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as EventAttrInfo;
    map["value"] = self.value;
    map["name"] = self.name;
    map["label"] = self.label;
    return map;
  }
} // _EventAttrInfo

class _EventInsightListInfo extends AppModel {
  @override
  AppModel extractClone() => EventInsightListInfo();

  @override
  EventInsightListInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as EventInsightListInfo;
    final datas0 = <EventInsightInfo>[];
    self.datas = datas0;
    map["datas"]?.forEach((element) {
      datas0.add(EventInsightInfo().initWith(element));
    });
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as EventInsightListInfo;
    map["datas"] = self.datas.map((element) {
      return element.toMap();
    }).toList();
    return map;
  }
} // _EventInsightListInfo

class _EventInsightInfo extends AppModel {
  @override
  AppModel extractClone() => EventInsightInfo();

  @override
  EventInsightInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as EventInsightInfo;
    self.id = map["id"]?.toInt() ?? 0;
    self.count = map["count"]?.toInt() ?? 0;
    self.duration = map["duration"]?.toInt() ?? 0;
    self.isHappening = map["isHappening"] ?? false;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as EventInsightInfo;
    map["id"] = self.id;
    map["count"] = self.count;
    map["duration"] = self.duration;
    map["isHappening"] = self.isHappening;
    return map;
  }
} // _EventInsightInfo
