import 'package:app_foundation/app_foundation.dart';

@ModelDetector(nameType: NameType.camel)
class SpeakerMessage extends _SpeakerMessage {
  late final int userId;

  late final double volume;

  late final bool muted;

  bool get isSpeaking => volume >= 0.5;
}

@ModelDetector(nameType: NameType.camel)
class MicrophoneMessage extends _MicrophoneMessage {
  late final int userId;

  late final bool muted;
}

class _SpeakerMessage extends AppModel {
  @override
  AppModel extractClone() => SpeakerMessage();

  @override
  SpeakerMessage initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as SpeakerMessage;
    self.userId = map["userId"]?.toInt() ?? 0;
    self.volume = map["volume"]?.toDouble() ?? 0.0;
    self.muted = map["muted"] ?? false;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as SpeakerMessage;
    map["userId"] = self.userId;
    map["volume"] = self.volume;
    map["muted"] = self.muted;
    return map;
  }
} // _SpeakerMessage

class _MicrophoneMessage extends AppModel {
  @override
  AppModel extractClone() => MicrophoneMessage();

  @override
  MicrophoneMessage initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as MicrophoneMessage;
    self.userId = map["userId"]?.toInt() ?? 0;
    self.muted = map["muted"] ?? false;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as MicrophoneMessage;
    map["userId"] = self.userId;
    map["muted"] = self.muted;
    return map;
  }
} // _MicrophoneMessage
