import 'package:app_foundation/app_foundation.dart';

/// 文件同步总状态信息
@ModelDetector(nameType: NameType.camel)
class AllFileStatsInfo extends _AllFileStatsInfo {
  /// 进度
  late final double progress;

  /// 预估时间
  late final int predicatedUploadTime;

  /// 已上传数量
  late final int toBeUploadedFileCount;
}

class _AllFileStatsInfo extends AppModel {
  @override
  AppModel extractClone() => AllFileStatsInfo();

  @override
  AllFileStatsInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as AllFileStatsInfo;
    self.progress = map["progress"]?.toDouble() ?? 0.0;
    self.predicatedUploadTime = map["predicatedUploadTime"]?.toInt() ?? 0;
    self.toBeUploadedFileCount = map["toBeUploadedFileCount"]?.toInt() ?? 0;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as AllFileStatsInfo;
    map["progress"] = self.progress;
    map["predicatedUploadTime"] = self.predicatedUploadTime;
    map["toBeUploadedFileCount"] = self.toBeUploadedFileCount;
    return map;
  }
} // _AllFileStatsInfo
