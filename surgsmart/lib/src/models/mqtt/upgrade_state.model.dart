import 'package:app_foundation/app_foundation.dart';

/// 设备软件下载安装结果
@ModelDetector(nameType: NameType.camel)
class UpgradeStateInfo extends _UpgradeStateInfo {
  /// 更新结果（下载安装成功/失败）
  late final int code; //0成功
  late final String result;
}

class _UpgradeStateInfo extends AppModel {
  @override
  AppModel extractClone() => UpgradeStateInfo();

  @override
  UpgradeStateInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as UpgradeStateInfo;
    self.code = map["code"]?.toInt() ?? 0;
    self.result = map["result"] ?? "";
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as UpgradeStateInfo;
    map["code"] = self.code;
    map["result"] = self.result;
    return map;
  }
} // _UpgradeStateInfo
