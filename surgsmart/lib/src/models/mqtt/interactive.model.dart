// ignore_for_file: no_leading_underscores_for_local_identifiers

import 'dart:ui';

import 'package:app_foundation/app_foundation.dart';

class UserGraphic {
  int userId = 0;
  List<Graphic> visible = [];
  List<Graphic> invisible = [];
  Graphic? cursor;

  // for drawing only
  String? name;

  UserGraphic copyWith({
    int? userId,
    List<Graphic>? visible,
    List<Graphic>? invisible,
    Graphic? cursor,
    String? name,
  }) {
    return UserGraphic()
      ..userId = userId ?? this.userId
      ..visible = visible ?? this.visible
      ..invisible = invisible ?? this.invisible
      ..cursor = cursor ?? this.cursor
      ..name = name ?? this.name;
  }
}

// @ModelDetector()
class GraphicPoint extends _GraphicPoint {
  @FieldAttr(name: 'color')
  late final String _color;

  @FieldAttr(name: 'offset')
  late final List<String> _offset;

  @FieldAttr(name: 'width')
  late final String _width;

  Color get color =>
      Color(int.parse('FF${_color.replaceAll('#', '')}', radix: 16));

  Offset get offset =>
      _offset.isEmpty
          ? Offset.zero
          : Offset(double.parse(_offset.first), double.parse(_offset.last));

  double get strokeWidth => double.parse(_width);
}

// @ModelDetector()
class GraphicPath extends _GraphicPath {
  @FieldAttr(name: 'color')
  late final String _color;

  @FieldAttr(name: 'offsets')
  late final List<List<double>> offsets;

  @FieldAttr(name: 'width')
  late final double strokeWidth;

  Color get color =>
      Color(int.parse('FF${_color.replaceAll('#', '')}', radix: 16));
}

// @ModelDetector(nameType: NameType.camel)
class GraphicSet extends _GraphicSet {
  late final int userId;

  late final List<String> uuids;
}

enum GraphicGraph { hand, solid, track }

// @ModelDetector(nameType: NameType.camel)
class Graphic extends _Graphic {
  late final int userId;

  @FieldAttr(convertEnumByString: true)
  late final GraphicGraph graph;

  late final GraphicPoint point;

  late final GraphicPath path;

  late int timestamp;

  late final String uuid;

  @FieldIgnore()
  final List<GraphicPoint> points = [];

  Offset get offset => point.offset;

  @override
  Graphic initWith(Map<String, dynamic> map) {
    final self = super.initWith(map);
    if (path.offsets.isEmpty) {
      self.points.add(self.point);
    } else {
      self.points.addAll(
        path.offsets.map(
          (e) => GraphicPoint().initWith({
            "color": path._color,
            "width": path.strokeWidth.toString(),
            "offset": e.map((e) => e.toString()),
          }),
        ),
      );
    }
    return self;
  }
}

class _GraphicPoint extends AppModel {
  @override
  AppModel extractClone() => GraphicPoint();

  @override
  GraphicPoint initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as GraphicPoint;
    self._color = map["color"] ?? "";
    final _offset0 = <String>[];
    self._offset = _offset0;
    map["offset"]?.forEach((element) {
      _offset0.add(element);
    });
    self._width = map["width"] ?? "0.0";
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as GraphicPoint;
    map["color"] = self._color;
    map["offset"] =
        self._offset.map((element) {
          return element;
        }).toList();
    map["width"] = self._width;
    return map;
  }
} // _GraphicPoint

class _GraphicPath extends AppModel {
  @override
  AppModel extractClone() => GraphicPath();

  @override
  GraphicPath initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as GraphicPath;
    self._color = map["color"] ?? "";
    final offsets0 = <List<double>>[];
    self.offsets = offsets0;
    map["offsets"]?.forEach((element) {
      final offsets1 = <double>[];
      offsets0.add(offsets1);
      element?.forEach((element) {
        offsets1.add(element);
      });
    });
    self.strokeWidth = map["width"]?.toDouble() ?? 0.0;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as GraphicPath;
    map["color"] = self._color;
    map["offsets"] =
        self.offsets.map((element) {
          return element.map((element) {
            return element;
          }).toList();
        }).toList();
    map["width"] = self.strokeWidth;
    return map;
  }
} // _GraphicPath

class _GraphicSet extends AppModel {
  @override
  AppModel extractClone() => GraphicSet();

  @override
  GraphicSet initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as GraphicSet;
    self.userId = map["userId"] ?? 0;
    final uuids0 = <String>[];
    self.uuids = uuids0;
    map["uuids"]?.forEach((element) {
      uuids0.add(element);
    });
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as GraphicSet;
    map["userId"] = self.userId;
    map["uuids"] =
        self.uuids.map((element) {
          return element;
        }).toList();
    return map;
  }
} // _GraphicSet

class _Graphic extends AppModel {
  @override
  AppModel extractClone() => Graphic();

  @override
  Graphic initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as Graphic;
    self.userId = map["userId"] ?? 0;
    final graphIndexOrName =
        map["graph"]?.toString() ?? GraphicGraph.values.first.name;
    self.graph =
        GraphicGraph.values.where((e) => e.name == graphIndexOrName).first;
    self.point = GraphicPoint().initWith(
      map["point"] != null ? map["point"].cast<String, dynamic>() : {},
    );
    self.path = GraphicPath().initWith(
      map["path"] != null ? map["path"].cast<String, dynamic>() : {},
    );
    self.timestamp = map["timestamp"] ?? 0;
    self.uuid = map["uuid"] ?? "";
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as Graphic;
    map["userId"] = self.userId;
    map["graph"] = self.graph.name;
    map["point"] = self.point.toMap();
    map["path"] = self.path.toMap();
    map["timestamp"] = self.timestamp;
    map["uuid"] = self.uuid;
    return map;
  }
} // _Graphic
