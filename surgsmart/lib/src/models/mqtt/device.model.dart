import 'package:app_foundation/app_foundation.dart';

/// 设备控制消息
@ModelDetector()
class DeviceControlMessage extends _DeviceControlMessage {
  /// 授权 token
  late final String? token;
}

/// 设备开启直播消息
@ModelDetector()
class DeviceStartLiveMessage extends _DeviceStartLiveMessage {
  /// 授权 token
  late final String? token;

  /// 直播标题
  late final String? title;
}

@ModelDetector()
class DeviceSettingMessage extends _DeviceSettingMessage {
  /// 授权 token
  late final String? token;

  /// 是否允许/开启
  late final bool enabled;
}

/// 设备手术开始消息
@ModelDetector(nameType: NameType.camel)
class DeviceSurgeryStartMessage extends _DeviceSurgeryStartMessage {
  /// 授权 token
  late final String? token;

  /// 用户ID
  late final int userId;

  /// 科室名称
  late final String? department;

  /// 术式名称
  late final String procedureName;

  /// 住院号
  late final String? admissionNumber;

  /// 性别
  late final int? sex;

  /// 年龄
  late final int? age;

  /// 是否允许高级 AI 功能
  late final bool? enableAdvanceAi;
}

class _DeviceControlMessage extends AppModel {
  @override
  AppModel extractClone() => DeviceControlMessage();

  @override
  DeviceControlMessage initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as DeviceControlMessage;
    self.token = map["token"];
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as DeviceControlMessage;
    map["token"] = self.token;
    return map;
  }
} // _DeviceControlMessage

class _DeviceStartLiveMessage extends AppModel {
  @override
  AppModel extractClone() => DeviceStartLiveMessage();

  @override
  DeviceStartLiveMessage initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as DeviceStartLiveMessage;
    self.token = map["token"];
    self.title = map["title"];
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as DeviceStartLiveMessage;
    map["token"] = self.token;
    map["title"] = self.title;
    return map;
  }
} // _DeviceStartLiveMessage

class _DeviceSettingMessage extends AppModel {
  @override
  AppModel extractClone() => DeviceSettingMessage();

  @override
  DeviceSettingMessage initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as DeviceSettingMessage;
    self.token = map["token"];
    self.enabled = map["enabled"] ?? false;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as DeviceSettingMessage;
    map["token"] = self.token;
    map["enabled"] = self.enabled;
    return map;
  }
} // _DeviceSettingMessage

class _DeviceSurgeryStartMessage extends AppModel {
  @override
  AppModel extractClone() => DeviceSurgeryStartMessage();

  @override
  DeviceSurgeryStartMessage initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as DeviceSurgeryStartMessage;
    self.token = map["token"];
    self.userId = map["userId"]?.toInt() ?? 0;
    self.department = map["department"];
    self.procedureName = map["procedureName"] ?? "";
    self.admissionNumber = map["admissionNumber"];
    self.sex = map["sex"]?.toInt();
    self.age = map["age"]?.toInt();
    self.enableAdvanceAi = map["enableAdvanceAi"];
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as DeviceSurgeryStartMessage;
    map["token"] = self.token;
    map["userId"] = self.userId;
    map["department"] = self.department;
    map["procedureName"] = self.procedureName;
    map["admissionNumber"] = self.admissionNumber;
    map["sex"] = self.sex;
    map["age"] = self.age;
    map["enableAdvanceAi"] = self.enableAdvanceAi;
    return map;
  }
} // _DeviceSurgeryStartMessage
