import 'package:app_foundation/app_foundation.dart';

/// 设备软件升级信息
@ModelDetector(nameType: NameType.camel)
class DeviceUpgradeProgressInfo extends _DeviceUpgradeProgressInfo {
  /// 进度
  late final double progress;

  /// 剩余时间
  late final int remainingTime;
}

class _DeviceUpgradeProgressInfo extends AppModel {
  @override
  AppModel extractClone() => DeviceUpgradeProgressInfo();

  @override
  DeviceUpgradeProgressInfo initWith(Map<String, dynamic> map) {
    final self = super.initWith(map) as DeviceUpgradeProgressInfo;
    self.progress = map["progress"]?.toDouble() ?? 0.0;
    self.remainingTime = map["remainingTime"]?.toInt() ?? 0;
    return self;
  }

  @override
  Map<String, dynamic> toMap() {
    final map = super.toMap();
    final self = this as DeviceUpgradeProgressInfo;
    map["progress"] = self.progress;
    map["remainingTime"] = self.remainingTime;
    return map;
  }
} // _DeviceUpgradeProgressInfo
