/// todo 需将外部使用的index改为value,否则误修改顺序会导致消息发送出错
enum ClientMsgType {
  //mtu大小通知
  mtuNotify(0),
  //设备授权信息请求
  authLoginRequest(1),
  //重启设备
  restartDevice(2),
  //关闭设备
  shutdownDevice(3),
  //重启应用
  restartApp(4),
  //开启手术
  surgeryStart(5),
  //停止手术
  surgeryStop(6),
  //开启直播
  liveStart(7),
  //停止直播
  liveStop(8),
  //睿标记
  markTime(9),
  //设置麦克风
  microphoneState(10),
  //体腔外模糊
  bodyoutMask(11),
  //设备设置获取
  settingRead(12),
  //手术信息读取
  surgeryRead(13),
  //下发类型
  //App状态
  appStatus(14),
  //手术状态
  surgeryStatus(15),
  //设备控制状态
  controlStatus(16),
  /* ble新增状态 */
  //主机断开
  m3Disconnect(17),
  //存储空间已满
  diskFull(18),
  //网络连接状态
  networkState(19),
  //手术开始时间
  surgeryTime(20),
  //协同人员麦克风
  synergyMicrophoneState(21),
  //请求m3断开：22
  requestM3Disconnect(22);

  final int value;
  const ClientMsgType(this.value);
}
