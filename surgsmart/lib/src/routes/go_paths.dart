import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/widgets.dart';

/// 参照 defaultSlide, 去实现不同的转场动画
class RouteTransition {
  static Widget defaultSlide(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    const begin = Offset(1.0, 0.0);
    const end = Offset.zero;
    final tween = Tween(begin: begin, end: end);
    final offsetAnimation = animation.drive(tween);
    return SlideTransition(position: offsetAnimation, child: child);
  }
}

/// 参照文档要求, 实现路由描述
@routeDetector
class GoPaths {
  @AppRoute(
    module: "home",
    stateManageType: StateManageType.static,
    description: "首页",
  )
  static const home = '/';

  @AppRoute(
    module: "setting",
    stateManageType: StateManageType.static,
    description: "系统设置",
    subViews: [
      AppRouteSubView(
        name: "record",
        stateManageType: StateManageType.static,
        description: "录制设置",
      ),
      AppRouteSubView(
        name: "network",
        stateManageType: StateManageType.custom,
        description: "网络设置",
      ),
      AppRouteSubView(
        name: "version",
        stateManageType: StateManageType.custom,
        description: "系统版本",
      ),
      AppRouteSubView(
        name: "service",
        stateManageType: StateManageType.custom,
        description: "域名设置",
      ),
    ],
  )
  static const setting = '/setting';

  @AppRoute(
    module: "file",
    stateManageType: StateManageType.custom,
    description: "文件",
  )
  static const file = '/file-list';

  @AppRoute(
    module: "surgery",
    stateManageType: StateManageType.custom,
    description: "手术",
    subViews: [
      AppRouteSubView(
        name: "operation",
        stateManageType: StateManageType.static,
        description: "功能操作面板",
      ),
      AppRouteSubView(
        name: "algorithm",
        stateManageType: StateManageType.custom,
        description: "算法数据面板",
      ),
      AppRouteSubView(
        name: "tracking",
        stateManageType: StateManageType.static,
        description: "解剖结构追踪",
      ),
    ],
  )
  static const surgeryRoom = '/surgery-room';

  @AppRoute(
    module: "surgery",
    stateManageType: StateManageType.custom,
    description: "手术创建",
  )
  static const surgeryCreate = '/surgery-create';

  @AppRoute(
    module: "report",
    stateManageType: StateManageType.custom,
    description: "手术报告",
  )
  static const surgeryReport = '/surgery-report';

  @AppRoute(
    module: "system_update",
    stateManageType: StateManageType.custom,
    description: "系统更新",
  )
  static const systemUpdate = '/system-update';

  @AppRoute(
    module: "link_tutorial",
    stateManageType: StateManageType.static,
    description: "连接教程",
  )
  static const linkTutorial = '/link-tutorial';

  @AppRoute(
    module: "service_setting",
    stateManageType: StateManageType.static,
    description: "域名网络服务设置",
  )
  static const serviceSetting = '/service-setting';

  @AppRoute(
    module: "expand_screen",
    stateManageType: StateManageType.custom,
    description: "扩展屏幕显示",
  )
  static const expandScreen = '/expand-screen';
}
