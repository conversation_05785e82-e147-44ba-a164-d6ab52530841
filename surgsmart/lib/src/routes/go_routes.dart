// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// RouterGenerator
// **************************************************************************

import 'package:go_router/go_router.dart';

import 'go_paths.dart';

// 🏷️ module =====> home
import '../modules/home/<USER>/home.controller.dart';
import '../modules/home/<USER>/home.view.dart';

// 🏷️ module =====> setting
import '../modules/setting/setting/setting.controller.dart';
import '../modules/setting/setting/setting.view.dart';

// 🏷️ module =====> file
import '../modules/file/file/file.controller.dart';
import '../modules/file/file/file.view.dart';

// 🏷️ module =====> surgery
import '../modules/surgery/surgery_room/surgery_room.controller.dart';
import '../modules/surgery/surgery_room/surgery_room.view.dart';
import '../modules/surgery/surgery_create/surgery_create.controller.dart';
import '../modules/surgery/surgery_create/surgery_create.view.dart';

// 🏷️ module =====> report
import '../modules/report/surgery_report/surgery_report.controller.dart';
import '../modules/report/surgery_report/surgery_report.view.dart';

// 🏷️ module =====> system_update
import '../modules/system_update/system_update/system_update.controller.dart';
import '../modules/system_update/system_update/system_update.view.dart';

// 🏷️ module =====> link_tutorial
import '../modules/link_tutorial/link_tutorial/link_tutorial.controller.dart';
import '../modules/link_tutorial/link_tutorial/link_tutorial.view.dart';

// 🏷️ module =====> service_setting
import '../modules/service_setting/service_setting/service_setting.controller.dart';
import '../modules/service_setting/service_setting/service_setting.view.dart';

// 🏷️ module =====> expand_screen
import '../modules/expand_screen/expand_screen/expand_screen.controller.dart';
import '../modules/expand_screen/expand_screen/expand_screen.view.dart';

/// APP 路由列表
final List<GoRoute> routes = [
  // 🏷️ module =====> home
  GoRoute(
    name: GoPaths.home,
    path: GoPaths.home,
    builder: (context, state) => HomeView(
      key: state.pageKey,
      binding: (key) => HomeController(key, state),
    ),
  ),

  // 🏷️ module =====> setting
  GoRoute(
    name: GoPaths.setting,
    path: GoPaths.setting,
    builder: (context, state) => SettingView(
      key: state.pageKey,
      binding: (key) => SettingController(key, state),
    ),
  ),

  // 🏷️ module =====> file
  GoRoute(
    name: GoPaths.file,
    path: GoPaths.file,
    builder: (context, state) => FileView(
      key: state.pageKey,
      binding: (key) => FileController(key, state),
    ),
  ),

  // 🏷️ module =====> surgery
  GoRoute(
    name: GoPaths.surgeryRoom,
    path: GoPaths.surgeryRoom,
    builder: (context, state) => SurgeryRoomView(
      key: state.pageKey,
      binding: (key) => SurgeryRoomController(key, state),
    ),
  ),
  GoRoute(
    name: GoPaths.surgeryCreate,
    path: GoPaths.surgeryCreate,
    builder: (context, state) => SurgeryCreateView(
      key: state.pageKey,
      binding: (key) => SurgeryCreateController(key, state),
    ),
  ),

  // 🏷️ module =====> report
  GoRoute(
    name: GoPaths.surgeryReport,
    path: GoPaths.surgeryReport,
    builder: (context, state) => SurgeryReportView(
      key: state.pageKey,
      binding: (key) => SurgeryReportController(key, state),
    ),
  ),

  // 🏷️ module =====> system_update
  GoRoute(
    name: GoPaths.systemUpdate,
    path: GoPaths.systemUpdate,
    builder: (context, state) => SystemUpdateView(
      key: state.pageKey,
      binding: (key) => SystemUpdateController(key, state),
    ),
  ),

  // 🏷️ module =====> link_tutorial
  GoRoute(
    name: GoPaths.linkTutorial,
    path: GoPaths.linkTutorial,
    builder: (context, state) => LinkTutorialView(
      key: state.pageKey,
      binding: (key) => LinkTutorialController(key, state),
    ),
  ),

  // 🏷️ module =====> service_setting
  GoRoute(
    name: GoPaths.serviceSetting,
    path: GoPaths.serviceSetting,
    builder: (context, state) => ServiceSettingView(
      key: state.pageKey,
      binding: (key) => ServiceSettingController(key, state),
    ),
  ),

  // 🏷️ module =====> expand_screen
  GoRoute(
    name: GoPaths.expandScreen,
    path: GoPaths.expandScreen,
    builder: (context, state) => ExpandScreenView(
      key: state.pageKey,
      binding: (key) => ExpandScreenController(key, state),
    ),
  ),
];
