// import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TrackingCard extends StatelessWidget {
  /// 标题
  final String title;

  /// 标志颜色
  final Color color;

  /// 背景颜色
  final Color bgColor;

  /// 默认背景色
  final Color defaultBgColor;

  /// 是否激活
  final bool isActive;

  const TrackingCard({
    super.key,
    required this.title,
    required this.color,
    required this.bgColor,
    this.defaultBgColor = Colors.transparent,
    required this.isActive,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(52.r),
      decoration: BoxDecoration(
        color: isActive ? bgColor : defaultBgColor,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        children: [
          Container(
            width: 72.r,
            height: 72.r,
            decoration: BoxDecoration(
              color: isActive ? color : color.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(36.r),
            ),
          ),
          // isActive
          //     ? DottedBorder(
          //       options: CircularDottedBorderOptions(
          //         dashPattern: [10, 5],
          //         strokeWidth: 2,
          //         padding: EdgeInsets.all(5.r),
          //         color: Colors.white,
          //       ),
          //       child: Container(
          //         width: 60.r,
          //         height: 60.r,
          //         decoration: BoxDecoration(
          //           color: color,
          //           borderRadius: BorderRadius.circular(30.r),
          //         ),
          //       ),
          //     )
          //     : Container(
          //       width: 72.r,
          //       height: 72.r,
          //       decoration: BoxDecoration(
          //         color: color.withValues(alpha: 0.5),
          //         borderRadius: BorderRadius.circular(36.r),
          //       ),
          //     ),
          SizedBox(width: 52.w),
          Text(
            title,
            style: TextStyle(
              fontSize: 60.sp,
              fontWeight: FontWeight.w500,
              color:
                  isActive ? Colors.white : Colors.white.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }
}
