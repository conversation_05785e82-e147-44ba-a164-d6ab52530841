import 'package:app_foundation/app_foundation.dart';
import 'package:blur/blur.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/surgery_room.controller.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/operation/widgets/operation_dropdown.widget.dart';
import 'package:surgsmart/src/widgets/app_background.widget.dart';

import 'tracking.controller.dart';
import 'widgets/tracking_card.widget.dart';

/// 所属模块: surgery
///
/// 所属路由: surgeryRoom
///
/// 解剖结构追踪
class TrackingView extends AppView<TrackingController> {
  const TrackingView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    // 注: 如果 AppBar 与数据有关联, 则把 controller.build() 提升为顶级节点
    return Scaffold(
      backgroundColor: Colors.black,
      body: controller.build(
        onSuccess: onSuccess,
        onFailure: onFailure,
        onLoading: onLoading,
        onEmpty: onEmpty,
      ),
    );
  }

  Widget onSuccess(BuildContext context, ApiModel value) {
    return Padding(
      padding: EdgeInsets.all(24.r),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          AppBackground(
            width: 941.w,
            radius: 30.r,
            padding: EdgeInsets.only(left: 40.w, right: 40.w),
            child: ValueListenableBuilder(
              valueListenable:
                  controller.parent<SurgeryRoomController>()!.largeScreenEnable,
              builder: (context, largeScreenEnable, child) {
                return ValueListenableBuilder(
                  valueListenable: controller.sync2largeScreen,
                  builder: (context, sync2largeScreen, child) {
                    return Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        32.verticalSpace,
                        Row(
                          children: [
                            Image(
                              image: R.image.logo_variant(),
                              width: 330.w,
                              height: 77.h,
                            ),
                          ],
                        ),
                        64.verticalSpace,
                        OperationDropdown(
                          title: '大屏同步展示',
                          switchState:
                              largeScreenEnable
                                  ? sync2largeScreen
                                  : largeScreenEnable,
                          switchChange: (val) {
                            controller.sync2largeScreen.value = val;
                          },
                          switchEnable: largeScreenEnable,
                        ),
                        largeScreenEnable
                            ? Padding(
                              padding: EdgeInsets.only(top: 84.h),
                              child: Image(
                                width: 450.w,
                                height: 282.h,
                                image:
                                    controller.sync2largeScreen.value
                                        ? R.image.screen_displayed()
                                        : R.image.screen_displayable(),
                              ),
                            )
                            : Padding(
                              padding: EdgeInsets.only(top: 148.h),
                              child: Text(
                                '未连接到显示设备',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 56.sp,
                                ),
                              ),
                            ),
                        Spacer(),
                        Button(
                          onPressed: () {
                            controller
                                .parent<SurgeryRoomController>()
                                ?.trackingExit();
                          },
                          label: Container(
                            width: 464.w,
                            padding: EdgeInsets.symmetric(
                              horizontal: 104.w,
                              vertical: 16.h,
                            ),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(55.r),
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  const Color(0xB25CF5EB),
                                  const Color(0xB256B3E3),
                                ],
                              ),
                            ),
                            child: Center(
                              child: Text(
                                S.current.f_pFbaCUzb,
                                style: TextStyle(
                                  fontSize: 56.sp,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ),
                        64.verticalSpace,
                      ],
                    );
                  },
                );
              },
            ),
          ),
          Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 20.w),
              height: double.infinity,
              child: Stack(
                children: [
                  AspectRatio(
                    aspectRatio: 16 / 9,
                    child: Texture(textureId: controller.textureId),
                  ),
                  Positioned(
                    left: 20,
                    top: 20,
                    child: Text(
                      "解剖结构追踪",
                      style: TextStyle(
                        fontSize: 52.sp,
                        height: 67 / 52,
                        fontWeight: FontWeight.w500,
                      ),
                    ).frosted(
                      frostColor: const Color(0xFF777777),
                      blur: 10,
                      borderRadius: BorderRadius.circular(10.r),
                      padding: EdgeInsets.symmetric(
                        vertical: 30.h,
                        horizontal: 42.w,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          AppBackground(
            width: 941.w,
            radius: 30.r,
            padding: EdgeInsets.only(left: 34.w, right: 34.w),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                SizedBox(height: 30.h),
                Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image(width: 80.r, height: 80.r, image: R.image.tracking()),
                    SizedBox(width: 10.w),
                    Text(
                      "解剖结构追踪",
                      style: TextStyle(
                        fontSize: 80.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 80.h),
                ValueListenableBuilder(
                  valueListenable: controller.arteryTracing,
                  builder: (context, value, child) {
                    return TrackingCard(
                      title: "动脉走形识别",
                      color: const Color(0xFF1AFF2B),
                      bgColor: const Color(0xFF1AFF2B).withValues(alpha: 0.25),
                      isActive: value > 0,
                    );
                  },
                ),
                SizedBox(height: 64.h),
                ValueListenableBuilder(
                  valueListenable: controller.arteryIdentification,
                  builder: (context, value, child) {
                    return TrackingCard(
                      title: "动脉识别",
                      color: const Color(0xFFFF4D4F),
                      bgColor: const Color(0xFFFF4D4F).withValues(alpha: 0.25),
                      isActive: value > 0,
                    );
                  },
                ),
                SizedBox(height: 64.h),
                ValueListenableBuilder(
                  valueListenable: controller.veinIdentification,
                  builder: (context, value, child) {
                    return TrackingCard(
                      title: "静脉识别",
                      color: const Color(0xFF42A1FF),
                      bgColor: const Color(0xFF42A1FF).withValues(alpha: 0.25),
                      isActive: value > 0,
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }
}
