import 'dart:async';
import 'dart:ffi';

import 'package:app_foundation/app_foundation.dart';
import 'package:medias_kit/core/medias_kit_ffi.dart' as ffi;
import 'package:medias_kit/medias_kit.dart';
import 'package:surgsmart/src/modules/expand_screen/expand_screen/expand_screen.controller.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/surgery_room.controller.dart';
import 'package:surgsmart/src/tools/multi_window.dart';

/// 所属模块: surgery
///
/// 所属路由: surgeryRoom
///
/// 解剖结构追踪
class TrackingController extends AppController with StateMixin<ApiModel> {
  TrackingController(super.key, super.routerState);

  /// 解剖结构追踪大屏同步
  final sync2largeScreen = true.notifier;

  /// 动脉走形识别
  final arteryTracing = 0.notifier;

  /// 动脉识别
  final arteryIdentification = 0.notifier;

  /// 静脉识别
  final veinIdentification = 0.notifier;

  /// 解剖结构追踪事件
  final eventFlags = 0.notifier;

  late final renderRef = ffi.textureRendererCreate(
    MediasKit.pluginId.toNativeCharPointer(this),
  );

  int get textureId => ffi.textureRendererGetTextureId(renderRef);

  @override
  void onInit() {
    update(LoadState.success(ApiModel()));
  }

  @override
  void onReady() {
    final srVC = parent<SurgeryRoomController>();
    srVC?.maskIpc.addRenderer("main", renderRef);
    srVC?.maskIpc.onFrameDataChanged = (tissueIds, eventFlags) {
      // 理论值: 30fps下, 200ms 延迟关闭
      arteryTracing.value =
          tissueIds.contains(3) ? 200 : arteryTracing.value - 33;
      arteryIdentification.value =
          tissueIds.contains(1) ? 200 : arteryIdentification.value - 33;
      veinIdentification.value =
          tissueIds.contains(2) ? 200 : veinIdentification.value - 33;
      this.eventFlags.value = eventFlags;
      sendTrackingMessageToWindows();
    };

    sendTrackingMessageToWindows();
    sync2largeScreen.addListener(sendTrackingMessageToWindows);
    arteryTracing.addListener(sendTrackingMessageToWindows);
    arteryIdentification.addListener(sendTrackingMessageToWindows);
    veinIdentification.addListener(sendTrackingMessageToWindows);
  }

  @override
  void onClose() {
    final srVC = parent<SurgeryRoomController>();
    srVC?.maskIpc.clearRenderers();
    srVC?.maskIpc.onFrameDataChanged = null;

    sendTrackingMessageToWindows(true);
    sync2largeScreen.removeListener(sendTrackingMessageToWindows);
    arteryTracing.removeListener(sendTrackingMessageToWindows);
    arteryIdentification.removeListener(sendTrackingMessageToWindows);
    veinIdentification.removeListener(sendTrackingMessageToWindows);

    Future.delayed(const Duration(seconds: 1), () {
      ffi.textureRendererDestroy(renderRef);
    });
  }

  void sendTrackingMessageToWindows([forceExit = false]) async {
    if (!parent<SurgeryRoomController>()!.largeScreenEnable.value) {
      return;
    }

    final msg = {
      "arteryTracing": arteryTracing.value > 0,
      "arteryIdentification": arteryIdentification.value > 0,
      "veinIdentification": veinIdentification.value > 0,
      "onlyEventFlags": !sync2largeScreen.value || forceExit,
      "eventFlags": eventFlags.value,
    };

    final address = await MultiWindow.sendAllWindowMsg(
      method: ExpandScreenController.trackingInfoTag,
      arguments: msg,
    );

    final srVC = parent<SurgeryRoomController>();
    for (var entry in address.entries) {
      Map<String, int> renders = entry.value.cast<String, int>();
      srVC?.maskIpc.addRenderer(
        entry.key,
        Pointer.fromAddress(renders['trackingRender']!),
      );
      srVC?.modelVideoController.addRender(
        render: renders['modelRender']!,
        renderFunction: ffi.addresses.textureRendererRenderRgbaFrame.address,
      );
    }
    srVC?.updateEventFlags(eventFlags.value);
  }
}
