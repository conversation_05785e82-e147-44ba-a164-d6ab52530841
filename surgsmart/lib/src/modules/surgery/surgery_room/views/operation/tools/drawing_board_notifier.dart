import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/models/mqtt/interactive.model.dart';

class DrawingBoardNotifier {
  static Future<void> drawGraphic(
    UserGraphic userGraphic,
    Graphic graphic,
    Graphic? cursor,
  ) async {
    final graphics = userGraphic.visible;
    // ai tracking graphics
    if (graphic.graph == GraphicGraph.track) {
      if (graphics.isNotEmpty) {
        bool contains = false;
        List<Graphic> newGraphics =
            graphics.map((oldGraphic) {
              if (oldGraphic.uuid == graphic.uuid) {
                contains = true;
                return graphic;
              } else {
                return oldGraphic;
              }
            }).toList();
        if (contains == false) {
          newGraphics.add(graphic);
        }
        userGraphic.visible = [...newGraphics];
      } else {
        graphics.add(graphic);
        userGraphic.visible = [...graphics];
      }
    } else {
      // whiteboard graphics
      if (graphics.isNotEmpty && graphics.last.uuid == graphic.uuid) {
        graphics.last.timestamp = graphic.timestamp;
        graphics.last.points.addAll(graphic.points);
      } else {
        graphics.add(graphic);
      }
      userGraphic.visible = [...graphics];
    }
    userGraphic.cursor = cursor;
  }

  static Future<void> clearGraphics(
    UserGraphic userGraphic, {
    List<String>? uuids,
  }) async {
    final graphics = userGraphic.visible;
    uuids = uuids ?? graphics.map((e) => e.uuid).toList();
    for (var uuid in uuids) {
      final graphic =
          graphics.where((graphic) => graphic.uuid == uuid).firstOrNull;
      if (graphic == null) return;
      graphics.remove(graphic);
      userGraphic.invisible.add(graphic);
    }
  }

  static Future<void> drawCursor(
    UserGraphic userGraphic,
    Graphic cursor,
  ) async {
    userGraphic.cursor = cursor;
  }

  static Future<void> redo(UserGraphic userGraphic, List<String> uuids) async {
    final graphics = userGraphic.invisible;
    final now = DateTime.now();
    for (var uuid in uuids) {
      final graphic =
          graphics.where((element) => element.uuid == uuid).firstOrNull;
      if (graphic == null) return;
      graphics.remove(graphic);
      graphic.timestamp = now.millisecondsSinceEpoch;
      userGraphic.visible.add(graphic);
    }
  }

  static Future<void> undo(UserGraphic userGraphic, List<String> uuids) async {
    final graphics = userGraphic.visible;
    for (var uuid in uuids) {
      final graphic =
          graphics.where((element) => element.uuid == uuid).firstOrNull;
      if (graphic == null) return;
      graphics.remove(graphic);
      userGraphic.invisible.add(graphic);
    }
  }

  static Future<void> removeGraphic(
    UserGraphic userGraphic,
    DateTime now,
  ) async {
    final graphics = userGraphic.visible;
    graphics.removeWhere((graphic) {
      final renderedAt = DateTime.fromMillisecondsSinceEpoch(graphic.timestamp);

      if (now.difference(renderedAt).inSeconds.abs() >= 3) {
        app.logD(
          'Graphic [${graphic.uuid}] for [${userGraphic.userId}] dismissed',
        );
        return true;
      }
      return false;
    });
    final cursor = userGraphic.cursor;
    if (cursor != null) {
      final renderedAt = DateTime.fromMillisecondsSinceEpoch(cursor.timestamp);
      if (now.difference(renderedAt).inSeconds.abs() >= 5) {
        userGraphic.cursor = null;
        app.logD('Graphic cursor indicate [${userGraphic.userId}] dismissed');
      }
    }
  }
}
