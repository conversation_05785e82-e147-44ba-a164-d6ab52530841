import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ControlStrip extends StatefulWidget {
  final String title;
  final ImageProvider activeImage;
  final ImageProvider? inactiveImage;
  final double value;
  final double min;
  final double max;
  final Color activeColor;
  final Color inactiveColor;
  final void Function(double value) slidingEnd;
  final void Function(double value)? slidingChange;

  const ControlStrip({
    super.key,
    this.title = '',
    required this.activeImage,
    this.inactiveImage,
    required this.value,
    required this.slidingEnd,
    this.slidingChange,
    this.min = 0,
    this.max = 100,
    this.activeColor = const Color(0xFF0EC6D2),
    this.inactiveColor = const Color(0x26FFFFFF),
  });

  @override
  State<ControlStrip> createState() => _ControlStripState();
}

class _ControlStripState extends State<ControlStrip> {
  int lastSlidingChangeTime = 0;

  late double displayValue = widget.value;

  double? eventValue;

  Timer? timer;

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant ControlStrip oldWidget) {
    if (oldWidget.value != widget.value) {
      setState(() {
        displayValue = widget.value;
      });
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return widget.title.isNotEmpty
        ? Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.title,
                style: TextStyle(fontSize: 40.sp, color: Color(0xCCFFFFFF)),
              ),
              50.verticalSpace,
              stripWidget(),
            ],
          )
        : stripWidget();
  }

  Widget stripWidget() {
    return Row(
      children: [
        Image(
          image: (displayValue == 0 && widget.inactiveImage != null)
              ? widget.inactiveImage!
              : widget.activeImage,
          width: 64.r,
          height: 64.r,
        ),
        SizedBox(width: 10.w),
        Expanded(
          child: SliderTheme(
            data: SliderTheme.of(context).copyWith(
              trackHeight: 20.h,
              thumbShape: RoundSliderThumbShape(enabledThumbRadius: 30.r),
              valueIndicatorTextStyle: TextStyle(
                fontSize: 50.sp,
              ),
              //valueIndicatorShape: CustomSliderValueIndicatorShape(),
            ),
            child: Slider(
              value: displayValue,
              min: widget.min,
              max: widget.max,
              activeColor: widget.activeColor,
              inactiveColor: widget.inactiveColor,
              // divisions: widget.max.toInt(),
              // label: _value.toInt().toString(),
              onChangeStart: (value) {
                timer = Timer.periodic(
                  const Duration(milliseconds: 300),
                  (timer) {
                    if (eventValue != null) {
                      widget.slidingChange?.call(eventValue!);
                      eventValue = null;
                    }
                  },
                );
              },
              onChanged: (value) {
                setState(() {
                  displayValue = value;
                  eventValue = value;
                });
              },
              onChangeEnd: (value) {
                timer?.cancel();
                timer = null;
                widget.slidingEnd(value);
              },
            ),
          ),
        )
      ],
    );
  }
}

class CustomSliderValueIndicatorShape extends SliderComponentShape {
  final double indicatorHeight;
  final double indicatorWidth;

  CustomSliderValueIndicatorShape({
    this.indicatorHeight = 60.0,
    this.indicatorWidth = 100.0,
  });

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size(indicatorWidth, indicatorHeight);
  }

  @override
  void paint(
    PaintingContext context,
    Offset center, {
    required Animation<double> activationAnimation,
    required Animation<double> enableAnimation,
    required bool isDiscrete,
    required TextPainter labelPainter,
    required RenderBox parentBox,
    required SliderThemeData sliderTheme,
    required TextDirection textDirection,
    required double value,
    required double textScaleFactor,
    required Size sizeWithOverflow,
  }) {
    final double valueIndicatorHeight = indicatorHeight;
    final double valueIndicatorWidth = indicatorWidth;
    final Offset offset = Offset(
      center.dx - valueIndicatorWidth / 2,
      center.dy - valueIndicatorHeight - indicatorHeight,
    );

    final Rect rect = Rect.fromLTWH(
      offset.dx,
      offset.dy,
      valueIndicatorWidth,
      valueIndicatorHeight,
    );

    final RRect rRect = RRect.fromRectAndRadius(
      rect,
      Radius.circular(indicatorHeight),
    );

    context.canvas.drawRRect(
      rRect,
      Paint()..color = sliderTheme.valueIndicatorColor ?? Colors.blue,
    );

    labelPainter.paint(
      context.canvas,
      offset +
          Offset((valueIndicatorWidth - labelPainter.width) / 2,
              (valueIndicatorHeight - labelPainter.height) / 1.2),
    );
  }
}
