import 'dart:async';
import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/apis/app_domain.dart';
import 'package:surgsmart/src/apis/http/live.api.dart';
import 'package:surgsmart/src/apis/http/room.api.dart';
import 'package:surgsmart/src/apis/mqtt/device_control.api.dart';
import 'package:surgsmart/src/apis/mqtt/rtc_live.api.dart';
import 'package:surgsmart/src/models/event_bus.model.dart';
import 'package:surgsmart/src/models/http/doctor.model.dart';
import 'package:surgsmart/src/models/http/live.model.dart';
import 'package:surgsmart/src/models/http/room.model.dart';
import 'package:surgsmart/src/models/mqtt/device.model.dart';
import 'package:surgsmart/src/models/mqtt/rtc_live.model.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/surgery_room.controller.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/operation/widgets/function_card.widget.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/operation/widgets/operation_menu.widget.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/tools/event_bus.dart';

/// 所属模块: surgery
///
/// 所属路由: surgeryRoom
///
/// 功能操作面板
class OperationController extends AppController with StateMixin<ApiModel> {
  OperationController(super.key, super.routerState);

  final operationState = OperationState.function.notifier;
  final roomShareKey = "".emptyNotifier;
  final liveShareKey = "".emptyNotifier;

  /// 协同获取列表timer,mqtt断开延时高的情况容错
  Timer? doctorsCheckTimer;

  MqttApiObserver? onAuthLoginObserver;
  MqttApiObserver? onSurgeryStopObserver;
  MqttApiObserver? onLiveStartObserver;
  MqttApiObserver? onLiveStopObserver;
  MqttApiObserver? onMarkTimeObserver;
  MqttApiObserver? onMicrophoneSettingObserver;
  MqttApiObserver? onBodyoutMaskSettingObserver;
  MqttApiObserver? onSettingReadObserver;
  MqttApiObserver? onSpeakerObserver;
  MqttApiObserver? onMicrophoneObserver;
  StreamSubscription? roomStateObserver;
  StreamSubscription? liveStateObserver;

  @override
  void onInit() {
    update(LoadState.success(ApiModel()));
  }

  void doctorsCheckRefresh() {
    if (operationState.value == OperationState.rtcLive) {
      doctorsCheckTimer?.cancel();
      doctorsCheckTimer = null;
      doctorsCheckTimer = Timer.periodic(const Duration(seconds: 10), (
        timer,
      ) async {
        // 刷新用户列表
        int userCount =
            parent<SurgeryRoomController>()?.doctors.value.length ?? 0;
        List<DoctorInfo> doctors =
            (await HttpRoomApi<DoctorListInfo>.users(
                  deviceId: AppContext.share.authorizeInfo.deviceId,
                ).request())
                .datas;
        parent<SurgeryRoomController>()?.doctors.value = doctors;

        if (userCount == 0 && doctors.isNotEmpty) {
          parent<SurgeryRoomController>()?.playAudio(
            fileName: "enter_meeting.wav.txt",
          );
        }
      });
    } else {
      doctorsCheckTimer?.cancel();
      doctorsCheckTimer = null;
    }
  }

  @override
  Future<void> onReady() async {
    ///订阅Room 业务事件
    subscriptRoomEventBus();
    if (AppContext.share.enableBleControl) {
      ///监听蓝牙消息
      addBleControlMessage();
    } else {
      /// 订阅远端遥控器 MQTT 消息
      subscriptRemoteControlMessage();
    }

    /// 订阅设备网络状态
    subscriptNetworkStatus();

    AppContext.share.networkConnected.addListener(subscriptNetworkStatus);

    final doctors = parent<SurgeryRoomController>()?.doctors;
    doctorCount = doctors?.value.length ?? 0;
    doctors?.addListener(updateOperationState);
    operationState.addListener(doctorsCheckRefresh);
  }

  @override
  void onClose() {
    AppContext.share.networkConnected.removeListener(subscriptNetworkStatus);
    operationState.removeListener(doctorsCheckRefresh);
    doctorsCheckTimer?.cancel();

    onAuthLoginObserver?.cancel();
    onSurgeryStopObserver?.cancel();
    onLiveStartObserver?.cancel();
    onLiveStopObserver?.cancel();
    onMarkTimeObserver?.cancel();
    onMicrophoneSettingObserver?.cancel();
    onBodyoutMaskSettingObserver?.cancel();
    onSettingReadObserver?.cancel();
    onSpeakerObserver?.cancel();
    onMicrophoneObserver?.cancel();
    roomStateObserver?.cancel();
    liveStateObserver?.cancel();

    operationState.dispose();
    roomShareKey.dispose;
    liveShareKey.dispose;

    parent<SurgeryRoomController>()?.doctors.removeListener(
      updateOperationState,
    );
  }

  void subscriptRoomEventBus() {
    // 订阅room开启状态
    roomStateObserver = eventBus.on<EventBusInfo>().listen((event) {
      if (event.type == EventBusType.surgeryRoomState) {
        getRoomShareKey();
      }
    });
    // 订阅live开启状态
    liveStateObserver = eventBus.on<EventBusInfo>().listen((event) {
      if (event.type == EventBusType.surgeryLiveState) {
        getLiveShareKey();
      }
    });
    //监听协同正在说话用户
    onSpeakerObserver = MqttRtcLiveApi<SpeakerMessage>.onSpeakerMessage(
      surgeryId: parent<SurgeryRoomController>()!.surgeryId,
    ).listen((message) {
      parent<SurgeryRoomController>()?.showCurrentSpeaker(message);
    });

    //监听协同人员麦克风开关
    onMicrophoneObserver =
        MqttRtcLiveApi<MicrophoneMessage>.onMicrophoneMessage(
          surgeryId: parent<SurgeryRoomController>()!.surgeryId,
        ).listen((message) {
          parent<SurgeryRoomController>()?.changeSynergyMicrophone(message);
        });
  }

  var doctorCount = 0;
  void updateOperationState() {
    final doctors = parent<SurgeryRoomController>()?.doctors.value;
    final count = doctors?.length ?? 0;
    if (count > 0) {
      if (count != doctorCount) {
        doctorCount = doctors?.length ?? 0;
        operationState.value = OperationState.rtcLive;
      }
    } else {
      doctorCount = 0;
      // operationState.value = OperationState.function;
    }
  }

  void onTap(FunctionEvent event) {
    switch (event) {
      case FunctionEvent.markTime:
        parent<SurgeryRoomController>()!.markTime();
        break;
      case FunctionEvent.playback:
        parent<SurgeryRoomController>()!.surgPlayback();
        break;
      case FunctionEvent.cooperation:
        operationState.value = OperationState.rtcLive;
        break;
      case FunctionEvent.rmtLive:
        operationState.value = OperationState.rtmLive;
        break;
      case FunctionEvent.setting:
        operationState.value = OperationState.setting;
        break;
      case FunctionEvent.control:
        operationState.value = OperationState.control;
        break;
      case FunctionEvent.stop:
        parent<SurgeryRoomController>()?.stopSurgery();
        break;
    }
  }

  // BLE 控制消息监听
  void addBleControlMessage() {
    // PeripheralController.instance
    //     .setDataAcceptListener((AcceptData acceptData) {
    //   app.logW("-----------蓝牙数据接收-----------${acceptData.toString()}");
    //   if (AppContext.share.taskState == TaskState.install) {
    //     return;
    //   }
    //   Map<String, dynamic> data = {};
    //   if (acceptData.data.startsWith("{") && acceptData.data.endsWith("}")) {
    //     data = jsonDecode(acceptData.data);
    //   }
    //   if (acceptData.msgType == ClientMsgType.mtuNotify.index) {
    //     /// 收到mtu通知，通信建立完成,下发token和手术状态
    //     PeripheralController.instance.notify(
    //         msgType: ClientMsgType.authLoginRequest.index,
    //         data: jsonEncode({
    //           "appToken": AppContext.share.authorizeInfo.accessToken,
    //           "deviceId": AppContext.share.authorizeInfo.deviceId
    //         }));
    //     final vc = parent<SurgeryRoomController>()!;
    //     PeripheralController.instance.notify(
    //         msgType: ClientMsgType.surgeryStatus.index,
    //         data: jsonEncode({
    //           "surgeryId": vc.surgeryId,
    //           "isLiveOngoing": vc.roomAndLiveStatus.value.isRtmpLiveOngoing
    //         }));
    //     PeripheralController.instance.notify(
    //         msgType: ClientMsgType.networkState.index,
    //         data: jsonEncode(
    //             {"enabled": AppContext.share.networkConnected.value}));
    //   } else if (acceptData.msgType == ClientMsgType.restartDevice.index) {
    //     /// 设备重启消息
    //     DeviceCmd.share.restartDevice();
    //   } else if (acceptData.msgType == ClientMsgType.shutdownDevice.index) {
    //     /// 设备关闭消息
    //     DeviceCmd.share.shutdownDevice();
    //   } else if (acceptData.msgType == ClientMsgType.authLoginRequest.index) {
    //     ///授权请求
    //     PeripheralController.instance.notify(
    //         msgType: ClientMsgType.authLoginRequest.index,
    //         data: jsonEncode(
    //             {"appToken": AppContext.share.authorizeInfo.accessToken}));
    //   } else if (acceptData.msgType == ClientMsgType.surgeryStop.index) {
    //     /// 手术关闭
    //     parent<SurgeryRoomController>()?.stopSurgery();
    //   } else if (acceptData.msgType == ClientMsgType.liveStart.index) {
    //     /// 直播开启
    //     parent<SurgeryRoomController>()?.startLive(title: data["title"]);
    //   } else if (acceptData.msgType == ClientMsgType.liveStop.index) {
    //     /// 直播关闭
    //     parent<SurgeryRoomController>()?.stopLive();
    //   } else if (acceptData.msgType == ClientMsgType.markTime.index) {
    //     /// 睿标记
    //     parent<SurgeryRoomController>()?.markTime();
    //   } else if (acceptData.msgType == ClientMsgType.microphoneState.index) {
    //     bool? enabled = data['enabled'];
    //     if (enabled == null) {
    //       return;
    //     }

    //     /// 设置麦克风
    //     parent<SurgeryRoomController>()?.setMicrophoneState(
    //       enabled: enabled,
    //     );
    //   } else if (acceptData.msgType == ClientMsgType.bodyoutMask.index) {
    //     bool? enabled = data['enabled'];
    //     if (enabled == null) {
    //       return;
    //     }

    //     /// 设置体腔外模糊
    //     parent<SurgeryRoomController>()?.setBodyoutMaskState(
    //       enabled: enabled,
    //     );
    //   } else if (acceptData.msgType == ClientMsgType.settingRead.index) {
    //     /// 读取当前设置信息
    //     final vc = parent<SurgeryRoomController>()!;
    //     PeripheralController.instance.notify(
    //         msgType: ClientMsgType.microphoneState.index,
    //         data: jsonEncode({"enabled": vc.microphoneIsOn.value}));
    //     PeripheralController.instance.notify(
    //         msgType: ClientMsgType.bodyoutMask.index,
    //         data: jsonEncode({"enabled": vc.outsideBlurIsOn.value}));
    //   } else if (acceptData.msgType == ClientMsgType.surgeryTime.index) {
    //     int time =
    //         parent<SurgeryRoomController>()!.state.value?.surgeryTime ?? 0;
    //     PeripheralController.instance.notify(
    //         msgType: ClientMsgType.surgeryTime.index,
    //         data: jsonEncode({"surgeryTime": time}));
    //   } else if (acceptData.msgType ==
    //       ClientMsgType.synergyMicrophoneState.index) {
    //     int? id = data['id'];
    //     if (id == null) {
    //       return;
    //     }
    //     MqttRoomControlApi.broadcastSynergyMicrophoneState(
    //       id: id,
    //       surgId: parent<SurgeryRoomController>()!.surgeryId,
    //     ).publish();
    //   } else if (acceptData.msgType ==
    //       ClientMsgType.requestM3Disconnect.index) {
    //     /// 断开连接
    //     PeripheralController.instance.stop();
    //   } else if (acceptData.msgType == ClientMsgType.networkState.index) {
    //     PeripheralController.instance.notify(
    //         msgType: ClientMsgType.networkState.index,
    //         data: jsonEncode(
    //             {"enabled": AppContext.share.networkConnected.value}));
    //   }
    // });
  }

  /// 订阅远端遥控器 MQTT 消息
  void subscriptRemoteControlMessage() {
    onAuthLoginObserver =
        MqttDeviceControlApi<DeviceControlMessage>.onAuthLoginRequest().listen((
          message,
        ) {
          MqttDeviceControlApi.broadcastAuthToken(
            controlToken: message.token!,
            authToken: AppContext.share.authorizeInfo.accessToken,
          );
        });

    onSurgeryStopObserver =
        MqttDeviceControlApi<DeviceControlMessage>.onSurgeryStopRequest()
            .listen((message) {
              parent<SurgeryRoomController>()?.stopSurgery();
            });

    onLiveStartObserver =
        MqttDeviceControlApi<DeviceStartLiveMessage>.onLiveStartRequest()
            .listen((message) {
              parent<SurgeryRoomController>()?.startLive(title: message.title);
            });

    onLiveStopObserver =
        MqttDeviceControlApi<DeviceControlMessage>.onLiveStopRequest().listen((
          message,
        ) {
          parent<SurgeryRoomController>()?.stopLive();
        });

    onMarkTimeObserver =
        MqttDeviceControlApi<DeviceControlMessage>.onMarkTimeRequest().listen((
          message,
        ) {
          parent<SurgeryRoomController>()?.markTime();
        });

    onMicrophoneSettingObserver =
        MqttDeviceControlApi<DeviceSettingMessage>.onMicrophoneSettingRequest()
            .listen((message) {
              parent<SurgeryRoomController>()?.setMicrophoneState(
                enabled: message.enabled,
              );
            });

    onBodyoutMaskSettingObserver =
        MqttDeviceControlApi<DeviceSettingMessage>.onBodyoutMaskSettingRequest()
            .listen((message) {
              parent<SurgeryRoomController>()?.setBodyoutMaskState(
                enabled: message.enabled,
              );
            });

    /// 监听设备设置获取消息
    onSettingReadObserver =
        MqttDeviceControlApi<DeviceControlMessage>.onSettingReadRequest()
            .listen((message) {
              final vc = parent<SurgeryRoomController>()!;
              MqttDeviceControlApi.broadcastMicrophoneSettingStatus(
                controlToken: message.token!,
                enabled: vc.microphoneIsOn.value,
              ).publish();
              MqttDeviceControlApi.broadcastBodyoutMaskSettingStatus(
                controlToken: message.token!,
                enabled: vc.outsideBlurIsOn.value,
              ).publish();
            });
  }

  /// 订阅设备网络状态
  void subscriptNetworkStatus() {
    if (AppContext.share.networkConnected.value) {
      onAuthLoginObserver?.restore();
      onSurgeryStopObserver?.restore();
      onLiveStartObserver?.restore();
      onLiveStopObserver?.restore();
      onMarkTimeObserver?.restore();
      onMicrophoneSettingObserver?.restore();
      onBodyoutMaskSettingObserver?.restore();
      onSettingReadObserver?.restore();
      onSpeakerObserver?.restore();
      onMicrophoneObserver?.restore();
    } else {
      onAuthLoginObserver?.cancel();
      onSurgeryStopObserver?.cancel();
      onLiveStartObserver?.cancel();
      onLiveStopObserver?.cancel();
      onMarkTimeObserver?.cancel();
      onMicrophoneSettingObserver?.cancel();
      onBodyoutMaskSettingObserver?.cancel();
      onSettingReadObserver?.cancel();
      onSpeakerObserver?.cancel();
      onMicrophoneObserver?.cancel();
      roomShareKey.value = null;
      liveShareKey.value = null;
    }
  }

  String? get roomShareUrl {
    if (roomShareKey.value == null) {
      getRoomShareKey();
      return null;
    }

    final baseUrl = AppDomain.share.webBaseUri;
    final deviceId = AppContext.share.authorizeInfo.deviceId;
    final surgeryId = parent<SurgeryRoomController>()!.surgeryId;

    return '$baseUrl/virtual-surgery-room?deviceId=$deviceId&surgeryId=$surgeryId&shareKey=${roomShareKey.value}';
  }

  String? get liveShareUrl {
    if (liveShareKey.value == null) {
      getLiveShareKey();
      return null;
    }

    final baseUrl = AppDomain.share.webBaseUri;
    final deviceId = AppContext.share.authorizeInfo.deviceId;
    final surgeryId = parent<SurgeryRoomController>()!.surgeryId;
    return '$baseUrl/live-room-detail?deviceId=$deviceId&surgeryId=$surgeryId&shareKey=${liveShareKey.value}';
  }

  void getRoomShareKey() {
    final roomStart =
        parent<SurgeryRoomController>()!
            .roomAndLiveStatus
            .value
            .isRtcLiveOngoing;
    if (!roomStart) {
      return;
    }
    HttpRoomApi<RoomShareInfo>.share(
      deviceId: AppContext.share.authorizeInfo.deviceId,
    ).request().then((value) {
      roomShareKey.value = value.shareKey;
    });
  }

  void getLiveShareKey() {
    final liveStart =
        parent<SurgeryRoomController>()!
            .roomAndLiveStatus
            .value
            .isRtmpLiveOngoing;
    if (!liveStart) {
      return;
    }

    HttpLiveApi<LiveShareInfo>.share(
      deviceId: AppContext.share.authorizeInfo.deviceId,
    ).request().then((value) {
      liveShareKey.value = value.shareKey;
    });
  }
}
