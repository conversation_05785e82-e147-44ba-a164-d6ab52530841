import 'dart:async';
import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/widgets/app_background.widget.dart';

class OperationContainer extends StatefulWidget {
  final int startTimestamp;
  final void Function()? onStop;
  final Widget child;

  const OperationContainer({
    super.key,
    required this.startTimestamp,
    required this.child,
    this.onStop,
  });

  @override
  State<OperationContainer> createState() => _OperationContainerState();
}

class _OperationContainerState extends State<OperationContainer> {
  late final Timer timer;

  final time = "00:00:00".notifier;

  @override
  void initState() {
    timer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (mounted) {
        final deltaSeconds =
            (DateTime.now().millisecondsSinceEpoch - widget.startTimestamp) /
            1000;
        time.value = deltaSeconds.round().toHMS;
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    timer.cancel();
    time.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppBackground(
      child: Column(
        children: [
          Expanded(child: widget.child),
          Container(
            width: double.infinity,
            height: 172.h,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF1C2C3B), Color(0xFF26465C)],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: BorderRadius.circular(30.r),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Center(
                    child: ShaderMask(
                      shaderCallback:
                          (bounds) => const LinearGradient(
                            begin: Alignment.topCenter,
                            colors: [Color(0xFF5CF5EB), Color(0xFF56B3E3)],
                            end: Alignment.bottomCenter,
                          ).createShader(bounds),
                      child: ValueListenableBuilder(
                        valueListenable: time,
                        builder: (context, value, child) {
                          return Text(
                            value,
                            style: GoogleFonts.roboto(
                              shadows: [
                                Shadow(
                                  blurRadius: 4,
                                  color: Colors.black.withValues(alpha: 0.5),
                                  offset: const Offset(0, 2),
                                ),
                              ],
                              textStyle: TextStyle(
                                color: Colors.white,
                                fontSize: 120.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
                GestureDetector(
                  child: Container(
                    height: double.infinity,
                    padding: EdgeInsets.all(24.w),
                    decoration: BoxDecoration(
                      color: Color(0x1AFFFFFF),
                      borderRadius: BorderRadius.circular(30.r),
                    ),
                    child: Row(
                      children: [
                        Image(width: 88.w, height: 88.h, image: R.image.stop()),
                        16.horizontalSpace,
                        Text(
                          S.current.f_8PBHnVEG,
                          style: TextStyle(fontSize: 48.sp),
                        ),
                      ],
                    ),
                  ),
                  onTap: () {
                    widget.onStop?.call();
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
