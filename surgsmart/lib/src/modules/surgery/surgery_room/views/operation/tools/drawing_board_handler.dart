import 'package:surgsmart/src/models/mqtt/interactive.model.dart';

class DrawingBoardHandler {
  static void handleCursorMessage(
    Map<String, dynamic> payload,
    Function(int userId, Graphic cursor) drawCursor,
  ) {
    final userId = int.parse(payload['userId'].toString());
    final cursor = Graphic().initWith({
      'graph': payload['graph'],
      'offset': [
        double.parse(payload['point']['offset'].first),
        double.parse(payload['point']['offset'].last),
      ],
      'timestamp': payload['timestamp'],
    });
    drawCursor(userId, cursor);
  }

  static void handleEmptyMessage(Map<String, dynamic> payload,
      Function(int userId, List<String> uuids) clearGraphics) {
    final userId = int.parse(payload['userId'].toString());
    final List<String> uuids = List<String>.from(payload['uuids']);
    clearGraphics(userId, uuids);
  }

  static void handlePointPathMessage(Map<String, dynamic> payload,
      Function(int userId, Graphic graphic, Graphic cursor) drawGraphic) {
    final userId = int.parse(payload['userId'].toString());
    final graphic = Graphic().initWith({
      'graph': payload['graph'],
      'timestamp': payload['timestamp'],
      'uuid': payload['uuid'],
      'points': [
        {
          'color': payload['point']['color'],
          'width': double.parse(payload['point']['width']),
          'offset': [
            double.parse(payload['point']['offset'].first),
            double.parse(payload['point']['offset'].last),
          ],
        }
      ],
    });
    final cursor = Graphic().initWith({
      'graph': payload['graph'],
      'offset': [
        double.parse(payload['point']['offset'].first),
        double.parse(payload['point']['offset'].last),
      ],
      'timestamp': payload['timestamp'],
    });
    drawGraphic(userId, graphic, cursor);
  }

  static void handleRedoMessage(Map<String, dynamic> payload,
      Function(int userId, List<String> uuids) redo) {
    final userId = int.parse(payload['userId'].toString());
    final List<String> uuids = List<String>.from(payload['uuids']);
    redo(userId, uuids);
  }

  static void handleUndoMessage(Map<String, dynamic> payload,
      Function(int userId, List<String> uuids) undo) {
    final userId = int.parse(payload['userId'].toString());
    final List<String> uuids = List<String>.from(payload['uuids']);
    undo(userId, uuids);
  }
}
