import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/apis/mqtt/device_control.api.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/widgets/no_network.widget.dart';

class RemoteControlCard extends StatelessWidget {
  const RemoteControlCard({super.key, this.isNetwork = true});

  final bool isNetwork;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 25.h),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Image(
                image: R.image.qrcode_icon(),
                width: 120.r,
                height: 120.r,
              ),
              Text(
                S.current.f_9aUb5hVs,
                style: TextStyle(
                  fontSize: 80.sp,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                  height: 100 / 80,
                ),
              ),
            ],
          ),
          isNetwork
              ? Expanded(
                  child: Column(
                    children: [
                      Stack(
                        children: [
                          ValueListenableBuilder(
                            valueListenable:
                                AppContext.share.controlToken.current,
                            builder: (context, value, child) => QrImageView(
                              data: AppContext.share.controlToken.url,
                              size: 520.r,
                              padding: EdgeInsets.all(20.r),
                              backgroundColor: Colors.transparent,
                              eyeStyle: const QrEyeStyle(
                                color: Colors.white,
                                eyeShape: QrEyeShape.square,
                              ),
                              dataModuleStyle: const QrDataModuleStyle(
                                color: Colors.white,
                                dataModuleShape: QrDataModuleShape.square,
                              ),
                            ),
                          ),
                          Positioned.fill(
                            child: Center(
                              child: Container(
                                width: 100.r,
                                height: 100.r,
                                padding: EdgeInsets.all(10.r),
                                decoration: BoxDecoration(
                                  color: Colors.black,
                                  borderRadius: BorderRadius.circular(10.r),
                                ),
                                child: Image(
                                  image: R.image.logo_only(),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 48.h),
                      Button(
                        onPressed: () {
                          AppContext.share.controlToken.refresh(
                            discardLast: (token) {
                              if (token?.isNotEmpty == true) {
                                MqttDeviceControlApi.broadcastControlStatus(
                                  controlToken: token!,
                                  status: DeviceControlStatus.tokenExpired,
                                ).publish();
                              }
                              return true;
                            },
                          );
                        },
                        padding: EdgeInsets.symmetric(
                          horizontal: 143.w,
                          vertical: 16.h,
                        ),
                        backgroundColor: const Color(0xFF05222D),
                        borderRadius: BorderRadius.circular(16.r),
                        borderSide: BorderSide(
                          color: const Color(0xFF0EC6D2),
                          width: 4.r,
                        ),
                        label: Text(
                          S.current.f_43uAHda1,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 48.sp,
                            height: 67 / 48,
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              : const Expanded(child: NoNetwork()),
        ],
      ),
    );
  }
}
