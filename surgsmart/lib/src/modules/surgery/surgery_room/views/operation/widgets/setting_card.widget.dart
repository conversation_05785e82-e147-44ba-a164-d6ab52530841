import 'dart:async';
import 'dart:ui';
import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/models/device_setting_model.dart';
import 'package:surgsmart/src/models/event_bus.model.dart';
import 'package:surgsmart/src/models/network_device_model.dart';
import 'package:surgsmart/src/modules/service_setting/service_setting/service_setting.controller.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/surgery_room.controller.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/operation/widgets/control_strip.widget.dart';
import 'package:surgsmart/src/routes/go_paths.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';
import 'package:surgsmart/src/tools/device_cmd.dart';
import 'package:surgsmart/src/tools/event_bus.dart';
import 'package:surgsmart/src/tools/toast.dart';
import 'operation_dropdown.widget.dart';

class SettingCard extends StatelessWidget {
  const SettingCard({super.key});
  @override
  Widget build(BuildContext context) {
    return AppControllerBuilder<SurgeryRoomController>(
      builder: (context, controller) {
        return SettingCardCtrl(controller: controller);
      },
    );
  }
}

class SettingCardCtrl extends StatefulWidget {
  final SurgeryRoomController? controller;
  const SettingCardCtrl({super.key, required this.controller});

  @override
  State<SettingCardCtrl> createState() => _SettingCardState();
}

class _SettingCardState extends State<SettingCardCtrl> {
  ScrollController scrollController = ScrollController();

  List<DeviceSettingInfo> microphoneDevices = [];
  List<DeviceSettingInfo> loudspeakerDevices = [];
  List<DeviceSettingInfo> videoDevices = [];
  List<DeviceSettingInfo> networkDevices = [];
  StreamSubscription<EventBusInfo>? microphoneObserver;
  StreamSubscription<EventBusInfo>? loudspeakerObserver;
  StreamSubscription<EventBusInfo>? videoObserver;
  StreamSubscription<EventBusInfo>? ethernetObserver;

  @override
  void initState() {
    getMediaDevices();
    subscriptRefreshEvent();

    getNetworkDevices();

    super.initState();
  }

  void subscriptRefreshEvent() {
    // 订阅音频外设插拔选中状态
    microphoneObserver = eventBus
        .on<EventBusInfo<List<DeviceSettingInfo>>>()
        .listen((event) {
          if (event.type == EventBusType.microphoneDeviceChange &&
              event.data != null) {
            microphoneDevices = event.data!;
            setState(() {});
          }
        });
    loudspeakerObserver = eventBus
        .on<EventBusInfo<List<DeviceSettingInfo>>>()
        .listen((event) {
          if (event.type == EventBusType.loudspeakerDeviceChange &&
              event.data != null) {
            loudspeakerDevices = event.data!;
            setState(() {});
          }
        });

    videoObserver = eventBus.on<EventBusInfo<List<DeviceSettingInfo>>>().listen(
      (event) {
        if (event.type == EventBusType.videoDeviceChange &&
            event.data != null) {
          videoDevices = event.data!;
          setState(() {});
        }
      },
    );

    ethernetObserver = eventBus.on<EventBusInfo>().listen((event) {
      if (event.type == EventBusType.ethernetAvailable) {
        if (event.data == true &&
            networkDevices.any(
              (device) => device.value == NetWorkType.ethernet,
            )) {
          //更新为有线选中
          app.logW("监听有线网络状态: ${event.data}}");

          networkDevices
              .firstWhere((device) => device.value == NetWorkType.ethernet)
              .selected = true;
          setState(() {});
        }
      } else if (event.type == EventBusType.netWorkChange) {
        getNetworkDevices();
      }
    });
  }

  @override
  void dispose() {
    microphoneObserver?.cancel();
    loudspeakerObserver?.cancel();
    videoObserver?.cancel();
    ethernetObserver?.cancel();
    super.dispose();
  }

  Future<void> getMediaDevices() async {
    microphoneDevices = widget.controller?.getMicrophoneDevices() ?? [];
    loudspeakerDevices = widget.controller?.getLoudspeakerDevices() ?? [];
    videoDevices = widget.controller?.getExtendedVideoDevices() ?? [];
    setState(() {});
  }

  Future<void> getNetworkDevices() async {
    networkDevices =
        await widget.controller?.getAvailableNetworkDevices() ?? [];
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 30.h),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Image(image: R.image.setting_icon(), width: 120.r, height: 120.r),
              SizedBox(width: 20.w),
              Text(
                S.current.f_9aUbh5VO,
                style: TextStyle(
                  fontSize: 80.sp,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                  height: 100 / 80,
                ),
              ),
            ],
          ),
          Expanded(
            child: ScrollConfiguration(
              behavior: const MaterialScrollBehavior().copyWith(
                physics: const BouncingScrollPhysics(),
                dragDevices: {PointerDeviceKind.touch, PointerDeviceKind.mouse},
              ),
              child: SingleChildScrollView(
                controller: scrollController,
                child: Padding(
                  padding: EdgeInsets.only(left: 60.r, right: 60.r, top: 20.r),
                  child: Column(
                    children: [
                      OperationDropdown(
                        title: "网络",
                        moreSettings: GestureDetector(
                          onTap: () {
                            app.openInner(
                              GoPaths.serviceSetting,
                              arguments: {"type": ServiceType.networkSetting},
                            );
                          },
                          child: Text(
                            '高级设置',
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.5),
                              fontSize: 36.sp,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                        noneTips: "未开启网络",
                        showTips:
                            AppPreferences.selectedNetwork.stringValue ==
                            NetWorkType.none.name,
                        dataList: networkDevices,
                        // interceptOpenSelect: () {
                        //   scrollController.jumpTo(1890.h);
                        //   eventBus.fire(
                        //     EventBusInfo(
                        //       type: EventBusType.openSelect,
                        //       data: "网络",
                        //     ),
                        //   );
                        // },
                        onSelect: (item) {
                          int index = networkDevices.indexOf(item);
                          int i = 0;
                          while (i < networkDevices.length) {
                            networkDevices[i].selected = i == index;
                            i++;
                          }
                          widget.controller?.setNetworkConfig(item.value);
                        },
                      ),
                      SizedBox(height: 25.h),
                      ValueListenableBuilder(
                        valueListenable:
                            widget.controller?.switchCamera ?? false.notifier,
                        builder: (context, value, child) {
                          return OperationDropdown(
                            title: S.current.f_9aUb4ZlV,
                            noneTips: S.current.f_MEhRr75X,
                            dataList: videoDevices,
                            switchState: value,
                            switchChange: (val) {
                              widget.controller?.setCameraSwitch(enabled: val);
                            },
                            onSelect: (item) async {
                              if (item.device ==
                                      widget
                                          .controller
                                          ?.currentVideoDevice
                                          ?.device ||
                                  widget.controller?.switchCamera.value !=
                                      true) {
                                return;
                              }
                              await widget.controller?.setVideoDevice(item);
                            },
                            footer: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  S.current.f_9aUbs47P,
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 36.sp,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                ValueListenableBuilder(
                                  valueListenable:
                                      widget.controller?.isShowExternalVideo ??
                                      false.notifier,
                                  builder: (context, value, child) {
                                    return Padding(
                                      padding: EdgeInsets.only(right: 16.w),
                                      child: Transform.scale(
                                        scale: 1.4.w,
                                        child: Switch(
                                          value: value,
                                          onChanged: (value) {
                                            if (widget
                                                    .controller
                                                    ?.switchCamera
                                                    .value !=
                                                true) {
                                              ToastUtils.showToast(
                                                context,
                                                message: S.current.f_XEZXNdmx,
                                              );
                                              return;
                                            }
                                            widget.controller?.setExternalVideo(
                                              show: value,
                                            );
                                          },
                                          trackColor:
                                              WidgetStateProperty.resolveWith<
                                                Color
                                              >((states) {
                                                if (states.contains(
                                                  WidgetState.selected,
                                                )) {
                                                  return const Color(
                                                    0xFF22D1B6,
                                                  );
                                                }
                                                return Colors.transparent;
                                              }),
                                          activeColor: Colors.white,
                                          inactiveThumbColor: Colors.white,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                      SizedBox(height: 25.h),
                      ValueListenableBuilder(
                        valueListenable:
                            widget.controller?.outsideBlurIsOn ??
                            false.notifier,
                        builder: (context, value, child) {
                          return OperationDropdown(
                            title: S.current.f_9aUbC2c9,
                            switchState: value,
                            switchChange: (val) {
                              widget.controller?.setBodyoutMaskState(
                                enabled: val,
                              );
                            },
                          );
                        },
                      ),
                      SizedBox(height: 25.h),
                      OperationDropdown(
                        title: S.current.f_9aUb51TQ,
                        noneTips: S.current.f_MEWGDcye,
                        dataList: loudspeakerDevices,
                        interceptOpenSelect: () {
                          scrollController.jumpTo(860.h);
                          eventBus.fire(
                            EventBusInfo(
                              type: EventBusType.openSelect,
                              data: S.current.f_9aUb51TQ,
                            ),
                          );
                        },
                        onSelect: (item) {
                          widget.controller?.setLoudspeakerConfig(item);
                        },
                        controlStripData: ControlStripData(
                          //title: '通话音量',
                          activeImage: R.image.volume_on(),
                          inactiveImage: R.image.volume_off(),
                        ),
                        /**提示音量取消 */
                        // footer: ValueListenableBuilder(
                        //   valueListenable:
                        //       widget.controller?.currentLoudspeaker ??
                        //           ValueNotifier<DeviceSettingInfo?>(null),
                        //   builder: (context, speaker, child) {
                        //     return speaker == null
                        //         ? SizedBox.shrink()
                        //         : ControlStrip(
                        //             title: '系统音量',
                        //             max: 65535,
                        //             activeImage: R.image.volume_on(),
                        //             inactiveImage: R.image.volume_off(),
                        //             value: widget.controller
                        //                     ?.getTipVolume(speaker.info) ??
                        //                 39321,
                        //             slidingEnd: (value) {
                        //               widget.controller
                        //                   ?.saveTipVolume(speaker.info, value);
                        //             },
                        //             slidingChange: (value) {
                        //               // 提示音属于单次播放控制，所以滑动过程中不设置
                        //               // widget.controller
                        //               //     ?.saveTipVolume(speaker.info, value);
                        //             },
                        //           );
                        //   },
                        // ),
                      ),
                      SizedBox(height: 25.h),
                      OperationDropdown(
                        title: S.current.f_9aUbfLqv,
                        noneTips: S.current.f_MHaexjYP,
                        dataList: microphoneDevices,
                        interceptOpenSelect: () {
                          scrollController.jumpTo(1280.h);
                          eventBus.fire(
                            EventBusInfo(
                              type: EventBusType.openSelect,
                              data: S.current.f_9aUbfLqv,
                            ),
                          );
                        },
                        onSelect: (item) {
                          widget.controller?.setMicrophoneConfig(item);
                        },
                        controlStripData: ControlStripData(
                          activeImage: R.image.mic_on(),
                          inactiveImage: R.image.mic_off(),
                        ),
                      ),
                      SizedBox(height: 25.h),
                      FutureBuilder(
                        future: DeviceCmd.share.getBrightness(),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.done) {
                            return Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(30.r),
                              decoration: BoxDecoration(
                                color: const Color(0xFF1D2632),
                                borderRadius: BorderRadius.circular(24.r),
                                border: Border.all(
                                  color: const Color(0xFF313E50),
                                  width: 8.r,
                                ),
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    S.current.f_MKi9hlLl,
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 56.sp,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  SizedBox(height: 50.h),
                                  ControlStrip(
                                    min: 0,
                                    max: 100,
                                    activeImage: R.image.luminance(),
                                    value: snapshot.data?.toDouble() ?? 0,
                                    slidingEnd: (value) {
                                      DeviceCmd.share.setBrightness(
                                        value.toInt(),
                                      );
                                    },
                                    slidingChange: (value) {
                                      DeviceCmd.share.setBrightness(
                                        value.toInt(),
                                      );
                                    },
                                  ),
                                ],
                              ),
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                      SizedBox(height: 25.h),
                      ValueListenableBuilder(
                        valueListenable:
                            widget.controller?.switchRecording ??
                            false.notifier,
                        builder: (context, value, child) {
                          return OperationDropdown(
                            title: S.current.f_9aUb72o5,
                            switchState: value,
                            selfControlState: false,
                            switchChange: (val) {
                              widget.controller?.setRecordAudioSwitch(
                                enabled: val,
                              );
                            },
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
