import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/widgets/click_animation.widget.dart';

enum FunctionEvent {
  markTime,
  cooperation,
  rmtLive,
  stop,
  control,
  setting,
  playback,
}

class FunctionCard extends StatelessWidget {
  const FunctionCard({
    super.key,
    required this.isRtcLiveOngoing,
    required this.isRtmpLiveOngoing,
    required this.noSignalAll,
    required this.isVoicePrepared,
    required this.onTap,
  });

  final bool isRtcLiveOngoing;
  final bool isRtmpLiveOngoing;
  final bool noSignalAll;
  final bool isVoicePrepared;

  final void Function(FunctionEvent event) onTap;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.only(left: 30.w, right: 30.w, top: 20.h),
          child: Row(
            children: [
              Image(image: R.image.logo_variant(), width: 330.w, height: 77.h),
              const Spacer(),
              if (isVoicePrepared)
                Image(image: R.image.xr_small(), width: 72.r, height: 72.r),
              24.horizontalSpace,
              ValueListenableBuilder(
                valueListenable: AppContext.share.networkConnected,
                builder: (context, networkConnected, child) {
                  return Button(
                    onPressed: () {
                      onTap(FunctionEvent.control);
                    },
                    label: Container(
                      width: 100.r,
                      height: 100.r,
                      padding: EdgeInsets.all(16.r),
                      child: Image(
                        image:
                            networkConnected
                                ? R.image.remote_control()
                                : R.image.remote_control_no(),
                        width: 68.r,
                        height: 68.r,
                      ),
                    ),
                  );
                },
              ),
              SizedBox(width: 10.w),
              Button(
                onPressed: () {
                  onTap(FunctionEvent.setting);
                },
                label: Container(
                  width: 100.r,
                  height: 100.r,
                  padding: EdgeInsets.all(16.r),
                  child: Image(
                    image: R.image.system_setting(),
                    width: 68.r,
                    height: 68.r,
                  ),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 40.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    //动画要求居中缩放
                    Flexible(
                      child: _FunctionItem(
                        assetKey: R.image.asset.add_mark.keyName,
                        title: S.current.f_8P0ppAnK,
                        onTap: () {
                          onTap(FunctionEvent.markTime);
                        },
                      ),
                    ),
                    SizedBox(width: 23.w),
                    Flexible(
                      child: _FunctionItem(
                        assetKey:
                            noSignalAll
                                ? R.image.asset.playback_no.keyName
                                : R.image.asset.playback.keyName,
                        title: S.current.f_8PBHcoUd,
                        color:
                            noSignalAll
                                ? Colors.white.withValues(alpha: 0.5)
                                : Colors.white,
                        onTap:
                            noSignalAll
                                ? null
                                : () {
                                  if (noSignalAll) return;
                                  onTap(FunctionEvent.playback);
                                },
                      ),
                    ),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: ValueListenableBuilder(
                        valueListenable: AppContext.share.networkConnected,
                        builder: (context, networkConnected, child) {
                          return _FunctionItem(
                            assetKey:
                                networkConnected
                                    ? R.image.asset.surgery_cooperation.keyName
                                    : R
                                        .image
                                        .asset
                                        .surgery_cooperation_no
                                        .keyName,
                            title: S.current.f_8PBHwBe2,
                            color:
                                networkConnected
                                    ? Colors.white
                                    : Colors.white.withValues(alpha: 0.5),
                            onTap: () {
                              onTap(FunctionEvent.cooperation);
                            },
                          );
                        },
                      ),
                    ),
                    SizedBox(width: 23.w),
                    Flexible(
                      child: ValueListenableBuilder(
                        valueListenable: AppContext.share.networkConnected,
                        builder: (context, networkConnected, child) {
                          return _FunctionItem(
                            assetKey:
                                networkConnected
                                    ? (isRtmpLiveOngoing
                                        ? R.image.asset.rtm_live_on.keyName
                                        : R.image.asset.rtm_live.keyName)
                                    : R.image.asset.rtm_live_no.keyName,
                            title:
                                isRtmpLiveOngoing && networkConnected
                                    ? S.current.f_LKrfp3XX
                                    : S.current.f_8PBHyiTS,
                            color:
                                networkConnected
                                    ? (isRtmpLiveOngoing
                                        ? const Color(0xff23D9BD)
                                        : Colors.white)
                                    : Colors.white.withValues(alpha: 0.5),
                            onTap: () {
                              onTap(FunctionEvent.rmtLive);
                            },
                          );
                        },
                      ),
                    ),

                    // Flexible(
                    //   child: _FunctionItem(
                    //     assetKey: R.image.asset.surgery_stop.keyName,
                    //     title: "结束手术",
                    //     onTap: () {
                    //       showStopSurgeryModal(context);
                    //     },
                    //   ),
                    // ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class _FunctionItem extends StatelessWidget {
  final String assetKey;
  final String title;
  final Color color;
  final void Function()? onTap;
  const _FunctionItem({
    required this.assetKey,
    required this.title,
    this.color = Colors.white,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ClickAnimateContainer(
      onTap: onTap,
      tapDownColor: const Color(0x500EC6D2),
      defaultColor: Colors.white10,
      borderRadius: 16.r,
      width: 419.w,
      height: 340.h,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(assetKey, width: 140.r, height: 140.r, fit: BoxFit.cover),
          SizedBox(
            width: double.infinity,
            height: 130.h,
            child: Center(
              child: Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: color,
                  fontSize: 48.sp,
                  height: 67 / 48,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
