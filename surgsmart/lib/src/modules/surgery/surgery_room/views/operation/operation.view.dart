import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/modules/home/<USER>/widgets/ble_scanner.widget.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/surgery_room.controller.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/operation/widgets/function_card.widget.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/operation/widgets/operation_container.widget.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/operation/widgets/operation_menu.widget.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/operation/widgets/remote_control_card.widget.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/operation/widgets/rtc_live_card.widget.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/operation/widgets/rtmp_live_card.widget.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/operation/widgets/setting_card.widget.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/widgets/modal.widget.dart';
import 'operation.controller.dart';

/// 所属模块: surgery
///
/// 所属路由: surgeryRoom
///
/// 功能操作面板
class OperationView extends AppView<OperationController> {
  const OperationView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    return controller.build(
      onSuccess: onSuccess,
      onFailure: onFailure,
      onLoading: onLoading,
      onEmpty: onEmpty,
    );
  }

  Widget onSuccess(BuildContext context, ApiModel value) {
    return ValueListenableBuilder(
      valueListenable: controller.operationState,
      builder: (context, value, child) {
        return Row(
          children: [
            Visibility(
              visible: OperationState.function != value,
              child: SizedBox(
                width: 142.w,
                child: OperationMenu(
                  operationState: value,
                  onTap: (operationState) {
                    controller.operationState.value = operationState;
                  },
                ),
              ),
            ),
            Expanded(
              child: Container(
                clipBehavior: Clip.hardEdge,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30.r),
                ),
                child: OperationContainer(
                  startTimestamp: (controller
                          .parent<SurgeryRoomController>()!
                          .state
                          .value
                          ?.surgeryTime ??
                      0),
                  onStop: () {
                    showStopSurgeryModal(context);
                  },
                  child: switch (value) {
                    OperationState.function => ValueListenableBuilder(
                        valueListenable: controller
                            .parent<SurgeryRoomController>()!
                            .roomAndLiveStatus,
                        builder: (context, value, child) {
                          return FunctionCard(
                            onTap: (event) {
                              controller.onTap(event);
                            },
                            noSignalAll: controller
                                .parent<SurgeryRoomController>()!
                                .noSignalAll(),
                            isRtcLiveOngoing: value.isRtcLiveOngoing,
                            isRtmpLiveOngoing: value.isRtmpLiveOngoing,
                            isVoicePrepared: controller
                                .parent<SurgeryRoomController>()!
                                .voicePrepare,
                          );
                        },
                      ),
                    OperationState.rtcLive => ValueListenableBuilder(
                        valueListenable: AppContext.share.networkConnected,
                        builder: (context, networkConnected, child) {
                          return ValueListenableBuilder(
                            valueListenable: controller.roomShareKey,
                            builder: (context, roomShareKey, child) {
                              return ValueListenableBuilder(
                                valueListenable: controller
                                    .parent<SurgeryRoomController>()!
                                    .doctors,
                                builder: (BuildContext context, doctors,
                                    Widget? child) {
                                  return RtcLiveCard(
                                    url: controller.roomShareUrl,
                                    doctors: doctors,
                                    isNetwork: networkConnected,
                                    onMicrophoneChange: (bool value) {
                                      controller
                                          .parent<SurgeryRoomController>()
                                          ?.setMicrophoneState(enabled: value);
                                    },
                                  );
                                },
                              );
                            },
                          );
                        },
                      ),
                    OperationState.rtmLive => ValueListenableBuilder(
                        valueListenable: AppContext.share.networkConnected,
                        builder: (context, networkConnected, child) {
                          return ValueListenableBuilder(
                            valueListenable: controller.liveShareKey,
                            builder: (context, liveShareKey, child) {
                              final vc =
                                  controller.parent<SurgeryRoomController>()!;
                              return ValueListenableBuilder(
                                  valueListenable: vc.roomAndLiveStatus,
                                  builder: (context, roomAndLiveStatus, child) {
                                    return ValueListenableBuilder(
                                      valueListenable: vc.liveUserCount,
                                      builder: (BuildContext context,
                                          liveUserCount, Widget? child) {
                                        return RtmpLiveCard(
                                          networkConnected: networkConnected,
                                          isLiving: roomAndLiveStatus
                                              .isRtmpLiveOngoing,
                                          url: controller.liveShareUrl,
                                          userCount: liveUserCount ?? 0,
                                          onLiveStart: () async {
                                            controller
                                                .parent<SurgeryRoomController>()
                                                ?.startLive();
                                          },
                                          onLiveEnd: () {
                                            controller
                                                .parent<SurgeryRoomController>()
                                                ?.stopLive();
                                          },
                                          onMicrophoneChange: (bool value) {
                                            controller
                                                .parent<SurgeryRoomController>()
                                                ?.setMicrophoneState(
                                                    enabled: value);
                                          },
                                        );
                                      },
                                    );
                                  });
                            },
                          );
                        },
                      ),
                    OperationState.control => AppContext.share.enableBleControl
                        ? ValueListenableBuilder(
                            valueListenable: AppContext.share.bleAdapterState,
                            builder: (context, networkConnected, child) {
                              return const BleScanner(
                                roomScan: true,
                              );
                            },
                          )
                        : ValueListenableBuilder(
                            valueListenable: AppContext.share.networkConnected,
                            builder: (context, networkConnected, child) {
                              return RemoteControlCard(
                                  isNetwork: networkConnected);
                            },
                          ),
                    OperationState.setting => const SettingCard(),
                  },
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 关闭手术提示
  void showStopSurgeryModal(BuildContext context) {
    String message = S.current.f_9y3YYSkr;
    bool noSignalAll =
        controller.parent<SurgeryRoomController>()!.noSignalAll();
    if (noSignalAll) {
      message = S.current.f_eHqNNdao;
    }
    Modal(
      title: S.current.f_9xFIMdDz,
      kind: ModalKind.dialog,
      type: ModalType.error,
      message: message,
      confirmText: S.current.f_9KZD5TVS,
      cancelText: S.current.f_9KZD45AL,
      onConfirm: () async {
        context.pop();
        controller.onTap(FunctionEvent.stop);
      },
    ).show(context, barrierDismissible: false);
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }
}
