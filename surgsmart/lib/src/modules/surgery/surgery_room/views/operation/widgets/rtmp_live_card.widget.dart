import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/surgery_room.controller.dart';
import 'package:surgsmart/src/tools/string_util.dart';
import 'package:surgsmart/src/widgets/no_network.widget.dart';

class RtmpLiveCard extends StatelessWidget {
  final bool networkConnected;

  final bool isLiving;

  final String? url;

  final int userCount;

  final VoidCallback onLiveStart;

  final VoidCallback onLiveEnd;

  final ValueChanged<bool> onMicrophoneChange;

  const RtmpLiveCard({
    super.key,
    required this.networkConnected,
    required this.url,
    required this.isLiving,
    required this.userCount,
    required this.onLiveStart,
    required this.onLiveEnd,
    required this.onMicrophoneChange,
  });

  @override
  Widget build(BuildContext context) {
    return isLiving
        ? _LiveCloseCard(
          networkConnected: networkConnected,
          url: url ?? "",
          userCount: userCount,
          onLiveEnd: onLiveEnd,
          onMicrophoneChange: onMicrophoneChange,
        )
        : _LiveCreateCard(
          networkConnected: networkConnected,
          onLiveStart: onLiveStart,
          onMicrophoneChange: onMicrophoneChange,
        );
  }
}

class _LiveCreateCard extends StatelessWidget {
  const _LiveCreateCard({
    required this.onLiveStart,
    required this.onMicrophoneChange,
    required this.networkConnected,
  });

  final bool networkConnected;

  final VoidCallback onLiveStart;

  final ValueChanged<bool> onMicrophoneChange;

  @override
  Widget build(BuildContext context) {
    return AppControllerBuilder<SurgeryRoomController>(
      builder: (context, controller) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(vertical: 14.h),
              child: Row(
                children: [
                  Container(
                    margin: EdgeInsets.only(left: 25.w),
                    padding: EdgeInsets.all(14.r),
                    decoration: BoxDecoration(
                      color: Colors.white10,
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        controller.setMicrophoneState(
                          enabled: !controller.microphoneIsOn.value,
                        );
                      },
                      child: ValueListenableBuilder(
                        valueListenable: controller!.microphoneIsOn,
                        builder: (context, value, child) {
                          return Image(
                            image:
                                value
                                    ? R.image.microphone_on()
                                    : R.image.microphone_off(),
                            width: 96.r,
                            height: 96.r,
                            fit: BoxFit.cover,
                          );
                        },
                      ),
                    ),
                  ),
                  SizedBox(width: 90.w),
                  if (networkConnected)
                    Text(
                      S.current.f_9aUbNeBF,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 80.sp,
                        fontWeight: FontWeight.w500,
                        height: 100 / 80,
                      ),
                    ),
                ],
              ),
            ),
            networkConnected
                ? Expanded(
                  child: Column(
                    children: [
                      SizedBox(height: 48.h),
                      Container(
                        width: 440.r,
                        height: 440.r,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          border: Border.all(color: const Color(0x73FFFFFF)),
                          borderRadius: BorderRadius.circular(8.r),
                          image: DecorationImage(image: R.image.qrcode_bg()),
                        ),
                        child: Image.asset(
                          R.image.what_mask().assetName,
                          width: 200.r,
                          height: 200.r,
                          fit: BoxFit.contain,
                        ),
                      ),
                      SizedBox(height: 48.h),
                      ValueListenableBuilder(
                        valueListenable: controller.isLiveStarting,
                        builder: (context, value, child) {
                          return Button(
                            onPressed: value ? null : onLiveStart,
                            backgroundColor:
                                value ? Colors.grey : const Color(0xFF3F97A8),
                            borderRadius: BorderRadius.circular(55.r),
                            padding: EdgeInsets.symmetric(
                              vertical: 16.h,
                              horizontal: 120.w,
                            ),
                            label: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  value
                                      ? S.current.f_bTSb5PcS
                                      : S.current.f_9aUb0BkK,
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 56.sp,
                                    fontWeight: FontWeight.w500,
                                    height: 78 / 56,
                                  ),
                                ),
                                if (value) ...[
                                  SizedBox(width: 30.w),
                                  const CircularProgressIndicator(
                                    color: Colors.white,
                                  ),
                                ],
                              ],
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                )
                : const Expanded(child: NoNetwork()),
          ],
        );
      },
    );
  }
}

class _LiveCloseCard extends StatelessWidget {
  const _LiveCloseCard({
    required this.networkConnected,
    required this.url,
    required this.userCount,
    required this.onLiveEnd,
    required this.onMicrophoneChange,
  });

  final bool networkConnected;

  final String url;

  final int userCount;

  final VoidCallback onLiveEnd;

  final ValueChanged<bool> onMicrophoneChange;

  @override
  Widget build(BuildContext context) {
    return AppControllerBuilder<SurgeryRoomController>(
      builder: (context, controller) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(vertical: 14.h),
              child: Row(
                children: [
                  Container(
                    margin: EdgeInsets.only(left: 25.w),
                    padding: EdgeInsets.all(14.r),
                    decoration: BoxDecoration(
                      color: Colors.white10,
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () async {
                        controller.setMicrophoneState(
                          enabled: !controller.microphoneIsOn.value,
                        );
                      },
                      child: ValueListenableBuilder(
                        valueListenable: controller!.microphoneIsOn,
                        builder: (context, value, child) {
                          return Image(
                            image:
                                value
                                    ? R.image.microphone_on()
                                    : R.image.microphone_off(),
                            width: 96.r,
                            height: 96.r,
                            fit: BoxFit.cover,
                          );
                        },
                      ),
                    ),
                  ),
                  SizedBox(width: 62.w),
                  if (networkConnected)
                    Text.rich(
                      TextSpan(
                        text: "$userCount",
                        style: GoogleFonts.roboto(
                          color: const Color(0xFF23D9BD),
                          fontSize: 96.sp,
                          fontWeight: FontWeight.w500,
                          height: 127 / 96,
                        ),
                        children: [
                          TextSpan(
                            text: StringUtil.replaceInterpolation(
                              S.current.f1_9aUbOLdy,
                              [''],
                            ),
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 80.sp,
                              fontWeight: FontWeight.w500,
                              height: 127 / 80,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
            networkConnected
                ? Expanded(
                  child: Column(
                    children: [
                      SizedBox(height: 24.h),
                      Stack(
                        children: [
                          QrImageView(
                            data: url,
                            size: 440.r,
                            padding: EdgeInsets.all(20.r),
                            backgroundColor: Colors.white,
                          ),
                          Positioned.fill(
                            child: Center(
                              child: Container(
                                width: 72.r,
                                height: 72.r,
                                padding: EdgeInsets.all(8.r),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(10.r),
                                ),
                                child: Image(image: R.image.company_logo()),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 24.h),
                      Text(
                        S.current.f_9aUb5Es2,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 48.sp,
                          height: 67 / 48,
                        ),
                      ),
                      SizedBox(height: 24.h),
                      ValueListenableBuilder(
                        valueListenable: controller.isLiveStopping,
                        builder: (context, value, child) {
                          return Button(
                            onPressed: value ? null : onLiveEnd,
                            backgroundColor:
                                value ? Colors.grey : const Color(0xFF3F97A8),
                            borderRadius: BorderRadius.circular(55.r),
                            padding: EdgeInsets.symmetric(
                              vertical: 16.h,
                              horizontal: 120.w,
                            ),
                            label: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  value ? "正在结束" : S.current.f_9aUbue0x,
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 56.sp,
                                    fontWeight: FontWeight.w500,
                                    height: 78 / 56,
                                  ),
                                ),
                                if (value) ...[
                                  SizedBox(width: 30.w),
                                  const CircularProgressIndicator(
                                    color: Colors.white,
                                  ),
                                ],
                              ],
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                )
                : const Expanded(child: NoNetwork()),
          ],
        );
      },
    );
  }
}
