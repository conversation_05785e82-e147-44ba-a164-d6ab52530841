import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';

class ScoreCard extends StatelessWidget {
  final int cvs1;
  final int cvs2;
  final int cvs3;
  final int? cvsAverage;

  const ScoreCard({
    super.key,
    this.cvs1 = 0,
    this.cvs2 = 0,
    this.cvs3 = 0,
    this.cvsAverage,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image(
              image: R.image.cvs_score(),
              width: 96.r,
              height: 96.r,
            ),
            SizedBox(width: 26.w),
            Text(
              S.current.f_8QlA6nQM,
              style: TextStyle(
                fontSize: 80.sp,
                fontWeight: FontWeight.w500,
                color: Colors.white,
                height: 100 / 80,
              ),
            ),
          ],
        ),
        Expanded(
          child: Padding(
            padding: EdgeInsets.all(26.r),
            child: cvsAverage == null
                ? _ScoringCard(cvs1: cvs1, cvs2: cvs2, cvs3: cvs3)
                : _ScoredCard(
                    cvs1: cvs1,
                    cvs2: cvs2,
                    cvs3: cvs3,
                    cvsAverage: cvsAverage!,
                  ),
          ),
        ),
      ],
    );
  }
}

class _ScoringCard extends StatelessWidget {
  final int cvs1;
  final int cvs2;
  final int cvs3;

  const _ScoringCard({
    this.cvs1 = 0,
    this.cvs2 = 0,
    this.cvs3 = 0,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _ItemTile(
          title: S.current.f_8UzGmgIp,
          score: cvs1,
          phase: "CVS I",
        ),
        _ItemTile(
          title: S.current.f_8UzGlvL2,
          score: cvs2,
          phase: "CVS II",
        ),
        _ItemTile(
          title: S.current.f_8UzGqreX,
          score: cvs3,
          phase: "CVS III",
        ),
      ],
    );
  }
}

class _ItemTile extends StatelessWidget {
  final String title;
  final int score;
  final String phase;
  const _ItemTile({
    required this.title,
    required this.score,
    required this.phase,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 215.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        border: Border.all(
          color: const Color(0xFF979797),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(
                top: 25.h,
                bottom: 30.h,
                left: 25.w,
                right: 25.w,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 64.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                      height: 90 / 64,
                    ),
                  ),
                  LinearProgressIndicator(
                    color: getColor(score),
                    value: score / 100,
                    minHeight: 32.r,
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                ],
              ),
            ),
          ),
          AspectRatio(
            aspectRatio: 1,
            child: Container(
              margin: EdgeInsets.all(8.r),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: Colors.white10,
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: Text.rich(
                textAlign: TextAlign.center,
                TextSpan(
                  text: "$score\n",
                  style: GoogleFonts.roboto(
                    color: getColor(score),
                    fontSize: 120.sp,
                    fontWeight: FontWeight.w500,
                    height: 1,
                  ),
                  children: [
                    TextSpan(
                      text: phase,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 49.sp,
                        height: 64 / 49,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _ScoredCard extends StatelessWidget {
  final int cvs1;
  final int cvs2;
  final int cvs3;
  final int cvsAverage;
  const _ScoredCard({
    required this.cvs1,
    required this.cvs2,
    required this.cvs3,
    required this.cvsAverage,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(30.r),
        border: Border.all(
          color: const Color(0xFF979797),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: 435.w,
            height: 407.h,
            alignment: Alignment.center,
            margin: EdgeInsets.only(top: 33.h),
            decoration: BoxDecoration(
              image: DecorationImage(
                image: R.image.cvs_average_background(),
                fit: BoxFit.cover,
              ),
            ),
            child: Text(
              "$cvsAverage",
              style: GoogleFonts.roboto(
                color: Colors.white,
                fontSize: 200.sp,
                fontWeight: FontWeight.w500,
                height: 1,
              ),
            ),
          ),
          Container(
            height: 222.h,
            padding: EdgeInsets.only(
              left: 75.w,
              right: 75.w,
              bottom: 24.h,
            ),
            child: Row(
              // mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                AspectRatio(
                  aspectRatio: 1,
                  child: Container(
                    margin: EdgeInsets.all(8.r),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: Colors.white10,
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: Text.rich(
                      textAlign: TextAlign.center,
                      TextSpan(
                          text: "$cvs1\n",
                          style: GoogleFonts.roboto(
                            color: getColor(cvs1),
                            fontSize: 120.sp,
                            fontWeight: FontWeight.w500,
                            height: 1,
                          ),
                          children: [
                            TextSpan(
                              text: "CVS I",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 49.sp,
                                height: 1,
                                wordSpacing: 20.w,
                              ),
                            ),
                          ]),
                    ),
                  ),
                ),
                AspectRatio(
                  aspectRatio: 1,
                  child: Container(
                    margin: EdgeInsets.all(8.r),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: Colors.white10,
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: Text.rich(
                      textAlign: TextAlign.center,
                      TextSpan(
                          text: "$cvs2\n",
                          style: GoogleFonts.roboto(
                            color: getColor(cvs2),
                            fontSize: 120.sp,
                            fontWeight: FontWeight.w500,
                            height: 1,
                          ),
                          children: [
                            TextSpan(
                              text: "CVS II",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 49.sp,
                                height: 1,
                                wordSpacing: 10.w,
                              ),
                            ),
                          ]),
                    ),
                  ),
                ),
                AspectRatio(
                  aspectRatio: 1,
                  child: Container(
                    margin: EdgeInsets.all(8.r),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: Colors.white10,
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: Text.rich(
                      textAlign: TextAlign.center,
                      TextSpan(
                        text: "$cvs3\n",
                        style: GoogleFonts.roboto(
                          color: getColor(cvs3),
                          fontSize: 120.sp,
                          fontWeight: FontWeight.w500,
                          height: 1,
                        ),
                        children: [
                          TextSpan(
                            text: "CVS III",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 49.sp,
                              height: 1,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// 匹配颜色
Color getColor(int score) {
  if (score <= 20) {
    return const Color(0xFFE8D597);
  } else if (score <= 50) {
    return const Color(0xFF5DC8DD);
  } else {
    return const Color(0xFF73D4A7);
  }
}
