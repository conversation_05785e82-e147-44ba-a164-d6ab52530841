import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'package:app_foundation/app_foundation.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/apis/mqtt/device_ai.api.dart';
import 'package:surgsmart/src/models/event_bus.model.dart';
import 'package:surgsmart/src/models/http/procedure.model.dart';
import 'package:surgsmart/src/models/mqtt/device_ai.model.dart';
import 'package:surgsmart/src/models/mqtt/event_count.model.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/surgery_room.controller.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';
import 'package:surgsmart/src/tools/event_bus.dart';

enum EventType { clamping, amputation, suturing, bleeding }

/// 所属模块: surgery
///
/// 所属路由: surgeryRoom
///
/// 算法数据面板
class AlgorithmController extends AppController with StateMixin<ApiModel> {
  final bool enableAdvanceAi;

  final int surgeryId;

  final OrgProcedureInfo procedure;

  AlgorithmController(
    super.key,
    super.routerState, {
    required this.surgeryId,
    required this.procedure,
    required this.enableAdvanceAi,
  });

  final events = <int, EventAttrInfo>{};

  final insightFullMap = <int, dynamic>{};

  int? _currentPhaseId;
  int? _currentPhaseDuration;

  final currentPhaseId = 0.emptyNotifier;
  final currentPhaseDuration = 0.emptyNotifier;

  final cvs = CvsInfo().initWith({}).notifier;

  final surgeryEvent = SurgeryEvent().emptyNotifier;

  // 监听切割闭合异常标记
  final staplingAnomaly = false.notifier;

  final firstBleeding = false.notifier;

  var bloodEventInsight = EventInsightInfo().initWith({});

  Timer? phaseTimer;

  MqttApiObserver? onCvsObserver;
  MqttApiObserver? onBodyObserver;
  MqttApiObserver? onPhaseObserver;
  MqttApiObserver? onEventCountObserver;

  @override
  void onInit() {
    update(LoadState.success(ApiModel()));
    phaseTimer ??= Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if ((enableAdvanceAi ||
              _currentPhaseId == 1110113001 ||
              _currentPhaseId == 1110113000) &&
          _currentPhaseId != null) {
        currentPhaseId.value = _currentPhaseId!;
        currentPhaseDuration.value = _currentPhaseDuration!;
      }
    });
  }

  @override
  void onReady() async {
    await loadData();

    /// 订阅本地算法 MQTT 消息
    subscriptLocalAlgorithmMessage();

    /// 出血状态改变监听
    // firstBleeding.addListener(() {
    //   if (firstBleeding.value) {
    //     parent<SurgeryRoomController>()
    //         ?.playAudio(fileName: "bleed_warning.wav.txt");
    //   }
    // });

    /// 切割闭合异常状态监听
    String? staplingRecord = AppPreferences.staplingRecord.stringValue;
    if (staplingRecord != null) {
      Map<String, dynamic> staplingMap = jsonDecode(staplingRecord);
      if (staplingMap['$surgeryId'] != true) {
        //断电重启情况已上报则不再上报
        staplingAnomaly.addListener(staplingChange);
      }
    } else {
      staplingAnomaly.addListener(staplingChange);
    }
  }

  @override
  void onClose() {
    onCvsObserver?.cancel();
    onBodyObserver?.cancel();
    onPhaseObserver?.cancel();
    onEventCountObserver?.cancel();
    phaseTimer?.cancel();

    currentPhaseId.dispose();
    currentPhaseDuration.dispose();
    cvs.dispose();
    surgeryEvent.dispose();
    firstBleeding.dispose();
    staplingAnomaly.removeListener(staplingChange);
    staplingAnomaly.dispose();
  }

  Future<void> staplingChange() async {
    if (staplingAnomaly.value) {
      String? staplingRecord = AppPreferences.staplingRecord.stringValue;
      Map<String, dynamic> staplingMap =
          staplingRecord != null ? jsonDecode(staplingRecord) : {};
      if (staplingMap['$surgeryId'] == true) return;
      staplingMap['$surgeryId'] = false;
      await AppPreferences.staplingRecord.setString(jsonEncode(staplingMap));
      eventBus.fire(EventBusInfo(type: EventBusType.staplingAnomaly));
    }
  }

  /// 加载数据
  Future<void> loadData() async {
    final jsonText = await R.text.insight_json();
    final insight = json.decode(jsonText);
    final eventList = insight['common']['event'];

    eventList.fold(events, (previousValue, element) {
      final eventAttr = EventAttrInfo().initWith(element);
      previousValue[eventAttr.value] = eventAttr;
      return previousValue;
    });

    insight.forEach((proc, featureMap) {
      featureMap.forEach((featureType, features) {
        for (var feature in features) {
          insightFullMap[feature["value"]] = {
            "procedure": proc,
            "feature": feature,
            "feature_type": featureType,
          };
        }
      });
    });
  }

  /// 订阅本地算法 MQTT 消息
  void subscriptLocalAlgorithmMessage() {
    onCvsObserver = MqttDeviceAiApi<CvsInfo>.onCvsMessage(
      surgeryId: surgeryId,
    ).listen((message) {
      if (!enableAdvanceAi) return;
      if (cvs.value.status == CvsStatus.stopped ||
          cvs.value.status == CvsStatus.expired) {
        return;
      }
      if (message.status == CvsStatus.stopped) {
        if (cvs.value.status != CvsStatus.ongoing) return;
        Future.delayed(30.seconds, () {
          final obj = cvs.value;
          obj.status = CvsStatus.expired;
          cvs.value = CvsInfo().initWith(obj.toMap());
        });
      }
      cvs.value = message;
    });

    onBodyObserver = MqttDeviceAiApi<EventOperationListInfo>.onBodyMessage(
      surgeryId: surgeryId,
    ).listen((message) {
      final currentBody =
          message.datas.where((value) => value.isHappening).firstOrNull;
      if (currentBody == null) return;
      if (isShowBodyTime()) {
        _currentPhaseId = currentBody.id;
      }
      _currentPhaseDuration = currentBody.duration.round();
      final isBodyout = currentBody.id == 1110113001;
      parent<SurgeryRoomController>()?.isBodyout.value = isBodyout;
    });

    onPhaseObserver = MqttDeviceAiApi<EventOperationListInfo>.onPhaseMessage(
      surgeryId: surgeryId,
    ).listen((message) {
      if (!enableAdvanceAi) return;
      final currentPhase =
          message.datas.where((value) => value.isHappening).firstOrNull;

      parent<SurgeryRoomController>()?.currentPhaseId.value = currentPhase?.id;
      if (currentPhase == null) return;
      _currentPhaseId = currentPhase.id;
      _currentPhaseDuration = currentPhase.duration.round();
    });

    onEventCountObserver = MqttDeviceAiApi<
      EventInsightListInfo
    >.onEventCountMessage(surgeryId: surgeryId).listen((message) {
      final surgeryEvent = SurgeryEvent();
      for (var eventInsight in message.datas) {
        final eventInfo = EventInfo(
          attr: events[eventInsight.id]!,
          insight: eventInsight,
        );
        if (eventInsight.id == 1110292001) {
          surgeryEvent.clamping = eventInfo;
        } else if (eventInsight.id == 1110293001 ||
            eventInsight.id == 1110294001 ||
            eventInsight.id == 1110295001) {
          // 切割闭合异常判断
          if (eventInsight.id == 1110294001) {
            ///肺部手术（标准手术代表中二三位为 “04” 的手术），10次才触发通知
            if (procedure.procedureConfig?.procedureCode?.indexOf('04') == 1) {
              if (eventInsight.count >= 10) {
                staplingAnomaly.value = true;
              }
            } else if (eventInsight.count >= 5) {
              staplingAnomaly.value = true;
            }
          }
          surgeryEvent.amputation = eventInfo;
        } else if (eventInsight.id == 1110291001) {
          surgeryEvent.suturing = eventInfo;
        } else if (eventInsight.id == 1110215001) {
          surgeryEvent.bleeding = eventInfo;
        } else {
          app.logW("------ unknown event: ${eventInsight.id}");
        }
      }
      this.surgeryEvent.value = surgeryEvent;
      //firstBleeding.value = surgeryEvent.bleeding?.insight.isHappening == true;
    });
  }

  bool isShowBodyTime() {
    return !(enableAdvanceAi &&
        (procedure.algorithmCode == "LC" || procedure.algorithmCode == "LPD"));
  }
}
