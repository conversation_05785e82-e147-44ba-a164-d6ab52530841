import 'dart:async';
import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/models/mqtt/event_count.model.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/algorithm/algorithm.controller.dart';
import 'package:surgsmart/src/tools/string_util.dart';

class DetectorCard extends StatelessWidget {
  final SurgeryEvent? eventCounts;
  final AlgorithmController controller;
  const DetectorCard({
    super.key,
    required this.eventCounts,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image(image: R.image.ai_detector(), width: 96.r, height: 96.r),
            SizedBox(width: 26.w),
            Text(
              S.current.f_8PBHzjOY,
              style: TextStyle(
                fontSize: 80.sp,
                fontWeight: FontWeight.w500,
                color: Colors.white,
                height: 100 / 80,
              ),
            ),
          ],
        ),
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(top: 40.h, left: 42.w, right: 42.w),
            child:
                controller
                            .procedure
                            .featureFlags
                            ?.isStaplerAmputationDisabled ==
                        true
                    ? robotDisplayEvent()
                    : commonDisplayEvent(),
          ),
        ),
      ],
    );
  }

  String? getDisplayLabel(EventInfo? event) {
    if (Intl.getCurrentLocale() == 'zh_CN') {
      return event?.attr.label;
    }
    //算法返回的英文名称按产品翻译替换，处理国际化后Ui展示问题
    if (1110294001 == event?.insight.id) {
      return 'Stapling';
    } else if (1110295001 == event?.insight.id) {
      return 'Transect';
    }
    return StringUtil.firstUpperCase(event?.attr.name);
  }

  Widget commonDisplayEvent() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      mainAxisSpacing: 0,
      crossAxisSpacing: 0,
      childAspectRatio: 420 / 328,
      children: [
        _ItemCard(
          count: eventCounts?.clamping?.insight.count ?? 0,
          desc: getDisplayLabel(eventCounts?.clamping) ?? S.current.f_8PBHfpse,
          showAnimation: eventCounts?.clamping?.insight.isHappening == true,
        ),
        _ItemCard(
          count: eventCounts?.amputation?.insight.count ?? 0,
          desc:
              getDisplayLabel(eventCounts?.amputation) ??
              (controller.procedure.algorithmCode == "LC"
                  ? S.current.f_8PBHW1wI
                  : S.current.f_8PBHArjG),
          showAnimation: eventCounts?.amputation?.insight.isHappening == true,
        ),
        _ItemCard(
          count: eventCounts?.suturing?.insight.count ?? 0,
          desc: getDisplayLabel(eventCounts?.suturing) ?? S.current.f_8PBHvlPz,
          showAnimation: eventCounts?.suturing?.insight.isHappening == true,
          duration: eventCounts?.suturing?.insight.duration ?? 0,
        ),
        _ItemCard(
          count: eventCounts?.bleeding?.insight.count ?? 0,
          desc: getDisplayLabel(eventCounts?.bleeding) ?? S.current.f_8PBHyAPp,
          showAnimation: eventCounts?.bleeding?.insight.isHappening == true,
        ),
      ],
    );
  }

  Widget robotDisplayEvent() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _ItemCard(
              count: eventCounts?.clamping?.insight.count ?? 0,
              desc:
                  getDisplayLabel(eventCounts?.clamping) ??
                  S.current.f_8PBHfpse,
              showAnimation: eventCounts?.clamping?.insight.isHappening == true,
            ),
            _ItemCard(
              count: eventCounts?.bleeding?.insight.count ?? 0,
              desc:
                  getDisplayLabel(eventCounts?.bleeding) ??
                  S.current.f_8PBHyAPp,
              showAnimation: eventCounts?.bleeding?.insight.isHappening == true,
            ),
          ],
        ),
        20.verticalSpace,
        _ItemCard(
          width: 856,
          count: eventCounts?.suturing?.insight.count ?? 0,
          desc: getDisplayLabel(eventCounts?.suturing) ?? S.current.f_8PBHvlPz,
          showAnimation: eventCounts?.suturing?.insight.isHappening == true,
          duration: eventCounts?.suturing?.insight.duration ?? 0,
        ),
      ],
    );
  }
}

class _ItemCard extends StatefulWidget {
  final int count;
  final String desc;
  final bool showAnimation;
  final int? duration;
  final double width;
  final double height = 328;

  const _ItemCard({
    required this.count,
    required this.desc,
    this.width = 420,
    this.showAnimation = false,
    this.duration,
  });

  @override
  State<_ItemCard> createState() => _ItemCardState();
}

class _ItemCardState extends State<_ItemCard> {
  Timer? timer;
  late String url;
  late var showAnimation = widget.showAnimation;
  var count = 3;

  @override
  void initState() {
    url =
        widget.width.w >= 856.w
            ? R.text.asset.event_detection_long_json.keyName
            : R.text.asset.event_detection_json.keyName;
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (count <= 0) {
        setState(() {
          showAnimation = false;
        });
      }
      count--;
    });
    super.initState();
  }

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant _ItemCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    setState(() {
      if (widget.showAnimation) {
        showAnimation = true;
        count = 3;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    var title = "${widget.count}";
    var style = GoogleFonts.roboto(
      fontSize: 160.sp,
      fontWeight: FontWeight.w500,
      color: Colors.white,
      height: 1,
    );
    if (widget.duration != null) {
      title = "00:00";
      title =
          widget.duration! >= 3600
              ? widget.duration!.toHMS
              : widget.duration!.toMS;
      style = GoogleFonts.roboto(
        fontSize: widget.duration! >= 3600 ? 96.sp : 120.sp,
        fontWeight: FontWeight.w500,
        color: Colors.white,
        height: 1,
      );
    }
    return SizedBox(
      width: widget.width.w,
      height: widget.height.h,
      child: Stack(
        children: [
          Container(
            width: widget.width.w,
            height: widget.height.h,
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.r),
              border:
                  widget.showAnimation
                      ? const Border()
                      : Border.all(color: const Color(0xFF979797)),
            ),
            child:
                !showAnimation
                    ? Container(
                      width: widget.width.w,
                      height: widget.height.h,
                      alignment: Alignment.center,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(title, style: style),
                          Text(
                            widget.desc,
                            style: TextStyle(
                              fontSize: 72.sp,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                              height: 1,
                            ),
                          ),
                        ],
                      ),
                    )
                    : Container(
                      width: widget.width.w,
                      height: widget.height.h,
                      color: const Color(0xff57B2E5).withValues(alpha: 0.2),
                      alignment: Alignment.center,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            S.current.f_8PBHtLNB,
                            style: TextStyle(
                              fontSize: 48.sp,
                              fontWeight: FontWeight.w400,
                              color: Colors.white,
                            ),
                          ),
                          Text(
                            widget.desc,
                            style: TextStyle(
                              fontSize: 92.sp,
                              fontWeight: FontWeight.w500,
                              color: const Color(0xff2CC796),
                            ),
                          ),
                        ],
                      ),
                    ),
          ),
          if (showAnimation)
            Positioned.fill(
              child: Lottie.asset(
                url,
                width: widget.width.w,
                height: widget.height.h,
                frameRate: const FrameRate(75),
              ),
            ),
        ],
      ),
    );
  }
}
