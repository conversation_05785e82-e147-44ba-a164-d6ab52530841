import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

///矩形框绘制组件
class DrawingRectangle extends StatelessWidget {
  final List<Offset>? points;
  final double drawWidth;
  final double drawHeight;
  final double strokeWidth;
  final Color color;
  final bool isDrawDashedLine;
  final bool isFillParent;

  const DrawingRectangle({
    super.key,
    this.points = const [Offset(0, 0)],
    this.drawWidth = 300,
    this.drawHeight = 300,
    this.strokeWidth = 8,
    this.color = Colors.yellow,
    this.isDrawDashedLine = true,
    this.isFillParent = false,
  });

  // @override
  // void didChangeDependencies() {
  //   super.didChangeDependencies();
  //   WidgetsBinding.instance.addPostFrameCallback((_) {
  //     final RenderBox renderBox = context.findRenderObject() as RenderBox;
  //     final size = renderBox.size;
  //     setState(() {
  //       constraintsWidth = size.width;
  //       constraintsHeight = size.height;
  //     });
  //   });
  // }

  // @override
  // void initState() {
  //   super.initState();
  //   测试代码
  //   Timer.periodic(100.milliseconds, (timer) {
  //     app.logD("drawX=$drawX, drawY=$drawY,constraintsWidth = $constraintsWidth,constraintsHeight = $constraintsHeight,drawWidth=$drawWidth,drawHeight=$drawHeight");
  //     if (isFillParent) {
  //       return;
  //     }
  //     setState(() {
  //       if (drawX > constraintsWidth - drawWidth) {
  //         drawX = 0;
  //       } else {
  //         drawX += 10;
  //       }

  //       if (drawY > constraintsHeight - drawHeight) {
  //         drawY = 0;
  //       } else {
  //         drawY += 10;
  //       }
  //     });
  //   });
  // }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      double dWidth = drawWidth.w;
      double dHeight = drawHeight.w;
      if (isFillParent) {
        dWidth = constraints.maxWidth;
        dHeight = constraints.maxHeight;
      }
      return CustomPaint(
        size: Size(dWidth, dHeight),
        painter: DashedRectanglePainter(
          drawWidth: dWidth,
          drawHeight: dHeight,
          constraintsHeight: constraints.maxHeight,
          constraintsWidth: constraints.maxWidth,
          color: color,
          strokeWidth: strokeWidth,
          isDrawDashedLine: isDrawDashedLine,
          isFillParent: isFillParent,
          points: points,
        ),
      );
    });
  }
}

class DashedRectanglePainter extends CustomPainter {
  final List<Offset>? points;

  final double constraintsWidth;
  final double constraintsHeight;
  final double drawWidth;
  final double drawHeight;
  final double strokeWidth;
  final Color color;
  final bool isDrawDashedLine;
  final bool isFillParent;

  DashedRectanglePainter({
    this.points = const [Offset(0, 0)],
    this.drawWidth = 300,
    this.drawHeight = 300,
    this.constraintsHeight = 0,
    this.constraintsWidth = 0,
    this.strokeWidth = 8,
    this.color = Colors.yellow,
    this.isDrawDashedLine = false,
    this.isFillParent = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    points?.forEach((element) {
      double dx = element.dx * constraintsWidth - drawWidth / 2;
      double dy = element.dy * constraintsHeight - drawHeight / 2;
      final rect = Rect.fromLTWH(isFillParent ? element.dx : dx,
          isFillParent ? element.dy : dy, drawWidth, drawHeight);
      drawRect(rect, canvas);
    });
  }

  void _drawDashedLine(
      Canvas canvas, Offset start, Offset end, Paint paint, bool isLandscape) {
    const double dashWidth = 10.0;
    const double dashSpace = 10.0;
    double startX = start.dx;
    double startY = start.dy;

    if (isLandscape) {
      while (startX < end.dx) {
        canvas.drawLine(
            Offset(startX, startY), Offset(startX + dashWidth, startY), paint);
        startX += dashWidth + dashSpace;
      }
    } else {
      while (startY < end.dy) {
        canvas.drawLine(
            Offset(startX, startY), Offset(startX, startY + dashWidth), paint);
        startY += dashWidth + dashSpace;
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }

  void drawRect(Rect rect, Canvas canvas) {
    final solidPaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    //实线宽度
    double solidLineWidth = isFillParent ? 80 : drawWidth / 4;
    double patchOffset = strokeWidth / 2; //边角修正
    // 实线绘制
    canvas.drawLine(Offset(rect.left, rect.top - patchOffset),
        Offset(rect.left, rect.top + solidLineWidth), solidPaint);
    canvas.drawLine(Offset(rect.right, rect.top - patchOffset),
        Offset(rect.right, rect.top + solidLineWidth), solidPaint);
    canvas.drawLine(Offset(rect.left, rect.bottom + patchOffset),
        Offset(rect.left, rect.bottom - solidLineWidth), solidPaint);
    canvas.drawLine(Offset(rect.right, rect.bottom + patchOffset),
        Offset(rect.right, rect.bottom - solidLineWidth), solidPaint);

    canvas.drawLine(
        rect.topLeft, Offset(rect.left + solidLineWidth, rect.top), solidPaint);
    canvas.drawLine(rect.topRight,
        Offset(rect.right - solidLineWidth, rect.top), solidPaint);
    canvas.drawLine(rect.bottomLeft,
        Offset(rect.left + solidLineWidth, rect.bottom), solidPaint);
    canvas.drawLine(rect.bottomRight,
        Offset(rect.right - solidLineWidth, rect.bottom), solidPaint);

    //虚线绘制
    if (isDrawDashedLine) {
      final dashedPaint = Paint()
        ..color = color
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;

      _drawDashedLine(canvas, Offset(rect.left + solidLineWidth, rect.top),
          Offset(rect.right - solidLineWidth, rect.top), dashedPaint, true);
      _drawDashedLine(canvas, Offset(rect.left + solidLineWidth, rect.bottom),
          Offset(rect.right - solidLineWidth, rect.bottom), dashedPaint, true);
      _drawDashedLine(canvas, Offset(rect.left, rect.top + solidLineWidth),
          Offset(rect.left, rect.bottom - solidLineWidth), dashedPaint, false);
      _drawDashedLine(canvas, Offset(rect.right, rect.top + solidLineWidth),
          Offset(rect.right, rect.bottom - solidLineWidth), dashedPaint, false);
    }
  }
}
