import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/src/widgets/smart_voice.widget.dart';

class VoiceUserSelector extends StatelessWidget {
  final String hint;
  final List<String> labels;
  final int? selectedIndex;

  const VoiceUserSelector({
    super.key,
    required this.labels,
    required this.hint,
    this.selectedIndex,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      borderRadius: BorderRadius.circular(30.r),
      child: Container(
        width: 2702.w,
        height: 940.h,
        padding:
            EdgeInsets.only(left: 190.w, right: 190.w, top: 34.h, bottom: 80.h),
        decoration: BoxDecoration(
          color: const Color(0xFF1D2632),
          border: Border.all(
            width: 8.w,
            color: const Color(0xFF313E50),
          ),
          borderRadius: BorderRadius.circular(30.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            //这里暂时快速处理，后续需要优化
            SizedBox(
              width: hint.length > 15
                  ? 1300.w
                  : hint.length < 9
                      ? 700.w
                      : 1000.w,
              child: SmartVoice(
                state: selectedIndex != null
                    ? DisplayState.success
                    : DisplayState.listening,
                maxContentLength: 100,
                content: hint,
                showBottomDynamicEffect: false,
              ),
            ),
            60.verticalSpace,
            Expanded(
              child: ScrollConfiguration(
                behavior: const MaterialScrollBehavior().copyWith(
                  physics: const BouncingScrollPhysics(),
                  dragDevices: {
                    PointerDeviceKind.touch,
                    PointerDeviceKind.mouse,
                  },
                ),
                child: GridView.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    mainAxisSpacing: 50.w,
                    crossAxisSpacing: 50.h,
                    crossAxisCount: 3,
                    childAspectRatio: 740 / 235,
                  ),
                  itemCount: labels.length,
                  itemBuilder: (BuildContext context, int index) {
                    return _ItemCard(
                        index: "${index + 1}",
                        label: labels[index],
                        selected: index == selectedIndex);
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ItemCard<T> extends StatelessWidget {
  final String index;
  final String label;
  final bool selected;

  const _ItemCard(
      {required this.index, required this.label, this.selected = false});

  @override
  Widget build(BuildContext context) {
    return Container(
        width: double.infinity,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
                width: 8.w, color: Color(selected ? 0xFF0EC6D2 : 0xFF313E50))),
        child: Row(
          children: [
            Flexible(
              flex: 1,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: selected
                        ? [
                            Color(0xFF00CBDA),
                            Color(0xFF0261A5),
                          ]
                        : [
                            Color(0x33FFFFFF),
                            Color(0x33FFFFFF),
                          ],
                  ),
                ),
                child: Center(
                  child: Text(
                    index,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 100.sp,
                    ),
                  ),
                ),
              ),
            ),
            Flexible(
              flex: 3,
              child: Container(
                width: double.infinity,
                color: selected ? Color(0x330EC6D2) : null,
                child: Center(
                  child: Text(
                    label,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 72.sp,
                    ),
                  ),
                ),
              ),
            )
          ],
        ));
  }
}
