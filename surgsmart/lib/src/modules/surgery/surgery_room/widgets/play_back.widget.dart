import 'dart:async';
import 'dart:io';
import 'package:medias_kit/core/medias_kit_ffi.dart' as ffi;
import 'package:app_foundation/app_foundation.dart';
import 'package:blur/blur.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/models/http/room.model.dart';
import 'package:surgsmart/src/models/mqtt/device_ai.model.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/widgets/drawing_rectangle.widget.dart';
import 'package:surgsmart/src/tools/device_cmd.dart';
import 'package:surgsmart/src/tools/throttle_control.dart';
import 'package:surgsmart/src/tools/toast.dart';
import 'package:surgsmart/src/widgets/video_player.widget.dart';

//算法数据30帧，读取坐标按照30帧绘制
final int frameInterval = (1000 / 30).round();

/// 回放视频组合控件
class SurgPlayback extends StatefulWidget {
  final PlaybackInfo playbackInfo;
  final double? rate;
  final Function()? close;
  final bool showProgressBar;

  const SurgPlayback({
    super.key,
    required this.playbackInfo,
    this.rate,
    this.close,
    this.showProgressBar = false,
  });

  @override
  SurgPlaybackState createState() => SurgPlaybackState();
}

class SurgPlaybackState extends State<SurgPlayback> {
  List<Offset>? points;
  VideoController? _videoController;
  Timer? _offsetTimer;
  Timer? _drawTimer;
  Timer? _framePlayTimer;

  double currentSchedule = 0;
  double totalDuration = 0;
  double _rate = 1.0;
  late PlaybackInfo playbackInfo;

  bool showProgressBar = false;
  bool playOrPauseStarting = false; //是否正在启动播放或暂停
  bool videoFrameGetting = false; //是否正在获取视频帧

  final int frameStep = 34;
  String frameSaveDirectory = '';
  String currentFrameSaveName = '';

  StreamSubscription? playStateSubscription;
  StreamSubscription? positionStateSubscription;

  Function(List<Offset> points)? onBleedingPointsUpdate;

  final drawNotify = false.notifier;
  final processNotify = false.notifier;

  final TransformationController _interactiveController =
      TransformationController();
  bool isFrameMode = false; //是否逐帧模式
  bool isFramePlay = false; //是否逐帧播放
  bool isManualPause = false; //是否手动暂停
  bool isSeeking = false; //是否正在拖动进度条
  Timer? _controlDisplayTimer; //进度条显示控制器
  int lastClickTime = 0; //上次操作时间

  File? frameFile;

  int videoWidth = 0;
  int videoHeight = 0;

  double _realScale = 1.0;
  int? _renderAddress;

  @override
  void initState() {
    if (widget.playbackInfo.videoInfo.isEmpty) return;
    _rate = widget.rate ?? 1.0;
    showProgressBar = widget.showProgressBar;
    int index = widget.playbackInfo.videoInfo.first.path.lastIndexOf('/');

    if (index != -1) {
      frameSaveDirectory = widget.playbackInfo.videoInfo.first.path.substring(
        0,
        index + 1,
      );
    }

    List<MergeVideoInfo> videoInfo = List<MergeVideoInfo>.from(
      widget.playbackInfo.videoInfo,
    );
    playbackInfo = PlaybackInfo(
      playbackType: widget.playbackInfo.playbackType,
      videoInfo: videoInfo,
      bleedingPoint:
          widget.playbackInfo.bleedingPoint != null
              ? BleedingPoint().initWith(
                widget.playbackInfo.bleedingPoint!.toMap(),
              )
              : null,
      offsetMs: widget.playbackInfo.offsetMs,
      startPlayMs: widget.playbackInfo.startPlayMs,
    );

    totalDuration = playbackInfo.getTotalDuration().toDouble();
    if (widget.showProgressBar &&
        playbackInfo.playbackType == PlaybackType.videoPlay) {
      lastClickTime = DateTime.now().millisecondsSinceEpoch;
      _controlDisplayTimer = Timer.periodic(const Duration(seconds: 1), (
        timer,
      ) {
        if (DateTime.now().millisecondsSinceEpoch - lastClickTime > 5000) {
          showProgressBar = false;
          processChange();
        }
      });
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        InteractiveViewer(
          transformationController: _interactiveController,
          minScale: 1.0,
          maxScale: 3.0,
          onInteractionUpdate: (scaleUpdateDetails) {
            _realScale = scaleUpdateDetails.scale;
            app.logW("realScale: $_realScale");
          },
          onInteractionEnd: (scaleEndDetails) {
            setState(() {});
          },
          child: GestureDetector(
            onTap: clickRecord,
            child: VideoWidget(
              path: playbackInfo.getVideoPaths(),
              loop: true,
              rate: _rate,
              playListener: onPlayListener,
            ),
          ),
        ),
        Positioned(
          left: 35.w,
          top: 32.h,
          right: 39.w,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if (getVideoTitle().isNotEmpty)
                Text(
                  getVideoTitle(),
                  style: TextStyle(
                    fontSize: 52.sp,
                    height: 67 / 52,
                    fontWeight: FontWeight.w500,
                  ),
                ).frosted(
                  frostColor: const Color(0xFF777777),
                  blur: 10,
                  borderRadius: BorderRadius.circular(10.r),
                  padding: EdgeInsets.symmetric(
                    vertical: 30.h,
                    horizontal: 42.w,
                  ),
                ),
              if (widget.close != null)
                Button(
                  onPressed: () {
                    getPlayer()?.stop();
                    widget.close?.call();
                  },
                  icon: Icon(Icons.close, size: 56.r, color: Colors.white),
                  label: Text(
                    S.current.f_pFbaCUzb,
                    style: TextStyle(fontSize: 52.sp),
                  ),
                  backgroundColor: Color(0x99090909),
                  borderRadius: BorderRadius.circular(80.r),
                  padding: EdgeInsets.only(
                    left: 45.w,
                    right: 45.w,
                    top: 30.h,
                    bottom: 30.h,
                  ),
                  betweenSpace: 20.w,
                ),
            ],
          ),
        ),
        if (playbackInfo.playbackType == PlaybackType.bleedPlay &&
            _realScale == 1.0 &&
            !isFrameMode)
          ValueListenableBuilder(
            valueListenable: drawNotify,
            builder: (context, bleedPlayControl, child) {
              return DrawingRectangle(points: points);
            },
          ),

        ValueListenableBuilder(
          valueListenable: processNotify,
          builder: (context, value, child) {
            if (!showProgressBar) {
              return const SizedBox.shrink();
            }
            return Stack(
              children: [
                Positioned(
                  width: 320.w,
                  height: 180.h,
                  left: getLeftPosition(),
                  bottom: 270.h,
                  child: Container(
                    decoration:
                        showProgressFrame() && frameFile != null
                            ? BoxDecoration(
                              border: Border.all(color: Colors.black, width: 4),
                              borderRadius: BorderRadius.circular(10),
                              image: DecorationImage(
                                image: ResizeImage(
                                  FileImage(frameFile!),
                                  width: 320.w.toInt(),
                                  height: 180.h.toInt(),
                                ),
                                fit: BoxFit.fill,
                              ),
                            )
                            : null,
                  ),
                ),
                Positioned(
                  bottom: 36.h,
                  left: 36.w,
                  right: 36.w,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SliderTheme(
                        data: SliderTheme.of(context).copyWith(
                          trackHeight: 32.w,
                          thumbShape: RoundSliderThumbShape(
                            enabledThumbRadius: 45.r,
                          ),
                          valueIndicatorTextStyle: TextStyle(fontSize: 50.sp),
                          //valueIndicatorShape: CustomSliderValueIndicatorShape(),
                        ),
                        child: Slider(
                          value: currentSchedule,
                          min: 0,
                          max: totalDuration,
                          activeColor: Colors.white,
                          inactiveColor: Color(0x73FFFFFF),
                          thumbColor: Color(0xFF0EC6D2),
                          divisions: 100,
                          onChanged: (value) {
                            isSeeking = true;
                            currentSchedule = value;
                            clickRecord(reFresh: false);
                            processChange();
                            //查找当前时间点对应的帧
                            loadVideoFrame(currentSchedule);
                          },
                          onChangeEnd: (value) {
                            seekTo(value, isDragging: true);
                          },
                        ),
                      ),
                      20.verticalSpace,
                      Row(
                        children: [
                          10.horizontalSpace,
                          GestureDetector(
                            behavior: HitTestBehavior.opaque,
                            onTap: () {
                              clickRecord();
                              if (playOrPauseStarting) return;
                              if (isFrameMode) return;
                              if (isFramePlay) {
                                if (_framePlayTimer?.isActive == true) {
                                  isManualPause = true;
                                  cancelFramePlay();
                                } else {
                                  isManualPause = false;
                                  playFrame();
                                }
                                processChange();
                                return;
                              }
                              if (getPlayer()?.state.playing == true) {
                                pause(isManual: true);
                              } else {
                                play();
                              }
                              playOrPauseStarting = true;
                              Future.delayed(Duration(milliseconds: 100)).then((
                                val,
                              ) {
                                playOrPauseStarting = false;
                                processChange();
                              });
                            },
                            child: Padding(
                              padding: EdgeInsets.all(10),
                              child: Image(
                                width: 90.r,
                                height: 90.r,
                                fit: BoxFit.fill,
                                image:
                                    isFrameMode
                                        ? R.image.video_pause_gray()
                                        : (getPlayer()?.state.playing == true ||
                                            _framePlayTimer?.isActive == true)
                                        ? R.image.video_playing()
                                        : R.image.video_pause(),
                              ),
                            ),
                          ),
                          20.horizontalSpace,
                          Text(
                            (currentSchedule ~/ 1000).toHMS,
                            style: TextStyle(
                              fontSize: 48.sp,
                              color: Colors.white,
                            ),
                          ),
                          Text(
                            '/${(totalDuration ~/ 1000).toHMS}',
                            style: TextStyle(
                              fontSize: 48.sp,
                              color: Color(0x73FFFFFF),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  void processChange() {
    processNotify.value = !processNotify.value;
  }

  @override
  void dispose() {
    cancelDraw();
    playStateSubscription?.cancel();
    positionStateSubscription?.cancel();
    _controlDisplayTimer?.cancel();
    // if (_renderAddress != null) {
    //   getController()?.removeRender(render: _renderAddress!);
    // }
    getPlayer()?.dispose();

    super.dispose();
  }

  void cancelDraw() {
    _offsetTimer?.cancel();
    _drawTimer?.cancel();
  }

  VideoController? getController() {
    return _videoController;
  }

  Player? getPlayer() {
    return _videoController?.player;
  }

  void setBleedingPointsDrawListener(Function(List<Offset> points) listener) {
    onBleedingPointsUpdate = listener;
  }

  void onPlayListener(VideoController controller) {
    _videoController = controller;

    getPlayer()!.stream.width.listen((width) {
      videoWidth = width ?? 0;
    });
    getPlayer()!.stream.height.listen((height) {
      videoHeight = height ?? 0;
    });

    if (playbackInfo.playbackType == PlaybackType.bleedPlay) {
      onBleedingDraw();
      playStateSubscription = getPlayer()?.stream.completed.listen((
        isCompleted,
      ) {
        //播放结束监听
        if (isCompleted) {
          onBleedingDraw();
        }
      });
    } else if (playbackInfo.playbackType == PlaybackType.surgPlay) {
      if (playbackInfo.startPlayMs > 0 &&
          playbackInfo.startPlayMs < totalDuration) {
        Future.delayed(
          Duration(
            milliseconds: playbackInfo.videoInfo.length == 1 ? 500 : 200,
          ),
        ).then((_) {
          seekTo(playbackInfo.startPlayMs.toDouble());
          Future.delayed(Duration(milliseconds: 1500)).then((_) {
            if (currentSchedule > 3000) {
              ToastUtils.showToast(
                app.context,
                message: '已为您自动跳转至3分钟前回放',
                type: ToastType.success,
              );
            }
          });
        });
      }
    }

    if (widget.showProgressBar) {
      positionStateSubscription = getPlayer()?.stream.position.listen((
        Duration position,
      ) {
        if (!Throttle().checkPass("positionStream") || isSeeking) {
          return;
        }
        int currentPlayListIndex = getPlayer()!.state.playlist.index;

        int tempDuration = 0;
        for (int i = 0; i < currentPlayListIndex; i++) {
          tempDuration += playbackInfo.videoInfo[i].duration;
        }
        currentSchedule = tempDuration + position.inMilliseconds.toDouble();
        if (currentSchedule > totalDuration) {
          currentSchedule = totalDuration;
        }
        processChange();
      });
    }
  }

  void addRender(int address) {
    _renderAddress = address;
    getController()?.addRender(
      render: address,
      renderFunction: ffi.addresses.textureRendererRenderRgbaFrame.address,
    );
  }

  void onBleedingDraw() {
    if (playbackInfo.bleedingPoint == null) {
      return;
    }
    cancelDraw();

    int offsetTime = playbackInfo.offsetMs;
    if (offsetTime < 0) {
      return;
    }
    //int offsetMs = ((1 / widget.rate * offsetTime) - (1 / widget.rate * frameInterval)).toInt();
    _offsetTimer = Timer(
      Duration(milliseconds: (1 / _rate * offsetTime).toInt()),
      () {
        startDraw();
      },
    );
  }

  void startDraw() {
    int index = 0;
    int drawMs = (1 / _rate * frameInterval).toInt();
    _drawTimer = Timer.periodic(Duration(milliseconds: drawMs), (timer) {
      //3s视频最大误差在30ms
      if (index < playbackInfo.bleedingPoint!.positions.length) {
        List<PointsData> data = playbackInfo.bleedingPoint!.positions[index];
        if (data.isEmpty) {
          index++;
          return;
        }
        points = [];
        for (var element in data) {
          points!.add(Offset(element.x, element.y));
        }
        drawNotify.value = !drawNotify.value;
        index++;
        onBleedingPointsUpdate?.call(points!);
      } else {
        cancelDraw();
      }
    });
  }

  void seekTo(double value, {bool isDragging = false}) async {
    if (getPlayer() == null) return;
    if (value >= totalDuration - (frameStep * 2)) {
      value -= (frameStep * 2);
    }
    bool isPlaying = getPlayer()!.state.playing;
    isSeeking = true;
    int seekDuration = value.toInt();
    bool isDelayedSeek = false;
    if (playbackInfo.videoInfo.length > 1) {
      int tempDuration = 0;
      int videoIndex = 0;
      for (var info in playbackInfo.videoInfo) {
        tempDuration += info.duration;
        if (value > tempDuration) {
          videoIndex++;
          seekDuration = (value - tempDuration).toInt();
        } else {
          break;
        }
      }
      if (getPlayer()!.state.playlist.index != videoIndex) {
        await getPlayer()?.jump(videoIndex);
        isDelayedSeek = true;
      }
    }

    if (!isDelayedSeek) {
      await getPlayer()?.seek(Duration(milliseconds: seekDuration));
    }

    if (isDragging) {
      int runDuration = 0;
      Timer.periodic(Duration(milliseconds: 100), (timer) {
        if (!isPlaying) {
          //维持拖动进度前状态
          pause();
        }
        runDuration += 100;
        if (runDuration == 1000) {
          timer.cancel();
          isSeeking = false;
          processChange();
        } else if (runDuration == 500) {
          if (isDelayedSeek) {
            getPlayer()?.seek(Duration(milliseconds: seekDuration));
            isDelayedSeek = false;
          }
        }
      });
    } else {
      isSeeking = false;
      processChange();
      if (isDelayedSeek) {
        Future.delayed(Duration(milliseconds: 500)).then((_) {
          getPlayer()?.seek(Duration(milliseconds: seekDuration));
        });
        isDelayedSeek = false;
      }
    }
  }

  void playFrame() {
    cancelFramePlay();
    pause();
    isFramePlay = true;
    if (isManualPause) return; //手动暂停不播放
    _framePlayTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      nextFrame();
    });
  }

  void cancelFramePlay() {
    _framePlayTimer?.cancel();
    _framePlayTimer = null;
  }

  void pause({bool isManual = false}) {
    if (getPlayer()?.state.playing == true) {
      isManualPause = isManual;
      getPlayer()?.pause();
    }
  }

  void frameMode(bool enable) {
    isFrameMode = enable;
    if (enable) {
      cancelFramePlay();
      pause();
    } else {
      if (isFramePlay) {
        playFrame();
      } else if (!isManualPause) {
        play();
      }
    }
    processChange();
  }

  void play() {
    if (getPlayer()?.state.playing != true) {
      getPlayer()?.play();
      isManualPause = false;
    }
  }

  void setRate(double value) {
    cancelFramePlay();
    if (isFramePlay) {
      isFramePlay = false;
      if (!isManualPause) {
        play();
      }
    }
    _rate = value;
    getPlayer()?.setRate(value);
  }

  double getRate() {
    return _rate;
  }

  // 设置缩放比例并以某点为中心进行缩放
  void zoomInAtPoint(Offset point, double scaleValue) {
    Matrix4 newMatrix =
        Matrix4.identity()
          ..scale(scaleValue, scaleValue, 1.0)
          ..translate(
            -(scaleValue - 1) * point.dx,
            -(scaleValue - 1) * point.dy,
          );
    _interactiveController.value = newMatrix;
  }

  void setScale(double value) {
    //中心缩放
    // RenderBox renderBox = context.findRenderObject() as RenderBox;
    // app.logW(
    //     "宽高：${renderBox.size.width} ${renderBox.size.height}"); //1910.0 1052.0
    //zoomInAtPoint(Offset(renderBox.size.width / 2, renderBox.size.height / 2), value);
    _realScale = value;
    _interactiveController.value = Matrix4.identity()..scale(value);
    setState(() {});
  }

  double getRealScale() {
    return _realScale;
  }

  bool previousFrame() {
    if (currentSchedule < frameStep) {
      if (currentSchedule < 0) {
        currentSchedule = frameStep - 1;
      }
      return false;
    }
    currentSchedule -= frameStep;
    seekTo(currentSchedule);
    return true;
  }

  bool nextFrame() {
    currentSchedule += frameStep;
    if (currentSchedule >= totalDuration - (frameStep * 2)) {
      currentSchedule -= frameStep;
      return false;
    }
    seekTo(currentSchedule);
    return true;
  }

  String getVideoTitle() {
    return playbackInfo.playbackType == PlaybackType.bleedPlay
        ? S.current.f_9sEXBBDt
        : playbackInfo.playbackType == PlaybackType.surgPlay
        ? S.current.f_9qcBeYzP
        : '';
  }

  void clickRecord({bool reFresh = true}) {
    if (widget.showProgressBar &&
        playbackInfo.playbackType == PlaybackType.videoPlay) {
      showProgressBar = true;
      lastClickTime = DateTime.now().millisecondsSinceEpoch;
      if (reFresh) {
        processChange();
      }
    }
  }

  void loadVideoFrame(double position) async {
    if (videoFrameGetting) return;
    int currentVideoIndex = 0;
    int seekDuration = position.toInt();
    if (playbackInfo.videoInfo.length > 1) {
      int tempDuration = 0;
      for (var info in playbackInfo.videoInfo) {
        tempDuration += info.duration;
        if (position > tempDuration) {
          currentVideoIndex++;
          seekDuration = (position - tempDuration).toInt();
        } else {
          break;
        }
      }
    }

    try {
      videoFrameGetting = true;
      String frameSaveName = 'frame_$position.jpg';
      await DeviceCmd.share.getVideoFrame(
        videoPath: playbackInfo.videoInfo[currentVideoIndex].path,
        framePath: frameSaveDirectory + frameSaveName,
        seconds: seekDuration ~/ 1000,
      );

      currentFrameSaveName = frameSaveName;
      frameFile = File(frameSaveDirectory + currentFrameSaveName);
      processChange();
    } catch (e) {
      app.logE("视频帧获取出错：${e.toString()}");
    } finally {
      videoFrameGetting = false;
    }
  }

  double getLeftPosition() {
    double startMargin = 36.w + 24.w;
    double startPosition = 160.w;
    double totalWidth = 1778.w;
    double total = totalWidth - (startPosition * 2);
    double startDuration = totalDuration * (startPosition / totalWidth);
    double currentPosition =
        (currentSchedule - startDuration) /
        (totalDuration - (startDuration * 2));
    double left = (currentPosition * total) + startMargin;
    return left;
  }

  String getProcessDuration() {
    return '${(currentSchedule ~/ 1000).toHMS}/${(totalDuration ~/ 1000).toHMS}';
  }

  bool showProgressFrame() {
    return currentSchedule / totalDuration > 0.1 &&
        currentSchedule / totalDuration < 0.9 &&
        isSeeking;
  }
}
