import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/src/models/mqtt/interactive.model.dart';
import 'package:surgsmart/src/tools/app_context.dart';

enum DrawingBoardType { finger, line, fingerLine }

class DrawingBoard extends StatefulWidget {
  final List<UserGraphic> userGraphics;
  final DrawingBoardType drawingBoardType;
  final bool showSmallFinger;

  const DrawingBoard({
    super.key,
    required this.userGraphics,
    this.drawingBoardType = DrawingBoardType.fingerLine,
    this.showSmallFinger = true,
  });

  @override
  State<DrawingBoard> createState() => _DrawingBoardState();
}

class _DrawingBoardState extends State<DrawingBoard> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _DrawingBoardPainter(
        userGraphics: widget.userGraphics,
        drawingBoardType: widget.drawingBoardType,
        showSmallFinger: widget.showSmallFinger,
      ),
    );
  }
}

class _DrawingBoardPainter extends CustomPainter {
  final DrawingBoardType drawingBoardType;
  List<UserGraphic> userGraphics;
  final bool showSmallFinger;

  _DrawingBoardPainter({
    required this.userGraphics,
    required this.drawingBoardType,
    required this.showSmallFinger,
  }) : super();
  @override
  void paint(Canvas canvas, Size size) {
    for (var userGraphic in userGraphics) {
      final cursor =
          drawingBoardType == DrawingBoardType.line ? null : userGraphic.cursor;
      final graphics =
          drawingBoardType != DrawingBoardType.finger
              ? userGraphic.visible
              : [];

      ui.Image? image =
          drawingBoardType == DrawingBoardType.line
              ? null
              : showSmallFinger
              ? AppContext.share.fingerSmall
              : AppContext.share.fingerLarge;
      String? name =
          drawingBoardType == DrawingBoardType.line ? null : userGraphic.name;

      for (var graphic in graphics) {
        if (graphic.points.isNotEmpty) {
          final origin = graphic.points.first;
          final path = Path();
          path.moveTo(
            origin.offset.dx * size.width,
            origin.offset.dy * size.height,
          );
          for (var point in graphic.points) {
            path.lineTo(
              point.offset.dx * size.width,
              point.offset.dy * size.height,
            );
          }
          final paint =
              Paint()
                ..color = origin.color
                ..style = PaintingStyle.stroke
                ..strokeWidth = origin.strokeWidth * size.width
                ..strokeCap = StrokeCap.round
                ..strokeJoin = StrokeJoin.round;
          canvas.drawPath(path, paint);
        }
      }
      if (cursor != null && image != null) {
        double realCursorX = cursor.offset.dx * size.width;
        double realCursorY = cursor.offset.dy * size.height;
        canvas.drawImage(image, Offset(realCursorX, realCursorY), Paint());
        const textColor = Colors.white;
        //final fontSize = 74.sp;
        final referSize = image.width / 4;
        const backgroundColor = Color(0xff2a69e9);
        const paintingStyle = PaintingStyle.fill;
        final width = 2.w;
        final padding = EdgeInsets.symmetric(
          horizontal: referSize,
          vertical: 10.h,
        );
        final style = TextStyle(
          color: textColor,
          fontSize: referSize,
          height: 1.3,
        );
        final span = TextSpan(text: name, style: style);
        final painter = TextPainter(
          text: span,
          textDirection: TextDirection.ltr,
        );
        painter.layout(minWidth: 0, maxWidth: size.width);
        double x = realCursorX + (image.width - painter.width) / 2 + referSize;
        double y = realCursorY + image.height + 10.h;
        final position = Offset(x, y);
        final backgroundPaint =
            Paint()
              ..color = backgroundColor
              ..style = paintingStyle
              ..strokeWidth = width;
        canvas.drawRRect(
          RRect.fromRectAndRadius(
            Rect.fromLTWH(
              x - padding.left,
              y - padding.bottom,
              painter.width + padding.horizontal,
              painter.height + padding.vertical,
            ),
            Radius.circular(16.r),
          ),
          backgroundPaint,
        );
        painter.paint(canvas, position);
      }
    }
  }

  @override
  bool shouldRepaint(_DrawingBoardPainter oldDelegate) {
    return userGraphics.isEmpty || userGraphics != oldDelegate.userGraphics;
  }
}
