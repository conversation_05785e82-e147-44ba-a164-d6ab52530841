import 'package:flutter/material.dart';

/// 从右向左滑动进入动画
class RightInSlideTransition extends StatefulWidget {
  final Widget child;
  final double beginX;
  const RightInSlideTransition(
      {super.key, required this.child, this.beginX = 0.13});
  @override
  RightInSlideTransitionState createState() => RightInSlideTransitionState();
}

class RightInSlideTransitionState extends State<RightInSlideTransition>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    // _slideAnimation =
    //     Tween<double>(begin: 1900, end: 0).animate(CurvedAnimation(
    //   parent: _controller,
    //   curve: Curves.easeInOut,
    // ));

    _slideAnimation = Tween<Offset>(
      begin: Offset(widget.beginX, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.ease,
    ));
    Future.delayed(Duration.zero).then((value) {
      _controller.reset();
      _controller.forward();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: widget.child,
    );

    // return AnimatedBuilder(
    //   animation: _slideAnimation,
    //   builder: (context, child) {
    //     return Transform.translate(
    //         offset: Offset(_slideAnimation.value, 0.0), child: widget.child);
    //   },
    // );
  }
}

enum OutSlideDirection { left, top, right, bottom }

///划出屏幕动画
class OutSlideTransition extends StatefulWidget {
  final Widget child;
  final OutSlideDirection direction;
  final bool startAnimation;
  final bool endAnimation;

  const OutSlideTransition({
    super.key,
    required this.child,
    required this.direction,
    required this.startAnimation,
    required this.endAnimation,
  });
  @override
  OutSlideTransitionState createState() => OutSlideTransitionState();
}

class OutSlideTransitionState extends State<OutSlideTransition>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _slideAnimation;
  static const int animationTime = 300; //ms
  int lastBuildTime = 0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: animationTime),
      vsync: this,
    );
    Tween<Offset> tween;

    switch (widget.direction) {
      case OutSlideDirection.left:
        tween = Tween<Offset>(
          begin: Offset.zero,
          end: const Offset(-1.0, 0.0),
        );
        break;
      case OutSlideDirection.right:
        tween = Tween<Offset>(
          begin: Offset.zero,
          end: const Offset(1.0, 0.0),
        );
        break;
      case OutSlideDirection.top:
        tween = Tween<Offset>(
          begin: Offset.zero,
          end: const Offset(0.0, -1.0),
        );
        break;
      case OutSlideDirection.bottom:
        tween = Tween<Offset>(
          begin: Offset.zero,
          end: const Offset(0.0, 1.0),
        );
        break;
    }

    _slideAnimation = tween.animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    startAnimation();
    return SlideTransition(
      position: _slideAnimation,
      child: widget.child,
    );
  }

  void startAnimation() {
    if (widget.startAnimation || widget.endAnimation) {
      if (_controller.isAnimating ||
          DateTime.now().millisecondsSinceEpoch - lastBuildTime <
              animationTime) {
        //正在执行动画return,避免多次错误build
        return;
      }
      lastBuildTime = DateTime.now().millisecondsSinceEpoch;

      Future.delayed(Duration.zero).then((value) {
        if (widget.startAnimation) {
          _controller.forward();
        } else if (widget.endAnimation) {
          _controller.reverse();
        }
      });
    }
  }
}
