import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/apis/http/device.api.dart';
import 'package:surgsmart/src/apis/http/surgery.api.dart';
import 'package:surgsmart/src/models/http/doctor.model.dart';
import 'package:surgsmart/src/models/http/procedure.model.dart';
import 'package:surgsmart/src/models/http/surgery.model.dart';
import 'package:surgsmart/src/models/http/org.model.dart';
import 'package:surgsmart/src/routes/go_paths.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';

/// 手术信息选择状态
enum SurgeryInfoSelectState {
  department,
  doctor,
  participant,
  procedure,
  // create,
}

extension SurgeryInfoSelectStateName on SurgeryInfoSelectState {
  String get displayName {
    switch (this) {
      case SurgeryInfoSelectState.department:
        return S.current.f_7Wld6Nlm;
      case SurgeryInfoSelectState.doctor:
        return S.current.f_7WldJXIk;
      case SurgeryInfoSelectState.procedure:
        return S.current.f_7WldHM42;
      case SurgeryInfoSelectState.participant:
        return '请选参与者({{0}}/{{1}})';
      // case SurgeryInfoSelectState.create:
      //   return "";
    }
  }
}

/// 所属模块: surgery
///
/// 手术创建
class SurgeryCreateController extends AppController with StateMixin<ApiModel> {
  SurgeryCreateController(super.key, super.routerState);

  /// 手术信息选择状态
  final infoSelectState = SurgeryInfoSelectState.department.nullableNotifier;

  late final List<OrgInfo> departments;
  late final Map<int, OrgInfo> departmentMap;

  late final List<OrgProcedureInfo> procedures;
  late final Map<int, OrgProcedureInfo> procedureMap;

  late final List<OrgSurgeonInfo> surgeons;
  late final Map<int, OrgSurgeonInfo> surgeonMap;

  late final List<OrgSurgeonInfo> participant;
  late final Map<int, OrgSurgeonInfo> participantMap;

  /// 科室
  int? department;

  /// 医生
  OrgSurgeonInfo? doctor;

  /// 选择的参与者
  Map<int, OrgSurgeonInfo> selectedParticipant = {};

  /// 术式
  OrgProcedureInfo? procedure;

  /// 住院号
  String? admissionNumber;

  @override
  void onInit() {}

  @override
  void onReady() async {
    try {
      await loadData();
      await readPreferences();
      update(LoadState.success(ApiModel()));
    } catch (error) {
      update(LoadState.failure(error.toString()));
    }
  }

  @override
  void onClose() {
    infoSelectState.dispose();
  }

  bool showAdmissionNumber() {
    return !AppContext.share.authorizeInfo.features.disablePatientInfo;
  }

  Future<void> loadData() async {
    List<dynamic> results = await Future.wait([
      HttpDeviceApi<OrgListInfo>.currentOrg().request(),
      HttpDeviceApi<OrgSurgeonListInfo>.currentOrgSurgeon().request(),
      HttpDeviceApi<OrgSurgeonListInfo>.currentOrgParticipant().request(),
      HttpDeviceApi<OrgProcedureListInfo>.currentOrgProcedure().request(),
    ]);

    departments = results[0].datas;
    departmentMap = Map.fromEntries(departments.map((e) => MapEntry(e.id, e)));

    surgeons = results[1].datas;
    surgeonMap = Map.fromEntries(surgeons.map((e) => MapEntry(e.id, e)));

    participant = results[2].datas;
    participantMap = Map.fromEntries(surgeons.map((e) => MapEntry(e.id, e)));

    procedures = results[3].datas;
    procedureMap = Map.fromEntries(
      procedures.map((e) => MapEntry(e.medicalOrganizationId, e)),
    );
  }

  List<OrgSurgeonInfo> avaliableSurgeons() {
    return surgeons
        .where((user) => user.medicalOrganizationIds.contains(department))
        .toList();
  }

  List<OrgProcedureInfo> avaliableProcedures() {
    return procedures
        .where((dp) => dp.medicalOrganizationId == department)
        .toList();
  }

  List<OrgSurgeonInfo> avaliableParticipant() {
    return participant
        .where(
          (user) =>
              user.medicalOrganizationIncludeAncestorsIds.contains(
                department,
              ) &&
              doctor?.id != user.id,
        )
        .toList();
  }

  String getParticipantNames() {
    StringBuffer buffer = StringBuffer();
    List<OrgSurgeonInfo> participants = selectedParticipant.values.toList();
    for (int i = 0; i < participants.length; i++) {
      buffer.write('${participants[i].name}\n');
      if (i == 4 && participants.length > 5) {
        buffer.write('+${participants.length - 5}');
        break;
      }
    }
    return buffer.toString().isEmpty && procedure != null
        ? '无'
        : buffer.toString().trim();
  }

  Future<void> readPreferences() async {
    department = AppPreferences.department.intValue;
    if (department != null && departmentMap.containsKey(department)) {
      infoSelectState.value = SurgeryInfoSelectState.doctor;
    }
  }

  var shouldResponse = true;
  Future<void> createSurgery({bool type = true}) async {
    if (!shouldResponse) return;

    if (doctor == null) {
      throw Exception(S.current.f_7WldJXIk);
    }

    if (procedure == null) {
      throw Exception(S.current.f_7WldHM42);
    }
    update(LoadState.loading());

    shouldResponse = false;
    final surgery =
        (await HttpSurgeryApi<SurgeryInfo>.create(
          userId: doctor?.id ?? 0,
          procedureName: procedure?.name ?? "",
          admissionNumber: admissionNumber,
          department: departmentMap[department]?.name,
          participantIds: selectedParticipant.keys.toList(),
        ).request());

    await AppPreferences.enableAdvanceAi.setBool(type);

    // 打开手术室
    app.openInner(
      GoPaths.surgeryRoom,
      arguments: {"surgeryId": surgery.id, "enableAdvanceAi": type},
      isReplaceCurrent: true,
    );
    shouldResponse = true;
  }
}
