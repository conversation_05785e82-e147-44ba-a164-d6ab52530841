import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/widgets/numeric_keypad_modal.widget.dart';

// ignore: must_be_immutable
class NumberInput extends StatefulWidget {
  NumberInput({
    super.key,
    this.text,
    required this.onConfirm,
    this.onClear,
  });

  String? text;

  final void Function(String? text) onConfirm;
  final void Function()? onClear;

  @override
  State<NumberInput> createState() => _NumberInputState();
}

class _NumberInputState extends State<NumberInput> {
  String? number;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        showNumericKeypadModal(context);
      },
      child: Container(
        margin: EdgeInsets.only(right: 170.w),
        padding: EdgeInsets.symmetric(
          horizontal: 24.w,
          vertical: 11.h,
        ),
        width: 700.w,
        decoration: BoxDecoration(
          color: const Color(0xFF1D2632),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: const Color(0xFF313E50),
            width: 4.r,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                number ?? S.current.f_7WldoTrL,
                style: TextStyle(
                  fontSize: 48.sp,
                  color: number == null
                      ? Colors.white.withValues(alpha: 0.6)
                      : Colors.white,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            number == null
                ? Icon(
                    Icons.chevron_right,
                    size: 64.sp,
                    color: Colors.white,
                  )
                : GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      setState(() {
                        number = null;
                        widget.onClear!();
                      });
                    },
                    child: Icon(
                      Icons.close,
                      size: 48.sp,
                      color: Colors.white,
                    ),
                  ),
          ],
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    number = widget.text;
  }

// 数字键盘
  void showNumericKeypadModal(BuildContext context) {
    KeypadModal(
      text: number,
      placeholder: S.current.f_9KZDO6zf,
      keypadType: KeypadType.numeric,
      onConfirm: (text) {
        if (text.isNotEmpty) {
          setState(() {
            number = text;
            widget.onConfirm(text);
          });
        } else {
          setState(() {
            number = null;
            widget.onClear!();
          });
        }
        Navigator.of(context).pop();
      },
    ).show(context, barrierDismissible: false);
  }
}
