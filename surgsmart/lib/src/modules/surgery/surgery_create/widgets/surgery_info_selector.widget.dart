import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/src/widgets/click_animation.widget.dart';

class SurgeryInfoItem {
  final String name;
  final String? imageMark;
  final String? imageBg;
  final int maxLine;
  final int fontSize;
  final int horizontalPadding;

  bool selected; //多选状态下使用

  SurgeryInfoItem({
    required this.name,
    this.imageMark,
    this.imageBg,
    this.selected = false,
    this.maxLine = 2,
    this.fontSize = 64,
    this.horizontalPadding = 32,
  });
}

class SurgeryInfoSelector extends StatelessWidget {
  final List<SurgeryInfoItem> items;
  final void Function(int index, bool selected) onItemSelected;
  final VoidCallback onTap;
  final bool isExpanded;
  final String title;
  final String? content;
  final int crossAxisCount;
  final bool multipleChoices;
  final String? noChoiceTitle;
  final gridViewChange = false.notifier;

  SurgeryInfoSelector({
    super.key,
    required this.items,
    required this.onItemSelected,
    required this.onTap,
    required this.isExpanded,
    required this.title,
    this.content = '',
    this.crossAxisCount = 5,
    this.multipleChoices = false,
    this.noChoiceTitle,
  });

  @override
  Widget build(BuildContext context) {
    double minWidth = 377.w;
    //这里的宽度和外部组件存在关联可以优化，目前组件业务展示固定暂时先这样快速处理
    double expendedWidth = 3840.w - (169.w * 2) - (minWidth * 3) - (24.w * 3);
    // 目前多选为最后栏一栏，暂时先通过此方式判断当前最大宽度
    if (multipleChoices) {
      expendedWidth -= 503.w;
      items.insert(
        0,
        SurgeryInfoItem(
          name: noChoiceTitle ?? '无$title',
          selected: items.every((element) => !element.selected),
        ),
      );
    }

    double paddingSize = 64.r;
    double gridAxisSpacing = 48.r;
    double gridCellWidth =
        (expendedWidth -
            2 * paddingSize -
            (crossAxisCount - 1) * gridAxisSpacing) /
        crossAxisCount;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: isExpanded ? expendedWidth : minWidth,
      curve: Curves.fastOutSlowIn,
      child:
          isExpanded
              ? Container(
                decoration: BoxDecoration(
                  color: const Color(0xFF121820),
                  border: Border.all(
                    color: const Color(0xFF0EC6D2),
                    width: 8.r,
                  ),
                  borderRadius: BorderRadius.circular(20.r),
                ),
                padding: EdgeInsets.all(paddingSize),
                child: ScrollConfiguration(
                  behavior: const MaterialScrollBehavior().copyWith(
                    physics: const BouncingScrollPhysics(),
                    dragDevices: {
                      PointerDeviceKind.touch,
                      PointerDeviceKind.mouse,
                    },
                  ),
                  child: ValueListenableBuilder(
                    valueListenable: gridViewChange,
                    builder: (context, value, child) {
                      return GridView.builder(
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          mainAxisSpacing: gridAxisSpacing,
                          crossAxisSpacing: gridAxisSpacing,
                          crossAxisCount: crossAxisCount,
                          childAspectRatio: gridCellWidth / 213.h,
                        ),
                        itemCount: items.length,
                        itemBuilder: (BuildContext context, int index) {
                          if (multipleChoices) {
                            return GestureDetector(
                              onTap: () {
                                if (index == 0) {
                                  for (int i = 0; i < items.length; i++) {
                                    items[i].selected = i == 0;
                                  }
                                } else {
                                  items.first.selected = false;
                                  items[index].selected =
                                      !items[index].selected;
                                }

                                gridViewChange.value =
                                    !gridViewChange.value; //刷新GridView
                                onItemSelected(index, items[index].selected);
                              },
                              child: _ItemCard(items[index]),
                            );
                          }
                          return ClickAnimateContainer(
                            height: 213.h,
                            width: gridCellWidth,
                            onTap: () => onItemSelected(index, true),
                            tapDownColor: const Color(0x330EC6D2),
                            child: _ItemCard(items[index]),
                          );
                        },
                      );
                    },
                  ),
                ),
              )
              : GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: onTap,
                child: Container(
                  width: minWidth,
                  padding: EdgeInsets.only(top: 50.h, bottom: 100.h),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        const Color(0xFF3C4657),
                        if (content?.isNotEmpty == true)
                          const Color(0xFF0E2F4B)
                        else
                          const Color(0xFF3C4657),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                    border: Border.all(
                      color: const Color(0x8AD9EBF6),
                      width: 2.r,
                    ),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (content?.isNotEmpty == true)
                        Expanded(
                          child: Center(
                            child: Padding(
                              padding: EdgeInsets.all(24.r),
                              child:
                                  multipleChoices
                                      ? Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children:
                                            content!.split('\n').map((line) {
                                              return Text(
                                                line,
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 64.sp,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                                textAlign: TextAlign.center,
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              );
                                            }).toList(),
                                      )
                                      : Text(
                                        content!,
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 64.sp,
                                          fontWeight: FontWeight.w500,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                            ),
                          ),
                        ),

                      Text(
                        title,
                        style: TextStyle(
                          color: const Color(0xCCFFFFFF),
                          fontSize: 56.sp,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
    );
  }
}

class _ItemCard<T> extends StatelessWidget {
  final SurgeryInfoItem surgeryInfoItem;

  const _ItemCard(this.surgeryInfoItem);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 213.h,
      width: double.infinity,
      child: Stack(
        children: [
          Container(
            height: 213.h,
            width: double.infinity,
            padding: EdgeInsets.symmetric(
              horizontal: surgeryInfoItem.horizontalPadding.w,
            ),
            decoration: BoxDecoration(
              color: Color(surgeryInfoItem.selected ? 0xFF059AAB : 0x14FFFFFF),
              borderRadius: BorderRadius.circular(12.r),
              image:
                  surgeryInfoItem.imageBg == null
                      ? null
                      : DecorationImage(
                        image: AssetImage(surgeryInfoItem.imageBg!),
                      ),
            ),
            child: Center(
              child: Text(
                surgeryInfoItem.name,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
                maxLines: surgeryInfoItem.maxLine,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: surgeryInfoItem.fontSize.sp,
                  height: 1.2,
                ),
              ),
            ),
          ),
          if (surgeryInfoItem.imageMark != null)
            Positioned(
              top: 0,
              right: 0,
              width: 100.w,
              height: 55.h,
              child: Image.asset(surgeryInfoItem.imageMark!),
            ),
        ],
      ),
    );
  }
}
