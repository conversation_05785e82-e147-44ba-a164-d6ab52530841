import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class StatusCard extends StatefulWidget {
  final AssetImage defaultImage;
  final AssetImage activeImage;
  final String title;
  final String? desc;
  final bool isActive;
  final VoidCallback? onTap;

  const StatusCard({
    super.key,
    required this.defaultImage,
    required this.activeImage,
    required this.title,
    this.desc,
    this.isActive = false,
    this.onTap,
  });

  @override
  State<StatusCard> createState() => _StatusCardState();
}

class _StatusCardState extends State<StatusCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _colorAnimation = ColorTween(
      begin: Colors.white.withValues(alpha: 0.04),
      end: Colors.white.withValues(alpha: 0.2),
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // 初始化时根据isActive状态设置动画
    if (widget.isActive) {
      _animationController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(StatusCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Container(
            width: widget.isActive ? null : 140.w,
            height: 140.h,
            decoration: BoxDecoration(
              color: _colorAnimation.value,
              borderRadius: BorderRadius.circular(8.r),
            ),
            padding: EdgeInsets.symmetric(
              vertical: 25.h,
              horizontal: widget.isActive ? 50.w : 25.w,
            ),
            child:
                widget.isActive
                    ? _buildActiveContent()
                    : _buildDefaultContent(),
          );
        },
      ),
    );
  }

  Widget _buildDefaultContent() {
    return Image(image: widget.defaultImage, fit: BoxFit.contain);
  }

  Widget _buildActiveContent() {
    return IntrinsicWidth(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 90.r,
            height: 90.r,
            child: Image.asset(
              widget.activeImage.assetName,
              fit: BoxFit.contain,
              cacheHeight: 90.r.toInt(), // 规避 实际图片尺寸 大于 组件时, 引起 Texture 组件闪烁
              cacheWidth: 90.r.toInt(),
            ),
          ),
          SizedBox(width: 25.w),
          widget.desc?.isNotEmpty == true
              ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.title,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 28.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    widget.desc!,
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.7),
                      fontSize: 24.sp,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              )
              : Text(
                widget.title,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 28.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
        ],
      ),
    );
  }
}
