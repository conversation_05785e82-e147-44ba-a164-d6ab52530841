
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'dart:ui' as ui;

class VideoRendererModel {
  final Uint8List rgbaBytes;
  final int width;
  final int height;

  const VideoRendererModel({
    required this.rgbaBytes,
    required this.width,
    required this.height,
  });

  factory VideoRendererModel.fromMap(Map<String, dynamic> map) {
    return VideoRendererModel(
      rgbaBytes: map['rgbaBytes'],
      width: map['width'],
      height: map['height'],
    );
  }

  Map<String, dynamic> toMap() {
    return {'rgbaBytes': rgbaBytes, 'width': width, 'height': height};
  }
}

class VideoRenderer extends StatefulWidget {
  final VideoRendererModel videoRendererModel;

  const VideoRenderer({super.key, required this.videoRendererModel});

  @override
  State<VideoRenderer> createState() => _VideoRendererState();
}

class _VideoRendererState extends State<VideoRenderer> {
  ui.Image? _image;

  @override
  void initState() {
    super.initState();
    _decodeImage();
  }

  @override
  void didUpdateWidget(VideoRenderer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.videoRendererModel.rgbaBytes !=
            widget.videoRendererModel.rgbaBytes ||
        oldWidget.videoRendererModel.width != widget.videoRendererModel.width ||
        oldWidget.videoRendererModel.height !=
            widget.videoRendererModel.height) {
      _decodeImage();
    }
  }

  void _decodeImage() {
    if (widget.videoRendererModel.rgbaBytes.isEmpty) return;
    ui.decodeImageFromPixels(
      widget.videoRendererModel.rgbaBytes,
      widget.videoRendererModel.width,
      widget.videoRendererModel.height,
      ui.PixelFormat.rgba8888,
      (ui.Image image) {
        if (mounted) {
          setState(() => _image = image);
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _VideoPainter(_image),
      size: Size(
        widget.videoRendererModel.width.toDouble(),
        widget.videoRendererModel.height.toDouble(),
      ),
    );
  }
}

class _VideoPainter extends CustomPainter {
  final ui.Image? image;

  _VideoPainter(this.image);

  @override
  void paint(Canvas canvas, Size size) {
    if (image == null) return;

    // 保持宽高比绘制
    final srcSize = Size(image!.width.toDouble(), image!.height.toDouble());
    final scaleX = size.width / srcSize.width;
    final scaleY = size.height / srcSize.height;
    final scale = scaleX < scaleY ? scaleX : scaleY;
    final scaledSize = Size(srcSize.width * scale, srcSize.height * scale);
    final offset = Offset(
      (size.width - scaledSize.width) / 2,
      (size.height - scaledSize.height) / 2,
    );

    canvas.drawImageRect(
      image!,
      Rect.fromLTWH(0, 0, srcSize.width, srcSize.height),
      Rect.fromPoints(
        offset,
        offset + Offset(scaledSize.width, scaledSize.height),
      ),
      Paint()..filterQuality = FilterQuality.high,
    );
  }

  @override
  bool shouldRepaint(_VideoPainter oldDelegate) => oldDelegate.image != image;
}
