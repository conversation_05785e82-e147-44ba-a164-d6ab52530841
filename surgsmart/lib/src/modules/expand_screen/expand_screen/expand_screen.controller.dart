import 'dart:async';
import 'dart:io';
import 'package:app_foundation/app_foundation.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:medias_kit/core/medias_kit_ffi.dart' as ffi;
import 'package:medias_kit/medias_kit.dart';
import 'package:screen_retriever/screen_retriever.dart';
import 'package:surgsmart/src/models/http/room.model.dart';
import 'package:surgsmart/src/models/mqtt/interactive.model.dart';
import 'package:surgsmart/src/models/screen_status.model.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/operation/tools/drawing_board_notifier.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/tools/throttle_control.dart';
import 'package:window_manager/window_manager.dart';

import '../../../widgets/smart_voice.widget.dart';

/// 所属模块: expand_screen
///
/// 扩展屏幕显示
class ExpandScreenController extends AppController
    with StateMixin<ApiModel>, ScreenListener {
  ExpandScreenController(super.key, super.routerState);

  static final String statusInfoTag = 'statusInfo';
  static final String videoFrameInfoTag = 'videoFrameInfo'; //自定义视频帧信息
  static final String trackingInfoTag = 'trackingInfo'; // 追踪信息
  static final String drawingBoardNotifierTag = 'drawingBoardNotifier';
  static final String drawingBleedingPointNotifierTag =
      'drawingBleedingPointNotifierTag';

  Monitor? monitorController;

  ///扩展的第二路视频源控制器
  Monitor? monitor2Controller;

  /// 白板绘图
  final graphics = <UserGraphic>[].notifier;

  final bleedingPoints = <Offset>[].notifier; // 出血点

  final statusInfo = ExpandScreenStatusInfo().initWith({}).notifier;

  final trackingInfo = TrackingInfo.fromMap({}).emptyNotifier; // 解剖结构追踪信息

  late final playbackRender = ffi.textureRendererCreate(
    MediasKit.pluginId.toNativeCharPointer(this),
  );
  int get playbackTextureId => ffi.textureRendererGetTextureId(playbackRender);

  late final trackingRender = ffi.textureRendererCreate(
    MediasKit.pluginId.toNativeCharPointer(this),
  );
  int get trackingTextureId => ffi.textureRendererGetTextureId(trackingRender);

  late final modelRender = ffi.textureRendererCreate(
    MediasKit.pluginId.toNativeCharPointer(this),
  );
  int get modelTextureId => ffi.textureRendererGetTextureId(modelRender);

  // OfflineSurgeryData? surgeryData;

  // final videoFrameInfo =
  //     VideoRendererModel(
  //       rgbaBytes: Uint8List.fromList([0]),
  //       width: 0,
  //       height: 0,
  //     ).notifier;

  int width = 0;
  int height = 0;

  @override
  void onInit() {
    HostDevice.share.videoSources.addListener(createLocalMediaHandler);
    HostDevice.share.startListenVideoCaptureDevices();
    screenRetriever.addListener(this);

    ///窗口消息监听
    setMultiWindowMethodHandler();
  }

  @override
  void onScreenEvent(String eventName) async {
    debugPrint('ExpandScreenController.onScreenEvent: $eventName');
    if (eventName == 'display-removed') {
    } else if (eventName == 'display-added') {
      if (AppContext.targetScreen == null) return;
      Offset targetOffset = Offset(
        AppContext.targetScreen!.visiblePosition?.dx ?? app.width,
        0,
      );
      await windowManager.focus();
      await windowManager.setSize(
        Size(
          AppContext.targetScreen!.size.width,
          AppContext.targetScreen!.size.height,
        ),
      );
      await windowManager.setPosition(targetOffset);

      await windowManager.show();
    }
  }

  @override
  void onReady() async {
    //await readSurgeryData();
    update(LoadState.success(ApiModel()));
  }

  @override
  void onClose() {
    screenRetriever.removeListener(this);
    graphics.dispose();
    statusInfo.dispose();
    //videoFrameInfo.dispose();

    ffi.textureRendererDestroy(playbackRender);
    ffi.textureRendererDestroy(trackingRender);
    ffi.textureRendererDestroy(modelRender);
  }

  /// 创建本地媒体处理器
  Future<void> createLocalMediaHandler() async {
    if (monitorController != null || getVideoCapture() == null) return;
    if (Platform.isLinux) {
      monitorController = Monitor(
        videoCaptureDevice: getVideoCapture()!,
        framerate: 30,
      );

      if (!monitorController!.init()) {
        throw "监视控制器初始化失败!";
      }
      update(LoadState.success(ApiModel()));
    }
  }

  ffi.VideoCaptureDevice? getVideoCapture({String? path}) {
    app.logW(
      'VideoCaptureDevice size:  ${HostDevice.share.videoSources.value.length}',
    );
    for (var video in HostDevice.share.videoSources.value) {
      if (path == null) {
        if (!video.isUsbExtend) return video;
      } else if (video.path.dartString == path) {
        return video;
      }
    }
    return null;
  }

  refreshHandler() => graphics.value = [...graphics.value];

  void trackingMessageHandler(dynamic map) {
    trackingInfo.value =
        map != null ? TrackingInfo.fromMap(map.cast<String, dynamic>()) : null;
    final eventFlags = trackingInfo.value?.eventFlags ?? 0;
    if (eventFlags != 0) {
      // TODO: 处理 eventFlags
    }
  }

  /// 互动消息处理
  void interactionMessageHandler(Map<String, dynamic> map) {
    String? type = map['type'];
    if (type == null) return;
    if (type == 'drawCursor') {
      Graphic message = Graphic().initWith(map);
      DrawingBoardNotifier.drawCursor(
        getOrCreateGraphic(message.userId),
        message,
      );
    } else if (type == 'clearGraphics') {
      GraphicSet message = GraphicSet().initWith(map);
      DrawingBoardNotifier.clearGraphics(
        getOrCreateGraphic(message.userId),
        uuids: message.uuids,
      );
    } else if (type == 'drawGraphic') {
      Graphic message = Graphic().initWith(map);
      DrawingBoardNotifier.drawGraphic(
        getOrCreateGraphic(message.userId),
        message,
        message,
      );
    } else if (type == 'redo') {
      GraphicSet message = GraphicSet().initWith(map);
      DrawingBoardNotifier.redo(
        getOrCreateGraphic(message.userId),
        message.uuids,
      );
    } else if (type == 'undo') {
      GraphicSet message = GraphicSet().initWith(map);
      DrawingBoardNotifier.undo(
        getOrCreateGraphic(message.userId),
        message.uuids,
      );
    } else if (type == 'onDoing') {
      Graphic message = Graphic().initWith(map);
      DrawingBoardNotifier.drawGraphic(getOrCreateGraphic(0), message, null);
    }

    refreshHandler();
  }

  UserGraphic getOrCreateGraphic(int userId) {
    var userGraphic =
        graphics.value.where((g) => g.userId == userId).firstOrNull;
    final doctor =
        statusInfo.value.doctors.where((d) => d.id == userId).firstOrNull;

    if (userGraphic == null) {
      userGraphic = UserGraphic().copyWith(
        userId: userId,
        name: doctor?.name ?? '-',
      );
      graphics.value.add(userGraphic);
    } else {
      userGraphic.userId = userId;
      userGraphic.name = doctor?.name ?? '-';
    }
    return userGraphic;
  }

  final bleedingPointsChange = "bleedingPointsChange";
  void setMultiWindowMethodHandler() {
    DesktopMultiWindow.setMethodHandler((call, fromWindowId) async {
      // if (surgeryData == null) {
      //   readSurgeryData();
      // }
      // 处理来自主窗口的消息
      if (call.method == statusInfoTag && call.arguments is Map) {
        ExpandScreenStatusInfo status = ExpandScreenStatusInfo().initWith(
          call.arguments.cast<String, dynamic>(),
        );
        AppContext.share.networkConnected.value = status.networkConnected;

        checkExternalVideo(status.externalVideoDevice);
        checkBleedingTip(status.playbackType);
        refreshDrawingBoard();

        statusInfo.value = status;

        if (Throttle().checkPass(bleedingPointsChange, intervalMs: 900)) {
          bleedingPoints.value = [];
        }
      } else if (call.method == drawingBoardNotifierTag &&
          call.arguments is Map) {
        interactionMessageHandler(call.arguments.cast<String, dynamic>());
      } else if (call.method == videoFrameInfoTag) {
        //videoFrameFresh(call.arguments);
        return playbackRender.address;
      } else if (call.method == drawingBleedingPointNotifierTag &&
          call.arguments is List) {
        bleedingPoints.value =
            (call.arguments as List)
                .map((e) => Offset(e['dx'] as double, e['dy'] as double))
                .toList();
        Throttle().checkPass(bleedingPointsChange, intervalMs: 500);
      } else if (call.method == trackingInfoTag) {
        trackingMessageHandler(call.arguments);
        return {
          'trackingRender': trackingRender.address,
          'modelRender': modelRender.address,
        };
      }
      return true;
    });
  }

  //Future<void> readSurgeryData() async {
  // surgeryData ??= await DbUtils.instance.queryRecentSurgeryRecord();
  // app.logW('手术记录: ${surgeryData.toString()}');
  //}

  // Future<void> videoFrameFresh(arguments) async {
  //   File file = File(arguments['path']);
  //   Uint8List rgbaBytes = await file.readAsBytes();
  //   videoFrameInfo.value = VideoRendererModel(
  //     rgbaBytes: rgbaBytes,
  //     width: arguments['width'],
  //     height: arguments['height'],
  //   );
  // }

  void checkExternalVideo(String? externalVideoDevice) {
    if (externalVideoDevice != null) {
      if (monitor2Controller == null ||
          statusInfo.value.externalVideoDevice != externalVideoDevice) {
        monitor2Controller?.dispose();
        monitor2Controller = null;
        final videoCaptureDevice = getVideoCapture(path: externalVideoDevice);
        if (videoCaptureDevice == null) return;
        monitor2Controller = Monitor(
          videoCaptureDevice: videoCaptureDevice,
          width: 1920,
          height: 1080,
          framerate: 30,
        );
        if (monitor2Controller?.init() != true) {
          throw "监视控制器初始化失败!";
        }
      }
    } else {
      monitor2Controller?.dispose();
      monitor2Controller = null;
    }
  }

  void refreshDrawingBoard() {
    for (var g in graphics.value) {
      DrawingBoardNotifier.removeGraphic(g, DateTime.now());
    }
    refreshHandler();
  }

  final showBleedingTipTag = 'showBleedingTipTag';
  bool showBleeding = false;

  void checkBleedingTip(PlaybackType? playbackType) {
    if (showBleeding) return;
    if (playbackType == PlaybackType.bleedPlay &&
        playbackType != statusInfo.value.playbackType) {
      showBleeding = true;
      app.addOverlay(
        tag: showBleedingTipTag,
        child: Positioned(
          left: 42.w,
          bottom: 37.h,
          width: 556.w,
          height: 88.h,
          child: SmartVoice(
            state: DisplayState.activate,
            maxContentLength: 100,
            scale: 0.4,
            content: '疑似严重出血，M3将自动进行回放',
            showBottomDynamicEffect: false,
          ),
        ),
      );
      Future.delayed(const Duration(seconds: 10)).then((value) {
        app.removeOverlay(tag: showBleedingTipTag);
        showBleeding = false;
      });
    }
  }
}
