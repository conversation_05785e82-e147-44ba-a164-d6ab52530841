import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/generated/l10n.dart';

enum SettingType {
  recording,
  networkSetting,
  systemVersion,
  serviceUrl;

  static Map<SettingType, String> labels = {
    SettingType.recording: S.current.f_9PHjjkPE,
    SettingType.networkSetting: '网络设置', //屏蔽网络设置
    SettingType.systemVersion: S.current.f_9Tx3Ijyz,
    //if (AppContext.enablePrivateServer) SettingType.serviceUrl: '域名设置',
  };
}

/// 所属模块: setting
///
/// 设置
class SettingController extends AppController with StateMixin<ApiModel> {
  SettingController(super.key, super.routerState);

  final type = SettingType.recording.notifier;

  @override
  void onInit() {
    update(LoadState.success(ApiModel()));
  }

  @override
  void onReady() {}

  @override
  void onClose() {
    type.dispose();
  }
}
