import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/src/modules/setting/setting/views/network/network.controller.dart';
import 'package:surgsmart/src/modules/setting/setting/views/network/network.view.dart';
import 'package:surgsmart/src/modules/setting/setting/views/record/record.controller.dart';
import 'package:surgsmart/src/modules/setting/setting/views/record/record.view.dart';
import 'package:surgsmart/src/modules/setting/setting/views/service/service.controller.dart';
import 'package:surgsmart/src/modules/setting/setting/views/service/service.view.dart';
import 'package:surgsmart/src/modules/setting/setting/views/version/version.controller.dart';
import 'package:surgsmart/src/modules/setting/setting/views/version/version.view.dart';
import 'package:surgsmart/src/modules/setting/setting/widgets/header.widget.dart';
import 'package:surgsmart/src/widgets/app_background.widget.dart';
import 'setting.controller.dart';

/// 所属模块: setting
///
/// 设置
class SettingView extends AppView<SettingController> {
  const SettingView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AppBackground(
        child: controller.build(
          onSuccess: onSuccess,
          onFailure: onFailure,
          onLoading: onLoading,
          onEmpty: onEmpty,
        ),
      ),
    );
  }

  Widget onSuccess(BuildContext context, ApiModel value) {
    return Padding(
      padding: EdgeInsets.only(
        left: 172.w,
        right: 172.w,
        top: 44.h,
        bottom: 56.h,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Header(),
          SizedBox(height: 48.h),
          Expanded(
            child: Row(
              children: [
                ValueListenableBuilder(
                  valueListenable: controller.type,
                  builder: (context, type, child) {
                    return Column(
                      children:
                          SettingType.labels.keys.map((type) {
                            return GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: () {
                                controller.type.value = type;
                              },
                              child: tabItem(
                                type,
                                bottomMargin:
                                    type == SettingType.labels.keys.last
                                        ? 0
                                        : 40,
                              ),
                            );
                          }).toList(),
                    );
                  },
                ),
                SizedBox(width: 48.w),
                Expanded(
                  child: ValueListenableBuilder(
                    valueListenable: controller.type,
                    builder: (context, type, child) {
                      return Container(
                        height: double.infinity,
                        width: double.infinity,
                        padding: EdgeInsets.symmetric(
                          horizontal: 64.w,
                          vertical: 48.h,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xff1D2632),
                          borderRadius: BorderRadius.circular(24.w),
                          border: Border.all(
                            width: 8.w,
                            color: const Color(0xff313E50),
                          ),
                        ),
                        child: getContentByType(),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }

  Widget tabItem(SettingType type, {int bottomMargin = 0}) {
    return Container(
      height: 182.h,
      width: 610.h,
      margin: EdgeInsets.only(bottom: bottomMargin.h),
      decoration: BoxDecoration(
        color:
            type == controller.type.value
                ? const Color.fromARGB(255, 36, 42, 51)
                : Colors.transparent,
        borderRadius: BorderRadius.circular(12.w),
        border: Border.all(
          width: 8.w,
          color:
              type == controller.type.value
                  ? const Color(0xff313E50)
                  : Colors.transparent,
        ),
      ),
      child: Center(
        child: Text(
          SettingType.labels[type]!,
          style: TextStyle(fontSize: 64.sp),
        ),
      ),
    );
  }

  Widget getContentByType() {
    switch (controller.type.value) {
      case SettingType.recording:
        return RecordView(
          key: key,
          binding: (key) => RecordController(key, null),
        );
      case SettingType.networkSetting:
        return NetworkView(
          key: key,
          binding: (key) => NetworkController(key, null),
        );
      case SettingType.systemVersion:
        return VersionView(
          key: key,
          binding: (key) => VersionController(key, null),
        );
      case SettingType.serviceUrl:
        return ServiceView(
          key: key,
          binding: (key) => ServiceController(key, null),
        );
    }
  }
}
