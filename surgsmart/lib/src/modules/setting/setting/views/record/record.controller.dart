import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';

/// 所属模块: setting
///
/// 所属路由: setting
///
/// 录制设置
class RecordController extends AppController with StateMixin<ApiModel> {
  RecordController(super.key, super.routerState);

  final resolution = (AppPreferences.resolution.intValue ?? 1080).notifier;
  final frameRate = (AppPreferences.frameRate.intValue ?? 60).notifier;

  @override
  void onInit() {
    update(LoadState.success(ApiModel()));
  }

  @override
  void onReady() {}

  @override
  void onClose() {
    resolution.dispose();
    frameRate.dispose();
  }
}
