import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';
import 'package:surgsmart/src/widgets/modal.widget.dart';

import 'record.controller.dart';

/// 所属模块: setting
///
/// 所属路由: setting
///
/// 录制设置
class RecordView extends AppView<RecordController> {
  const RecordView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    // 注: 如果 AppBar 与数据有关联, 则把 controller.build() 提升为顶级节点
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: controller.build(
        onSuccess: onSuccess,
        onFailure: onFailure,
        onLoading: onLoading,
        onEmpty: onEmpty,
      ),
    );
  }

  Widget onSuccess(BuildContext context, ApiModel value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                S.current.f_9Tx36i4o,
                style: TextStyle(fontSize: 64.sp),
              ),
            ),
            Text("${S.current.f_9Tx3OLUx} ", style: TextStyle(fontSize: 48.sp)),
            ValueListenableBuilder(
              valueListenable: controller.resolution,
              builder: (context, resolution, child) {
                final sizePerHour =
                    (resolution == 2160 ? 24000 : 6000) / 8 * 3600;

                return Text(
                  sizePerHour.toInt().valueIndent(
                    value: 1024,
                    units: ["G/h", "M/h", "K/h"],
                    floatLen: 2,
                  ),
                  style: TextStyle(fontSize: 64.sp),
                );
              },
            ),
          ],
        ),
        Text(
          S.current.f_9Tx3FtFH,
          style: TextStyle(
            fontSize: 48.sp,
            color: Colors.white.withValues(alpha: 0.8),
          ),
        ),
        SizedBox(height: 24.h),
        Row(
          children: [
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                controller.resolution.value = 1080;
                AppPreferences.resolution.setInt(1080);
              },
              child: ValueListenableBuilder(
                valueListenable: controller.resolution,
                builder: (context, resolution, child) {
                  return Container(
                    width: 869.w,
                    height: 146.h,
                    decoration: BoxDecoration(
                      color:
                          resolution == 1080
                              ? const Color(0xff0EC6D2).withValues(alpha: 0.2)
                              : Colors.white.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(12.w),
                      border: Border.all(
                        width: 8.w,
                        color:
                            resolution == 1080
                                ? const Color(0xff0EC6D2)
                                : Colors.transparent,
                      ),
                    ),
                    child: Center(
                      child: Text("1080P", style: TextStyle(fontSize: 64.sp)),
                    ),
                  );
                },
              ),
            ),
            SizedBox(width: 48.w),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                if (controller.resolution.value == 2160) {
                  return;
                }
                Modal(
                  title: S.current.f_zbuzCjC0,
                  kind: ModalKind.dialog,
                  type: ModalType.info,
                  cancelText: S.current.f_9KZD45AL,
                  confirmText: S.current.f_NdV2HSh2,
                  message: S.current.f_NecQ6tke,
                  onConfirm: () async {
                    context.pop();
                    controller.resolution.value = 2160;
                    AppPreferences.resolution.setInt(2160);
                  },
                  onCancel: () async {
                    app.context.pop();
                  },
                ).show(context);
              },
              child: ValueListenableBuilder(
                valueListenable: controller.resolution,
                builder: (context, resolution, child) {
                  return Container(
                    width: 869.w,
                    height: 146.h,
                    decoration: BoxDecoration(
                      color:
                          resolution == 2160
                              ? const Color(0xff0EC6D2).withValues(alpha: 0.2)
                              : Colors.white.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(12.w),
                      border: Border.all(
                        width: 8.w,
                        color:
                            resolution == 2160
                                ? const Color(0xff0EC6D2)
                                : Colors.transparent,
                      ),
                    ),
                    child: Center(
                      child: Text("4K", style: TextStyle(fontSize: 64.sp)),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        SizedBox(height: 48.h),
        Text(S.current.f_9Tx3HKWw, style: TextStyle(fontSize: 64.sp)),
        Text(
          S.current.f_9Tx34FD2,
          style: TextStyle(
            fontSize: 48.sp,
            color: Colors.white.withValues(alpha: 0.8),
          ),
        ),
        SizedBox(height: 24.h),
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            controller.frameRate.value = 60;
            AppPreferences.frameRate.setInt(60);
          },
          child: ValueListenableBuilder(
            valueListenable: controller.frameRate,
            builder: (context, frameRate, child) {
              return Container(
                width: 869.w,
                height: 146.h,
                decoration: BoxDecoration(
                  color:
                      frameRate == 60
                          ? const Color(0xff0EC6D2).withValues(alpha: 0.2)
                          : Colors.white.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(12.w),
                  border: Border.all(
                    width: 8.w,
                    color:
                        frameRate == 60
                            ? const Color(0xff0EC6D2)
                            : Colors.transparent,
                  ),
                ),
                child: Center(
                  child: Text(
                    "60${S.current.f_9Tx3ZQSk}",
                    style: TextStyle(fontSize: 64.sp),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }
}
