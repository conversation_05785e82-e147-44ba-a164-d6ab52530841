import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/models/http/version.mode.dart';

import 'version.controller.dart';

/// 所属模块: setting
///
/// 所属路由: setting
///
/// 系统版本
class VersionView extends AppView<VersionController> {
  const VersionView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    // 注: 如果 AppBar 与数据有关联, 则把 controller.build() 提升为顶级节点
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: controller.build(
        onSuccess: onSuccess,
        onFailure: onFailure,
        onLoading: onLoading,
        onEmpty: onEmpty,
      ),
    );
  }

  Widget onSuccess(BuildContext context, VersionInfo value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          child: Text(S.current.f_9Tx3Jn6s, style: TextStyle(fontSize: 64.sp)),
          onLongPress: () {
            controller.versionTouch = true;
            controller.showCurrentVersions();
          },
          onLongPressUp: () {
            controller.versionTouch = false;
          },
        ),
        SizedBox(height: 24.h),
        Container(
          width: 869.w,
          height: 427.h,
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.15),
            borderRadius: BorderRadius.circular(12.w),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image(
                image: R.image.device_version(),
                width: 500.w,
                height: 280.h,
              ),
              //这里要求版本写死1.0.1
              Text('1.0.1', style: TextStyle(fontSize: 48.sp)), //value.version
            ],
          ),
        ),
        SizedBox(height: 24.h),
        value.hasNewVersion
            ? GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: controller.showSystemUpdateModal,
              child: Container(
                width: 869.w,
                height: 169.h,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      const Color(0xFF5CF5EB).withValues(alpha: 0.7),
                      const Color(0xff56B3E3).withValues(alpha: 0.7),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12.w),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      S.current.f_9Tx3oCEN,
                      style: TextStyle(fontSize: 64.sp),
                    ),
                    Icon(Icons.keyboard_arrow_right_rounded, size: 100.sp),
                  ],
                ),
              ),
            )
            : SizedBox(
              width: 869.w,
              height: 169.h,
              child: Center(
                child: Text(
                  S.current.f_9Tx3nRcU,
                  style: TextStyle(fontSize: 64.sp),
                ),
              ),
            ),
      ],
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }
}
