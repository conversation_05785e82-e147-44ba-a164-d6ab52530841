import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';

import 'service.controller.dart';

/// 所属模块: setting
///
/// 所属路由: setting
///
/// 域名设置
class ServiceView extends AppView<ServiceController> {
  const ServiceView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    return controller.build(
      onSuccess: onSuccess,
      onFailure: onFailure,
      onLoading: onLoading,
      onEmpty: onEmpty,
    );
  }

  Widget onSuccess(BuildContext context, ApiModel value) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('请输入域名或IP，连接服务'),
        ],
      ),
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }
}
