import 'dart:async';
import 'dart:convert';
import 'package:app_foundation/app_foundation.dart';
import 'package:dbus_wifi/dbus_wifi.dart';
import 'package:dbus_wifi/models/wifi_network.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/src/models/event_bus.model.dart';
import 'package:surgsmart/src/models/http/auth.model.dart';
import 'package:surgsmart/src/models/network_device_model.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';
import 'package:surgsmart/src/tools/device_cmd.dart';
import 'package:surgsmart/src/tools/event_bus.dart';
import 'package:surgsmart/src/tools/toast.dart';
import 'package:surgsmart/src/widgets/numeric_keypad_modal.widget.dart';

class NetworkState {
  NetworkState({required this.isOpen, required this.selectedDevice});

  final ValueNotifier<bool?> isOpen;
  final ValueNotifier<NetworkDevice?> selectedDevice;
}

/// 所属模块: setting
///
/// 所属路由: setting
///
/// 网络设置
class NetworkController extends AppController with StateMixin<ApiModel> {
  final bool isSystemSetting;
  NetworkController(
    super.key,
    super.routerState, {
    this.isSystemSetting = true,
  });

  final type = NetWorkType.ethernet.notifier;
  final wifiController = DbusWifi();

  StreamSubscription<EventBusInfo>? networkObserver;
  StreamSubscription<EventBusInfo>? ethernetObserver;

  Timer? scanTimer;
  final wifiList = <NetworkDevice>[].notifier;

  List<WifiNetwork> lastScanResult = [];
  List<Map<String, dynamic>> savedWifiNetworks = [];

  static final defaultMobileNetworkName = 'mobile_surgsmart';

  final Map<NetWorkType, NetworkState> networkState = {
    NetWorkType.ethernet: NetworkState(
      isOpen: ValueNotifier<bool?>(false),
      selectedDevice: ValueNotifier<NetworkDevice?>(null),
    ),
    NetWorkType.wifi: NetworkState(
      isOpen: ValueNotifier<bool?>(false),
      selectedDevice: ValueNotifier<NetworkDevice?>(null),
    ),
    NetWorkType.gsm: NetworkState(
      isOpen: ValueNotifier<bool?>(false),
      selectedDevice: ValueNotifier<NetworkDevice?>(null),
    ),
  };

  @override
  void onInit() async {
    await initNetworkState();
    await checkSavedWifiNetworks();

    update(LoadState.success(ApiModel()));

    networkObserver = eventBus.on<EventBusInfo>().listen((event) {
      if (event.type == EventBusType.netWorkChange &&
          networkState[type.value]!
                  .selectedDevice
                  .value
                  ?.extensions?['isEdit'] !=
              true) {
        refreshNetworkInfo();
      }
    });

    ethernetObserver = eventBus.on<EventBusInfo>().listen((event) {
      if (event.type == EventBusType.ethernetAvailable) {
        if (event.data == true) {
          switchNetwork(NetWorkType.ethernet);
        }
      }
    });
  }

  @override
  void onReady() {}

  @override
  void onClose() {
    networkObserver?.cancel();
    ethernetObserver?.cancel();
    scanTimer?.cancel();
  }

  Future<void> wifiScan() async {
    lastScanResult = await wifiController.search(timeout: Duration(seconds: 7));
    refreshWifiList();
  }

  /// 检查wifi是否已连接
  bool isConnectedWifi(String ssid) {
    return getWifiConnected()?.connection == ssid;
  }

  /// 检查wifi是否连接过
  bool isSavedWifi(String ssid) {
    return savedWifiNetworks.any((network) => network['id'] == ssid);
  }

  NetworkDevice? getWifiConnected() {
    NetworkDevice? networkDevice;
    for (var device in AppContext.share.networkDevices) {
      if (device.type == NetWorkType.wifi.name && device.isConnected()) {
        networkDevice = device;
      }
    }
    return networkDevice;
  }

  /// 获取已保存的wifi网络列表
  Future<void> checkSavedWifiNetworks() async {
    savedWifiNetworks = await wifiController.getSavedNetworks();
  }

  /// 忘记已保存的wifi网络0
  Future<void> forgetWifi(String ssid) async {
    //final forgotten = await wifiController.forgetNetwork(ssid: ssid); // 有时候这个会失败
    bool forgotten = await DeviceCmd.share.deleteNetwork(connection: ssid);
    if (forgotten) {
      await checkSavedWifiNetworks();

      ///忘记的wifi为当前连接的wifi需更新连接状态
      if (isConnectedWifi(ssid)) {
        await AppContext.share.getNetWorkDevices();
      }
      refreshWifiList();
    } else {
      ToastUtils.showToast(context!, message: '移除网络失败', type: ToastType.error);
    }
  }

  Future<void> switchNetwork(NetWorkType netType, {bool isInit = false}) async {
    //更新UI
    for (var key in networkState.keys) {
      bool enable = true;
      if (isSystemSetting) {
        Features features = AppContext.share.authorizeInfo.features;
        bool isPrivateServer = AppContext.enablePrivateServer;

        /// 私有化部署手动配置启用网络
        if (key == NetWorkType.ethernet) {
          enable = isPrivateServer ? true : features.networkWired;
        } else if (key == NetWorkType.wifi) {
          enable = isPrivateServer ? true : features.networkWifi;
        } else if (key == NetWorkType.gsm) {
          enable = isPrivateServer ? true : features.networkCellular;
        }
      }

      networkState[key]!.isOpen.value = enable ? (netType == key) : null;
      networkState[key]!.selectedDevice.value?.extensions?['isEdit'] =
          null; // 取消编辑状态

      if (enable && netType == key) {
        if (!isInit) {
          await AppContext.share.switchNetwork(netType);
        }
        if (netType == NetWorkType.wifi) {
          //给硬件开启一点时间，否则可能无法获取扫描结果
          Future.delayed(Duration(seconds: 2), () {
            wifiScan();
          });
          // scanTimer ??= Timer.periodic(Duration(seconds: 15), (timer) {
          //   wifiScan();
          // });
        } else {
          scanTimer?.cancel();
          scanTimer = null;
        }
      }
    }
    if (!isInit) {
      await AppPreferences.selectedNetwork.setString(netType.name);

      if (netType != NetWorkType.none) {
        ToastUtils.showToast(
          context!,
          message: '已开启${NetWorkType.labels[netType]}',
          type: ToastType.success,
        );
      } else {
        await AppContext.share.switchNetwork(netType);
      }
      update(LoadState.success(ApiModel()));
    }
  }

  Future<void> initNetworkState() async {
    await DeviceCmd.share.disableWifiPasswordInputBox();

    String? selectedNetwork = AppPreferences.selectedNetwork.stringValue;
    NetWorkType netWorkType =
        selectedNetwork != null
            ? NetWorkType.fromString(selectedNetwork)
            : NetWorkType.none;

    type.value =
        netWorkType == NetWorkType.none ? NetWorkType.ethernet : netWorkType;

    await switchNetwork(netWorkType, isInit: true);

    var gsmDevice = getDeviceByType(NetWorkType.gsm);
    if (gsmDevice == null) return;
    networkState[NetWorkType.gsm]!.selectedDevice.value = gsmDevice.clone();
    String? mobileNetworkJson = AppPreferences.mobileNetworkInfo.stringValue;
    if (mobileNetworkJson?.isNotEmpty == true) {
      Map networkMap = jsonDecode(mobileNetworkJson!);
      MobileNetWorkInfo mobileNetWorkInfo = MobileNetWorkInfo(
        connection: networkMap['connection'],
        apn: networkMap['apn'],
        mode: MobileNetWorkMode.fromString(networkMap['mode']),
      );
      networkState[NetWorkType.gsm]!.selectedDevice.value!.mobileNetWorkInfo =
          mobileNetWorkInfo;
    }
  }

  NetworkDevice? getDeviceByType(NetWorkType type) {
    NetworkDevice? device;
    for (var dev in AppContext.share.networkDevices) {
      if (dev.type == type.name) {
        device = dev;
      }
    }
    return device;
  }

  void refreshWifiList() {
    var wifiDevice = AppContext.share.networkDevices.firstWhere(
      (device) => device.type == NetWorkType.wifi.name,
    );
    //构造列表、结果去重
    Map<String, NetworkDevice> wifiMap = {};
    NetworkDevice? connectedWifi;
    for (var wifiItem in lastScanResult) {
      String ssid = utf8.decode(wifiItem.ssid.codeUnits);
      if (ssid.isEmpty) continue;
      bool isConnected = isConnectedWifi(wifiItem.ssid);
      wifiMap[wifiItem.ssid] = NetworkDevice(
        device: wifiDevice.device,
        state: isConnected ? 'connected' : 'disconnected',
        type: NetWorkType.wifi.name,
        connection: ssid,
        wifiNetwork: wifiItem,
      );
      if (isConnected) {
        connectedWifi = wifiMap[wifiItem.ssid];
      }
    }
    if (networkState[NetWorkType.wifi]!.selectedDevice.value?.connection !=
        connectedWifi?.connection) {
      networkState[NetWorkType.wifi]!.selectedDevice.value = connectedWifi;
    }

    final wifiNetworks = wifiMap.values.toList();
    if (connectedWifi != null) {
      //已连接置顶排序
      wifiNetworks.removeWhere(
        (element) => element.connection == connectedWifi!.connection,
      );
      wifiNetworks.insert(0, connectedWifi);
    }
    wifiList.value = wifiNetworks;
  }

  /// 配置并连接移动网络
  Future<void> setMobileNetwork() async {
    NetworkDevice? mobileDevice =
        networkState[NetWorkType.gsm]!.selectedDevice.value;
    MobileNetWorkInfo? info = mobileDevice?.mobileNetWorkInfo;
    if (info == null) return;

    //断开当前连接，如果存在
    // AppContext.share.networkDevices
    //     .where((device) => (device.type == NetWorkType.gsm.name &&
    //         device.isConnected()))
    //     .forEach((device) async {
    //   await DeviceCmd.share.connectionNetwork(device.connection, false);
    // });
    //List<NetworkDevice> historyConnection = await AppContext.share.getConnectionHistory();
    //
    //if (historyConnection.indexWhere((element) => element.connection == defaultMobileNetworkName) == -1) {
    await DeviceCmd.share.deleteNetwork(connection: defaultMobileNetworkName);
    await DeviceCmd.share.createMobileAPN(
      device: mobileDevice!.device,
      connection: info.connection,
      apn: info.apn,
    );
    // } else {
    //   await DeviceCmd.share.setMobileAPN(info.connection, info.apn);
    // }

    // 激活设置连接
    await DeviceCmd.share.mobileConnectOpen(connection: info.connection);

    AppPreferences.mobileNetworkInfo.setString(jsonEncode(info.toMap()));
  }

  /// 设置移动网络配置
  Future<void> setMobileConfig({
    String? apn,
    MobileNetWorkMode? mode,
    bool isSave = true,
  }) async {
    MobileNetWorkInfo? info =
        networkState[NetWorkType.gsm]!.selectedDevice.value?.mobileNetWorkInfo;
    if (info == null) {
      networkState[NetWorkType.gsm]!
          .selectedDevice
          .value
          ?.mobileNetWorkInfo = MobileNetWorkInfo(
        connection: defaultMobileNetworkName,
        apn: apn ?? '',
        mode: mode ?? MobileNetWorkMode.auto,
      );
    } else {
      if (mode != null) {
        info.mode = mode;
        await DeviceCmd.share.setMobileNetworkMode(mode);
      }
      if (apn != null) {
        info.apn = apn;
      }
    }

    refreshNetworkInfo(netType: NetWorkType.gsm);

    if (isSave) {
      AppPreferences.mobileNetworkInfo.setString(jsonEncode(info!.toMap()));
    }
  }

  Future<bool> setNetWorkMethod({
    required String connection,
    required NetWorkMethod method,
  }) async {
    return DeviceCmd.share.dhcpAvailable(
      connection,
      NetWorkMethod.auto == method,
    );
  }

  Future<bool> checkNetwork() async {
    return AppContext.share.netWorkRequest();
  }

  //刷新当前网络详细信息
  void refreshNetworkInfo({NetWorkType? netType}) {
    networkState[netType ?? type.value]!.selectedDevice.value =
        networkState[netType ?? type.value]!.selectedDevice.value?.clone();
  }

  void connectWifi(
    NetworkDevice device, {
    String? password,
    bool isReconnect = false,
  }) async {
    try {
      showLoadingDialog('Wi-Fi连接中...');
      await DeviceCmd.share.setNetworkAutoconnect(device.connection, false);

      if (password?.isNotEmpty == true) {
        await wifiController.connect(device.wifiNetwork!, password!);
      } else {
        await DeviceCmd.share.connectionNetwork(device.connection, true);
      }
      await Future.delayed(Duration(seconds: 5));
      await AppContext.share.getNetWorkDevices();

      Future.delayed(Duration(seconds: isReconnect ? 4 : 2)).then((val) async {
        hideLoadingDialog();
        //更新连接信息
        await AppContext.share.getNetWorkDevices();

        update(LoadState.success(ApiModel()));

        await Future.delayed(Duration(seconds: 1));

        if (isConnectedWifi(device.connection)) {
          getSelectedDeviceNotifier().value = getWifiConnected();

          ToastUtils.showToast(
            context!,
            message: '连接成功',
            type: ToastType.success,
          );
          DeviceCmd.share.setNetworkAutoconnect(device.connection, true);
        } else {
          wifiConnectFailed(device);
        }
        //更新历史连接，用以判定是否连接过网络
        await checkSavedWifiNetworks();
        refreshWifiList();
        refreshNetworkInfo();
      });
    } catch (e) {
      app.logW('连接失败----${e.toString()}');
      hideLoadingDialog();
      wifiConnectFailed(device);
    }
  }

  ValueNotifier<NetworkDevice?> getSelectedDeviceNotifier({NetWorkType? type}) {
    return networkState[type ?? this.type.value]!.selectedDevice;
  }

  final showLoadingTag = 'showLoadingDialog';
  void showLoadingDialog(String hint) {
    if (app.existedOverlay(tag: showLoadingTag)) {
      return;
    }
    app.addOverlay(
      tag: showLoadingTag,
      child: Positioned.fill(
        child: Container(
          decoration: BoxDecoration(color: Colors.black.withValues(alpha: 0.6)),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              CupertinoActivityIndicator(
                radius: 155.r,
                color: const Color(0xFF297BFF),
              ),
              SizedBox(height: 40.h),
              Text(
                hint,
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 64.sp),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void hideLoadingDialog() {
    app.removeOverlay(tag: showLoadingTag);
  }

  void wifiConnectFailed(NetworkDevice device) {
    ToastUtils.showToast(
      context!,
      message: '密码错误或网络连接失败',
      type: ToastType.error,
    );
    inputWifiPassword(device, isReconnect: true);
  }

  void connectWifiDevice(NetworkDevice device) async {
    if (isSavedWifi(device.connection)) {
      connectWifi(device);
    } else {
      inputWifiPassword(device);
    }
  }

  void inputWifiPassword(NetworkDevice device, {bool isReconnect = false}) {
    showKeypadModal(
      keypadType: KeypadType.fullKey,
      placeholder: '请输入Wi-Fi密码',
      inputHide: true,
      onConfirm: (input) async {
        if (input.isEmpty) {
          ToastUtils.showToast(
            context!,
            message: '请输入密码',
            type: ToastType.error,
          );
          return;
        }
        Navigator.of(context!).pop();
        connectWifi(device, password: input, isReconnect: isReconnect);
      },
    );
  }

  // 键盘输入
  void showKeypadModal({
    required KeypadType keypadType,
    required Function(String) onConfirm,
    String? text,
    String? placeholder,
    bool inputHide = false,
  }) {
    KeypadModal(
      text: text,
      placeholder: placeholder,
      keypadType: keypadType,
      inputHideControl: inputHide,
      onConfirm: onConfirm,
    ).show(context!, barrierDismissible: false);
  }

  void setEditMode({bool? isEdit, bool? clickable}) {
    NetworkDevice networkDevice = getSelectedDeviceNotifier().value!.clone();
    if (isEdit != null) networkDevice.extensions?['isEdit'] = isEdit;
    if (clickable != null) networkDevice.extensions?['clickable'] = clickable;
    getSelectedDeviceNotifier().value = networkDevice;
  }

  // 网络配置启用
  Future<void> saveNetworkConfig(
    NetworkDevice networkDevice,
    NetworkConfig tempConfig,
  ) async {
    bool isSuccess = false;
    String connection = await getConnectionNameByDeviceName(networkDevice);

    if (tempConfig.method == NetWorkMethod.auto.name) {
      isSuccess = await setNetWorkMethod(
        connection: connection,
        method: NetWorkMethod.auto,
      );
    } else {
      if (tempConfig.ip.isEmpty ||
          tempConfig.gateway.isEmpty ||
          tempConfig.subnetMask.isEmpty ||
          tempConfig.dnsServers.isEmpty) {
        ToastUtils.showToast(
          context!,
          message: '存在未配置条目，请配置完整后保存',
          type: ToastType.error,
        );
        return;
      }
      String subnetMask = tempConfig.subnetMask.first;

      if (tempConfig.isValidIP(subnetMask)) {
        subnetMask =
            tempConfig.cidrMap.entries
                .firstWhere((element) => element.value == subnetMask)
                .key
                .toString();
      }

      isSuccess = await DeviceCmd.share.manualConfigConnection(
        connection: connection,
        ip: tempConfig.ip.first,
        netmask: subnetMask,
        gateway: tempConfig.gateway,
        dns: tempConfig.dnsServers.first,
      );
    }
    if (isSuccess) {
      // todo 手动设置后需重连激活
      showLoadingDialog('正在保存并获取网络信息，请稍后');
      await DeviceCmd.share.reConnectNetwork(networkDevice.connection);
      setEditMode(isEdit: false, clickable: false);
      Future.delayed(const Duration(seconds: 2), () {
        hideLoadingDialog();
        ToastUtils.showToast(context!, message: '已保存设置');
        update(LoadState.success(ApiModel()));
      });
    } else {
      ToastUtils.showToast(context!, message: '保存失败', type: ToastType.error);
    }
  }

  Future<String> getConnectionNameByDeviceName(
    NetworkDevice networkDevice,
  ) async {
    String connection = networkDevice.connection;
    if (connection.isNotEmpty && connection != '--') {
      return connection;
    }
    List<NetworkDevice> networkDevices =
        await AppContext.share.getConnectionHistory();
    return networkDevices
        .firstWhere(
          (element) => element.device == networkDevice.device,
          orElse: () => networkDevice,
        )
        .connection;
  }

  bool isEditMode() {
    return getSelectedDeviceNotifier().value?.extensions?['isEdit'] ?? false;
  }
}
