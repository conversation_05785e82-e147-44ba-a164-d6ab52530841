import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/generated/l10n.dart';

class Header extends StatelessWidget {
  const Header({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          But<PERSON>(
            onPressed: () {
              Navigator.pop(context);
            },
            icon: Icon(
              Icons.arrow_back_ios,
              size: 56.r,
            ),
            foregroundColor: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(4.w)),
            label: Text(
              S.current.f_43uAIxZj,
              style: TextStyle(
                fontSize: 56.sp,
                color: Colors.white,
              ),
            ),
            betweenSpace: 15.w,
          ),
        ],
      ),
    );
  }
}
