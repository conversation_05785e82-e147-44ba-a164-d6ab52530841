# 业务路由文档

文档由 APPFoundation 生成，请勿手动修改

## setting 路由 => 系统设置

| 文件 | 用途 |
| :-- | :-- |
| [views](./views) | 存放包含业务逻辑的子组件 |
| [widgets](./widgets) | 存放不含业务逻辑的子组件 |
| [SettingView](./setting.view.dart) | 路由页面 |
| [SettingController](./setting.controller.dart) | 路由控制器 |

## 业务子组件

| 名称 | 视图 | 控制器 | 描述 |
| :-- | :-- | :-- | :-- |
| [record](./views/record) | [RecordView](./views/record/record.view.dart) | [RecordController](./views/record/record.controller.dart) | 录制设置 |
| [network](./views/network) | [NetworkView](./views/network/network.view.dart) | [NetworkController](./views/network/network.controller.dart) | 网络设置 |
| [version](./views/version) | [VersionView](./views/version/version.view.dart) | [VersionController](./views/version/version.controller.dart) | 系统版本 |
| [service](./views/service) | [ServiceView](./views/service/service.view.dart) | [ServiceController](./views/service/service.controller.dart) | 域名设置 |

[返回上级](../../README.md)
