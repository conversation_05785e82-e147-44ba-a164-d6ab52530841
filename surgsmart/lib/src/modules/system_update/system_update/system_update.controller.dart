import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/apis/http/upgrade.api.dart';
import 'package:surgsmart/src/apis/mqtt/device_upgrade.api.dart';
import 'package:surgsmart/src/models/event_bus.model.dart';
import 'package:surgsmart/src/models/http/version.mode.dart';
import 'package:surgsmart/src/models/mqtt/device_upgrade.model.dart';
import 'package:surgsmart/src/models/mqtt/upgrade_state.model.dart';
import 'package:surgsmart/src/routes/go_paths.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/tools/event_bus.dart';
import 'package:surgsmart/src/tools/string_util.dart';
import 'package:surgsmart/src/widgets/modal.widget.dart';

import '../../../apis/mqtt/device_control.api.dart';

/// 所属模块: system_update
///
/// 系统更新
class SystemUpdateController extends AppController with StateMixin<ApiModel> {
  SystemUpdateController(super.key, super.routerState);

  final progressInfo = DeviceUpgradeProgressInfo().initWith({}).notifier;

  MqttApiObserver? onProgressObserver;
  MqttApiObserver? onUpgradeResultObserver;
  MqttApiObserver? onDownloadResultObserver;

  bool progressEnd = false;
  bool showErrDialog = false;

  late VersionInfo versionInfo;

  @override
  void onInit() {
    versionInfo = (routerState?.extra as VersionInfo);
  }

  @override
  void onReady() async {
    onProgressObserver =
        MqttDeviceUpgradeApi<DeviceUpgradeProgressInfo>.onProgressMessage()
            .listen((message) async {
      app.logW("进度${message.progress}");
      progressInfo.value = message;
      if (!progressEnd && message.progress >= 1) {
        progressEnd = true; //成功消息可能会发多条
        progressInfo.value = message;
      }
    });

    onUpgradeResultObserver =
        MqttDeviceUpgradeApi<UpgradeStateInfo>.onInstallResultMessage()
            .listen((message) {
      app.logW("安装通知：${message.toString()}");
      AppContext.share.taskState = TaskState.nothing;
      if (message.code == 0) {
        MqttDeviceControlApi.broadcastAppStatus().publish();
        // PeripheralController.instance.notify(
        //     priority: true,
        //     msgType: BleMsgType.controlStatus.index,
        //     data: jsonEncode(DeviceControlStatus.ready.toMap()));

        // Future.delayed(const Duration(milliseconds: 21), () {
        //   PeripheralController.instance.stop();
        // });

        //安装成功
        Navigator.of(context!).popUntil(
          (route) => route.settings.name == GoPaths.home,
        );
      } else {
        showErrorDialog();
      }
    });

    onDownloadResultObserver =
        MqttDeviceUpgradeApi<UpgradeStateInfo>.onDownloadResultMessage()
            .listen((message) async {
      app.logW("下载完成通知：${message.toString()}");
      if (message.code == 0) {
        versionInfo =
            await HttpUpgradeApi<VersionInfo>.checkVersion().request();
        if (versionInfo.allowInstall) {
          await HttpUpgradeApi.installUpdate().request();
        } else {
          showErrorDialog();
        }
      } else {
        showErrorDialog();
      }
    });

    startUpdate();
  }

  void showErrorDialog() {
    if (showErrDialog) {
      return;
    }
    showErrDialog = true;
    Modal(
      title: S.current.f_zzT87YnB,
      kind: ModalKind.dialog,
      type: ModalType.error,
      cancelText: versionInfo.isForce ? null : S.current.f_9Tx3u1Ya,
      confirmText: S.current.f_MtReaRTU,
      message: S.current.f_NbNXfjYm,
      closable: false,
      onConfirm: () async {
        showErrDialog = false;
        progressEnd = false;
        app.context.pop();
        startUpdate();
      },
      onCancel: () async {
        showErrDialog = false;
        app.context.pop();
        app.context.pop();
      },
    ).show(context!);
  }

  @override
  void onClose() {
    onProgressObserver?.cancel();
    onUpgradeResultObserver?.cancel();
    onDownloadResultObserver?.cancel();
    progressInfo.dispose();
  }

  void startUpdate() async {
    try {
      AppContext.share.taskState = TaskState.install;
      eventBus.fire(EventBusInfo(type: EventBusType.taskStateChange));

      if (versionInfo.allowInstall) {
        progressInfo.value = DeviceUpgradeProgressInfo().initWith({
          "progress": 1.0,
        });
        update(LoadState.success(ApiModel()));
        await HttpUpgradeApi.installUpdate().request();
        return;
      }
      progressInfo.value = DeviceUpgradeProgressInfo().initWith({});
      update(LoadState.success(ApiModel()));
      await HttpUpgradeApi.startDownload().request();
    } catch (e) {
      showErrorDialog();
    }
  }

  String formatTimestamp(int seconds) {
    if (seconds < 60) {
      return '$seconds ${S.current.f_QKYizx9Z}';
    } else if (seconds < 3600) {
      var minutes = (seconds / 60).floor();
      return '$minutes ${S.current.f_QrCZ9iUJ}';
    } else {
      var hours = (seconds / 3600).floor();
      return StringUtil.replaceInterpolation(
          S.current.f2_RfJqERRM, [hours, ((seconds % 3600) / 60).floor()]);
    }
  }
}
