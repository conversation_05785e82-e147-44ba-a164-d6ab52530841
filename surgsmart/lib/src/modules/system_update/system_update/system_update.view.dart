import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/tools/string_util.dart';
import 'package:surgsmart/src/widgets/app_background.widget.dart';
import 'package:surgsmart/src/widgets/app_status_bar.widget.dart';

import 'system_update.controller.dart';

/// 所属模块: system_update
///
/// 系统更新
class SystemUpdateView extends AppView<SystemUpdateController> {
  const SystemUpdateView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    // 注: 如果 AppBar 与数据有关联, 则把 controller.build() 提升为顶级节点
    return Scaffold(
      body: AppBackground(
        child: controller.build(
          onSuccess: onSuccess,
          onFailure: onFailure,
          onLoading: onLoading,
          onEmpty: onEmpty,
        ),
      ),
    );
  }

  Widget onSuccess(BuildContext context, ApiModel value) {
    return ValueListenableBuilder(
      valueListenable: controller.progressInfo,
      builder: (context, progressInfo, child) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const AppStatusBar(showUpdate: false),
            SizedBox(height: 48.h),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 180.w, vertical: 60.h),
              child: Column(
                children: [
                  Text(
                    S.current.f_Nc2NMROz,
                    style: TextStyle(
                      fontSize: 100.sp,
                      color: const Color(0xff0BBA89),
                    ),
                  ),
                  SizedBox(height: 120.h),
                  progressInfo.progress >= 1
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              S.current.f_Nc2NdJmS,
                              style: TextStyle(
                                fontSize: 72.sp,
                              ),
                            ),
                            SizedBox(width: 40.w),
                            CupertinoActivityIndicator(
                              radius: 50.r,
                              color: const Color(0xFF297BFF),
                            ),
                          ],
                        )
                      : Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  width: 1235.w,
                                  child: LinearPercentIndicator(
                                    percent: progressInfo.progress,
                                    lineHeight: 48.r,
                                    barRadius: Radius.circular(24.r),
                                    backgroundColor: const Color(0X59D8D8D8),
                                    linearGradient: const LinearGradient(
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight,
                                      colors: [
                                        Color(0xFF5CF5EB),
                                        Color(0XFF56B3E3),
                                      ],
                                    ),
                                  ),
                                ),
                                SizedBox(width: 40.w),
                                Text(
                                  '${(progressInfo.progress * 100).toStringAsFixed(0)}%',
                                  style: TextStyle(
                                    fontSize: 72.sp,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 120.h),
                            Text(
                              '${StringUtil.replaceInterpolation(S.current.f1_43uA7bgG, [
                                    controller.formatTimestamp(
                                        progressInfo.remainingTime)
                                  ])}, ${S.current.f_Nc2NU05U}',
                              style: TextStyle(
                                fontSize: 72.sp,
                              ),
                            ),
                          ],
                        ),
                ],
              ),
            )
          ],
        );
      },
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }
}
