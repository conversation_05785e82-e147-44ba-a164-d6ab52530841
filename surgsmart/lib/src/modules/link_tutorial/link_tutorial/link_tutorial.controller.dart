import 'dart:async';

import 'package:app_foundation/app_foundation.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:surgsmart/src/apis/http/device.api.dart';
import 'package:surgsmart/src/models/event_bus.model.dart';
import 'package:surgsmart/src/models/http/setting.model.dart';
import 'package:surgsmart/src/tools/event_bus.dart';

class TutorialContent {
  String title;
  String content;

  TutorialContent({required this.title, required this.content});
}

/// 所属模块: link_tutorial
///
/// 连接教程
class LinkTutorialController extends AppController
    with StateMixin<TutorialContent> {
  LinkTutorialController(super.key, super.routerState);
  StreamSubscription? signalStateObserver;

  @override
  void onInit() async {
    if (Intl.getCurrentLocale() != 'zh_CN') {
      //暂时写死连接教程英文翻译
      var tutorialContentEn = TutorialContent(
        title: "Connection tutorial",
        content:
            "1.Ensure that the endoscopic image is displaying correctly. If there are any abnormalities, please contact the endoscope manufacturer.\n2.Confirm whether the connection between the M3 and the endoscope is SDI (round connector) or HDMI (flat connector).\n3.Reconnect the cables at both ends.\n4.If the issue persists, please contact the after-sales support personnel:\nEngineer Chen: ***********",
      );
      update(LoadState.success(tutorialContentEn));
      return;
    }

    var tutorialContent = TutorialContent(
      title: "连接教程",
      content:
          "1.确认腔镜画面显示正常，如果异常请联系腔镜厂商\n2.确认M3与腔镜的连线是SDI（圆头）还是HDMI（扁头）\n3.在连线两端重新进行插拔\n4.依旧无法解决问题，请联系售后人员\n陈工 ***********",
    );
    try {
      SettingInfo settingInfo =
          await HttpDeviceApi<SettingInfo>.settingInfo().request();

      for (var info in settingInfo.datas) {
        if (info.category == 'device.hardware.install-guide') {
          if (info.key == 'title') {
            tutorialContent.title = info.value;
          } else if (info.key == 'content') {
            tutorialContent.content = info.value;
          }
        }
      }
    } finally {
      update(LoadState.success(tutorialContent));
    }

    //视频信号输入状态改变
    signalStateObserver = eventBus.on<EventBusInfo>().listen((event) {
      if (event.type == EventBusType.videoSignalChange && event.data == true) {
        context?.pop();
      }
    });
  }

  @override
  void onReady() {}

  @override
  void onClose() {
    signalStateObserver?.cancel();
  }
}
