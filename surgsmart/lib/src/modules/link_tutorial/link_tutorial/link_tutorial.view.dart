import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/models/http/room.model.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/widgets/play_back.widget.dart';
import 'link_tutorial.controller.dart';

/// 所属模块: link_tutorial
///
/// 连接教程
class LinkTutorialView extends AppView<LinkTutorialController> {
  const LinkTutorialView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFF00040D),
      body: controller.build(
        onSuccess: onSuccess,
        onFailure: onFailure,
        onLoading: onLoading,
        onEmpty: onEmpty,
      ),
    );
  }

  Widget onSuccess(BuildContext context, TutorialContent data) {
    return Row(
      children: [
        Flexible(
          flex: 1,
          child: Container(
            margin: EdgeInsets.all(24.r),
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24.r),
            ),
            child: SurgPlayback(
              playbackInfo: PlaybackInfo(videoInfo: [
                MergeVideoInfo(
                    path:
                        '/home/<USER>/surgsmart-operating-room/data/flutter_assets/packages/surgsmart/assets/texts/medias/video_tutorial.mp4.txt',
                    duration: 28839)
              ], playbackType: PlaybackType.videoPlay),
              showProgressBar: true,
            ),
          ),
        ),
        Expanded(
          child: Padding(
            padding: EdgeInsets.all(60.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  data.title,
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 80.sp,
                      fontWeight: FontWeight.bold),
                ),
                20.verticalSpace,
                Expanded(
                  child: SingleChildScrollView(
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Flexible(
                          child: Text(
                            data.content,
                            style: TextStyle(
                                color: Colors.white, fontSize: 64.sp, height: 2),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                60.verticalSpace,
                Center(
                  child: Button(
                    onPressed: () {
                      context.pop();
                    },
                    label: Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: 104.w, vertical: 16.h),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(55.r),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            const Color(0xB25CF5EB),
                            const Color(0xB256B3E3)
                          ],
                        ),
                      ),
                      child: Text(
                        S.current.f_9vOS4jin,
                        style: TextStyle(
                          fontSize: 56.sp,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }
}
