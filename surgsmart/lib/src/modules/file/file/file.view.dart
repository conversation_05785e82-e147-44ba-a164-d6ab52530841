import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/models/http/file.model.dart';
import 'package:surgsmart/src/modules/file/file/widgets/file_list.widget.dart';
import 'package:surgsmart/src/modules/file/file/widgets/file_list_footer.widget.dart';
import 'package:surgsmart/src/modules/file/file/widgets/file_list_header.widget.dart';

import 'package:surgsmart/src/widgets/app_background.widget.dart';

import 'file.controller.dart';

/// 所属模块: file
///
/// 文件
class FileView extends AppView<FileController> {
  const FileView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    // 注: 如果 AppBar 与数据有关联, 则把 controller.build() 提升为顶级节点
    return Scaffold(
      body: AppBackground(
        child: controller.build(
          onSuccess: onSuccess,
          onFailure: onFailure,
          onLoading: onLoading,
          onEmpty: onEmpty,
        ),
      ),
    );
  }

  Widget onSuccess(BuildContext context, List<SurgeryFileInfo> fileList) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 180.w, vertical: 60.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ValueListenableBuilder(
            valueListenable: controller.checkEmpty,
            builder: (context, value, child) {
              return FileListHeader(
                controller: controller,
                doctors: controller.doctors,
                procedures: controller.procedures,
                onUpdateAdmissionNumber:
                    controller.showAdmissionNumber()
                        ? (admissionNumber) {
                          controller.admissionNumber = admissionNumber;
                          controller.page = 1;
                          controller.loadFileData();
                          controller.loadFileIds();
                        }
                        : null,
                onUpdateProcedureId: (name) {
                  controller.procedureName = name;
                  controller.page = 1;
                  controller.loadFileData();
                  controller.loadFileIds();
                },
                onUpdateUserId: (id) {
                  controller.userId = id;
                  controller.page = 1;
                  controller.loadFileData();
                  controller.loadFileIds();
                },
                onUpdateDateTime: ({endTime, startTime}) {
                  controller.endTime = endTime;
                  controller.startTime = startTime;
                  controller.page = 1;
                  controller.loadFileData();
                  controller.loadFileIds();
                },
                onExport:
                    controller.disableExport()
                        ? null
                        : () async {
                          controller.chooseIds = controller.checkedIds;
                          await controller.showChooseDiskModal();
                        },
                ondDeleteAll: () async {
                  controller.chooseIds = controller.checkedIds;
                  controller.deleteCheck();
                },
              );
            },
          ),
          ValueListenableBuilder(
            valueListenable: controller.surgeryFileListInfo,
            builder: (context, value, child) {
              return FileList(
                controller: controller,
                list: value.data,
                onDelete: (file) async {
                  controller.chooseIds = [file];
                  controller.deleteCheck();
                },
                onExport: (file) async {
                  controller.chooseIds = [file];
                  await controller.showChooseDiskModal();
                },
              );
            },
          ),
          ValueListenableBuilder(
            valueListenable: controller.spaceInfo,
            builder: (context, value, child) {
              return FileListFooter(
                page: controller.page,
                pageSize: 4,
                total: controller.total,
                chooseTotal: controller.checkedIds.length,
                spaceInfo: value,
                onUpdatePage: (int page) {
                  controller.page = page;
                  controller.loadFileData();
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 180.h,
        backgroundColor: Colors.transparent,
        centerTitle: true,
        leadingWidth: 480.w,
        leading: Padding(
          padding: EdgeInsets.only(left: 100.w),
          child: Button(
            betweenSpace: 24.w,
            icon: Icon(
              Icons.arrow_back_ios_new_rounded,
              size: 56.r,
              color: Colors.white,
            ),
            label: Text(
              S.current.f_7VB8izF1,
              style: TextStyle(
                color: Colors.white,
                fontSize: 56.sp,
                height: 78 / 56,
              ),
            ),
            onPressed: context.pop,
          ),
        ),
        title: Text(
          S.current.f_43uAm9gr,
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 80.sp,
            height: 112 / 80,
          ),
        ),
      ),
      body: Center(child: Text(error)),
    );
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }
}
