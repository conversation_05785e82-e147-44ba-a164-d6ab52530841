import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/widgets/numeric_keypad_modal.widget.dart';

// ignore: must_be_immutable
class NumberInput extends StatefulWidget {
  NumberInput({
    super.key,
    this.width,
    this.height,
    this.backgroundColor,
    this.textColor,
    this.text,
    this.placeholder,
    required this.onConfirm,
    this.onClear,
  });

  double? width;
  double? height;
  Color? backgroundColor;
  Color? textColor;
  String? text;
  String? placeholder;

  final void Function(String? text) onConfirm;
  final void Function()? onClear;

  @override
  State<NumberInput> createState() => _NumberInputState();
}

class _NumberInputState extends State<NumberInput> {
  String? number;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        showNumericKeypadModal(context);
      },
      child: Container(
        width: widget.width ?? 440.w,
        height: widget.height ?? 102.h,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        decoration: BoxDecoration(
          color: widget.backgroundColor ?? const Color(0xff1D2632),
          border: Border.all(width: 4.w, color: const Color(0xff313E50)),
          borderRadius: BorderRadius.all(Radius.circular(8.w)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                number ?? (widget.placeholder ?? "请输入"),
                style: TextStyle(
                  fontSize: 48.sp,
                  color:
                      widget.textColor ??
                      (number == null
                          ? Colors.white.withValues(alpha: 0.6)
                          : Colors.white),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            number == null
                ? Icon(
                  Icons.search,
                  size: 48.sp,
                  color: widget.textColor ?? Colors.white,
                )
                : GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    setState(() {
                      number = null;
                      widget.onClear!();
                    });
                  },
                  child: Icon(
                    Icons.close,
                    size: 48.sp,
                    color: widget.textColor ?? Colors.white,
                  ),
                ),
          ],
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    number = widget.text;
  }

  // 数字键盘
  void showNumericKeypadModal(BuildContext context) {
    KeypadModal(
      text: number,
      placeholder: S.current.f_9KZDO6zf,
      keypadType: KeypadType.numeric,
      onConfirm: (text) {
        if (text.isNotEmpty) {
          setState(() {
            number = text;
            widget.onConfirm(text);
          });
        } else {
          setState(() {
            number = null;
            widget.onClear?.call();
          });
        }
        Navigator.of(context).pop();
      },
    ).show(context, barrierDismissible: false);
  }
}
