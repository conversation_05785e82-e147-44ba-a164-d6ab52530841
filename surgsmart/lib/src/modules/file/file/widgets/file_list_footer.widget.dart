import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:medias_kit/core/medias_kit_ffi.dart' as ffi;
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/tools/string_util.dart';

import 'package:surgsmart/src/widgets/pagination.widget.dart';

class FileListFooter extends StatefulWidget {
  const FileListFooter({
    super.key,
    required this.page,
    required this.total,
    required this.pageSize,
    required this.chooseTotal,
    required this.spaceInfo,
    required this.onUpdatePage,
  });

  final int page;
  final int total;
  final int pageSize;
  final int chooseTotal;
  final ffi.HostDeviceSpaceInfo spaceInfo;

  final void Function(int page) onUpdatePage;

  @override
  State<FileListFooter> createState() => _FileListFooterState();
}

class _FileListFooterState extends State<FileListFooter> {
  // 分页数据
  late var page = widget.page;
  late var total = widget.total;
  late var pageSize = widget.pageSize;
  late var chooseTotal = widget.chooseTotal;
  late var spaceInfo = widget.spaceInfo;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didUpdateWidget(covariant FileListFooter oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.page != oldWidget.page) {
      page = widget.page;
    }
    if (widget.total != oldWidget.total) {
      total = widget.total;
    }
    chooseTotal = widget.chooseTotal;
    spaceInfo = widget.spaceInfo;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    double progress = spaceInfo.capacity == 0
        ? 0
        : (spaceInfo.capacity - spaceInfo.available) / (spaceInfo.capacity);
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          StringUtil.replaceInterpolation(S.current.f1_9KZDu4EQ, [chooseTotal]),
          style: TextStyle(
            fontSize: 48.sp,
            color: const Color(0xD9FFFFFF),
          ),
        ),
        SizedBox(width: 48.w),
        // 分页
        Pagination(
          currentPage: page,
          size: pageSize,
          total: total,
          onPaginate: (page) {
            widget.onUpdatePage(page);
          },
        ),
        const Spacer(),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
          decoration: BoxDecoration(
            border: Border.all(
              width: 2.w,
              color: progress >= 0.8
                  ? const Color(0xffFF4E4E)
                  : Colors.transparent,
            ),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Row(
            children: [
              if (progress >= 0.8)
                Icon(
                  Icons.warning_amber_rounded,
                  color: const Color(0xFFFF4E4E),
                  size: 49.r,
                ),
              SizedBox(width: 10.w),
              if (progress >= 0.8)
                Text(
                  S.current.f_9KZDLMvc,
                  style: TextStyle(
                    fontSize: 48.sp,
                    color: const Color(0xD9FFFFFF),
                  ),
                ),
              SizedBox(width: 20.w),
              SizedBox(
                width: 452.w,
                height: 40.h,
                child: LinearProgressIndicator(
                  color: const Color(0xff8B97AD),
                  backgroundColor: const Color(0xff2D3749),
                  value: progress,
                  borderRadius: BorderRadius.circular(20.r),
                ),
              ),
              SizedBox(width: 20.w),
              Text(
                StringUtil.replaceInterpolation(S.current.f2_9KZDQrN7, [
                  (spaceInfo.available).bitIndent(
                    bitStep: 10,
                    units: [
                      'TB',
                      'GB',
                      'MB',
                      'KB',
                      'B',
                    ],
                  ),
                  (spaceInfo.capacity).bitIndent(
                    bitStep: 10,
                    units: [
                      'TB',
                      'GB',
                      'MB',
                      'KB',
                      'B',
                    ],
                  )
                ]),
                style: TextStyle(
                  fontSize: 48.sp,
                  color: const Color(0xD9FFFFFF),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
