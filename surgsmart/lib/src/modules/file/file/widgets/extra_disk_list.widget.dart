import 'dart:async';
import 'dart:io';

import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:medias_kit/medias_kit.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/tools/toast.dart';

/// 磁盘列表
class ExtraDiskList extends StatefulWidget {
  const ExtraDiskList({
    super.key,
    required this.totalFileSize,
    required this.onExport,
    this.selectedUsbPath,
    this.onPullout,
  });

  final String? selectedUsbPath;
  final int totalFileSize;
  final void Function(String? path, {int? size}) onExport;
  final void Function()? onPullout;

  @override
  State<ExtraDiskList> createState() => _ExtraDiskListState();
}

class _ExtraDiskListState extends State<ExtraDiskList> {
  String? selectedUsbPath; // 选择的磁盘
  final ValueNotifier<List<Widget>> list = ValueNotifier([]);
  Timer? usbTimer;

  @override
  void initState() {
    super.initState();
    selectedUsbPath = widget.selectedUsbPath;
    usbTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      if (mounted) {
        //监听媒体设备
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    usbTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Directory directory = Directory('/media/${Platform.environment['USER']}/');
    if (Platform.isMacOS) {
      directory = Directory('/Volumes/');
    }
    List<Widget> widgetList = [];
    if (directory.existsSync()) {
      List<FileSystemEntity> fileSystemList = directory.listSync();
      if (fileSystemList.isEmpty) {
        list.value = [];
        widget.onPullout?.call();
      }
      for (var element in fileSystemList) {
        var stat = element.statSync();
        String name = element.path.split('/').last;
        String path = element.path;
        if (Platform.isLinux) {
          final info = HostDevice.share.getSpaceInfo(path);
          int capacity = info.capacity;
          int available = info.available;
          Widget box = GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              // 校验磁盘空间是否充足
              if (available < widget.totalFileSize + 1024 * 1024 * 10) {
                ToastUtils.showToast(
                  context,
                  message: S.current.f_9KZD0rqF,
                  type: ToastType.error,
                );
                return;
              }
              setState(() {
                selectedUsbPath = path;
                widget.onExport(path, size: available);
              });
            },
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 60.w, horizontal: 60.h),
              decoration: BoxDecoration(
                color:
                    selectedUsbPath == path
                        ? const Color(0xff0EC6D2).withValues(alpha: 0.2)
                        : const Color(0xff1D2632),
                border: Border.all(
                  width: 8.w,
                  color:
                      selectedUsbPath == path
                          ? const Color(0xff0EC6D2)
                          : const Color(0xff313E50),
                ),
                borderRadius: BorderRadius.circular(24.w),
              ),
              child: Row(
                children: [
                  Container(
                    width: 280.w,
                    height: 280.h,
                    padding: EdgeInsets.symmetric(
                      vertical: 60.h,
                      horizontal: 60.w,
                    ),
                    margin: EdgeInsets.only(right: 48.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(140.w),
                      gradient: const LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [Color(0xff00CBDA), Color(0xff0261A5)],
                      ),
                    ),
                    child: Image(image: R.image.usb()),
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: TextStyle(
                          fontSize: 72.sp,
                          fontWeight: FontWeight.w300,
                        ),
                      ),
                      Text(
                        '${(capacity - available).bitIndent(bitStep: 10, units: ['TB', 'GB', 'MB', 'KB', 'B'])} / ${capacity.bitIndent(bitStep: 10, units: ['TB', 'GB', 'MB', 'KB', 'B'])}',
                        style: TextStyle(
                          fontSize: 56.sp,
                          fontWeight: FontWeight.w300,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
          widgetList.add(box);
          list.value = widgetList;
        } else {
          int capacity = stat.size;
          int available = stat.size;
          Widget box = GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              // 校验磁盘空间是否充足
              if (available < widget.totalFileSize + 1024 * 1024 * 10) {
                ToastUtils.showToast(
                  context,
                  message: S.current.f_9KZD0rqF,
                  type: ToastType.error,
                );
                return;
              }
              setState(() {
                selectedUsbPath = path;
                widget.onExport(path, size: available);
              });
            },
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 60.w, horizontal: 60.h),
              decoration: BoxDecoration(
                color:
                    selectedUsbPath == path
                        ? const Color(0xff0EC6D2).withValues(alpha: 0.2)
                        : const Color(0xff1D2632),
                border: Border.all(
                  width: 8.w,
                  color:
                      selectedUsbPath == path
                          ? const Color(0xff0EC6D2)
                          : const Color(0xff313E50),
                ),
                borderRadius: BorderRadius.circular(24.w),
              ),
              child: Row(
                children: [
                  Container(
                    width: 280.w,
                    height: 280.h,
                    padding: EdgeInsets.symmetric(
                      vertical: 60.h,
                      horizontal: 60.w,
                    ),
                    margin: EdgeInsets.only(right: 48.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(140.w),
                      gradient: const LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [Color(0xff00CBDA), Color(0xff0261A5)],
                      ),
                    ),
                    child: Image(image: R.image.usb()),
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(name),
                      Text(
                        '${(capacity - available).bitIndent(bitStep: 10, units: ['TB', 'GB', 'MB', 'KB', 'B'])} / ${capacity.bitIndent(bitStep: 10, units: ['TB', 'GB', 'MB', 'KB', 'B'])}',
                        style: TextStyle(
                          fontSize: 56.sp,
                          fontWeight: FontWeight.w300,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
          widgetList.add(box);
          list.value = widgetList;
        }
      }
    }
    return ValueListenableBuilder(
      valueListenable: list,
      builder: (context, value, child) {
        return value.isNotEmpty
            ? GridView.builder(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                mainAxisSpacing: 24.r,
                crossAxisSpacing: 64.r,
                crossAxisCount: 2,
                childAspectRatio: 1255 / 404,
              ),
              itemCount: list.value.length,
              scrollDirection: Axis.vertical,
              itemBuilder: (context, index) {
                return list.value[index];
              },
            )
            : Center(child: Text(S.current.f_hU3eFkkG));
      },
    );
  }
}
