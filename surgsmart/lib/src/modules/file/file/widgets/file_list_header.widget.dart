import 'dart:async';

import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/models/http/doctor.model.dart';
import 'package:surgsmart/src/models/http/file_search_value.modal.dart';
import 'package:surgsmart/src/models/http/procedure.model.dart';
import 'package:surgsmart/src/modules/file/file/file.controller.dart';
import 'package:surgsmart/src/widgets/date_select.widget.dart';
import 'package:surgsmart/src/modules/file/file/widgets/number_input.widget.dart';
import 'package:surgsmart/src/widgets/select.widget.dart';

class FileListHeader extends StatefulWidget {
  const FileListHeader({
    super.key,
    this.doctors,
    this.procedures,
    this.onUpdateAdmissionNumber,
    this.onUpdateProcedureId,
    this.onUpdateUserId,
    this.onExport,
    this.ondDeleteAll,
    this.onUpdateDateTime,
    this.controller,
  });

  final FileController? controller;
  final List<OrgSurgeonInfo>? doctors;
  final List<OrgProcedureInfo>? procedures;

  final void Function(String? admissionNumber)? onUpdateAdmissionNumber;
  final void Function(String? procedureName)? onUpdateProcedureId;
  final void Function(int? userId)? onUpdateUserId;
  final void Function({int? startTime, int? endTime})? onUpdateDateTime;
  final Future Function()? onExport;
  final Future Function()? ondDeleteAll;

  @override
  State<FileListHeader> createState() => _MyMaterialState();
}

class _MyMaterialState extends State<FileListHeader> {
  late FileSearchValue fileSearchValue = FileSearchValue();

  FileController? controller;
  late List<OrgProcedureInfo> proceduresOptions = [];
  late List<OrgSurgeonInfo> doctorsOptions = [];

  @override
  void initState() {
    super.initState();
    controller = widget.controller;
    proceduresOptions = widget.procedures!;
    doctorsOptions = widget.doctors!;
  }

  @override
  void didUpdateWidget(covariant FileListHeader oldWidget) {
    super.didUpdateWidget(oldWidget);
    controller = widget.controller;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Button(
            onPressed: () {
              app.context.pop();
            },
            icon: Icon(Icons.arrow_back_ios, size: 56.r),
            foregroundColor: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(4.w)),
            label: Text(
              S.current.f_43uAm9gr,
              style: TextStyle(fontSize: 56.sp, color: Colors.white),
            ),
            betweenSpace: 15.w,
          ),
          const Spacer(),
          // 住院号搜索框
          if (widget.onUpdateAdmissionNumber != null)
            NumberInput(
              text: fileSearchValue.admissionNumber,
              placeholder: S.current.f_9KZD3fbe,
              onConfirm: (number) {
                fileSearchValue.admissionNumber = number;
                widget.onUpdateAdmissionNumber?.call(
                  fileSearchValue.admissionNumber!,
                );
              },
              onClear: () {
                fileSearchValue.admissionNumber = null;
                widget.onUpdateAdmissionNumber?.call(null);
              },
            ),
          SizedBox(width: 36.w),
          // 手术术式
          Select(
            placeholder: S.current.f_9KZDPrBD,
            width: 886.w,
            height: 102.h,
            backgroundColor: const Color(0xff1D2632),
            borderSide: BorderSide(width: 4.w, color: const Color(0xff313E50)),
            items:
                proceduresOptions
                    .map((option) => SelectItem(text: option.name.toString()))
                    .toList(),

            onSelect: (index) {
              fileSearchValue.procedureName = proceduresOptions[index].name;
              widget.onUpdateProcedureId?.call(fileSearchValue.procedureName!);
            },
            onClear: () {
              fileSearchValue.procedureName = null;
              widget.onUpdateProcedureId?.call(null);
            },
          ),
          SizedBox(width: 36.w),
          // 主刀医生
          Select(
            placeholder: S.current.f_9KZDsFoW,
            width: 420.w,
            height: 102.h,
            backgroundColor: const Color(0xff1D2632),
            borderSide: BorderSide(width: 4.w, color: const Color(0xff313E50)),
            items:
                doctorsOptions
                    .map((option) => SelectItem(text: option.name.toString()))
                    .toList(),
            onSelect: (index) {
              fileSearchValue.userId = doctorsOptions[index].id;
              widget.onUpdateUserId?.call(fileSearchValue.userId!);
            },
            onClear: () {
              fileSearchValue.userId = null;
              widget.onUpdateUserId?.call(null);
            },
          ),
          SizedBox(width: 36.w),
          // 日期选择器
          DateSelect(
            onConfirm: (DateTime? dateTime) {
              if (dateTime != null) {
                fileSearchValue.startTime = getDateTime(dateTime.toString());
                fileSearchValue.endTime = getDateTime(
                  dateTime.toString(),
                  type: 'end',
                );
                widget.onUpdateDateTime?.call(
                  startTime: fileSearchValue.startTime,
                  endTime: fileSearchValue.endTime,
                );
              }
            },
            onClear: () {
              fileSearchValue.startTime = null;
              fileSearchValue.endTime = null;
              widget.onUpdateDateTime?.call();
            },
          ),
          if (widget.onExport != null) ...[
            SizedBox(width: 36.w),
            Button(
              onPressed:
                  controller!.checkedIds.isNotEmpty
                      ? () {
                        widget.onExport?.call();
                      }
                      : null,
              backgroundColor: const Color(0xff1D2632),
              foregroundColor: const Color(0xffffffff),
              borderRadius: BorderRadius.all(Radius.circular(4.0.w)),
              label: Text(
                S.current.f_9KZDlkvO,
                style: TextStyle(fontSize: 48.sp, height: 66 / 48),
              ),
              padding: EdgeInsets.symmetric(horizontal: 56.w, vertical: 34.h),
              borderSide: BorderSide(
                width: 4.w,
                color: const Color(0xff313E50),
              ),
            ),
          ],

          SizedBox(width: 36.w),
          Button(
            onPressed:
                controller!.checkedIds.isNotEmpty
                    ? () {
                      widget.ondDeleteAll?.call();
                    }
                    : null,
            backgroundColor: const Color(0xff1D2632),
            foregroundColor: const Color(0xffFF626C),
            borderRadius: BorderRadius.all(Radius.circular(4.0.w)),
            label: Text(
              S.current.f_9KZD6MVt,
              style: TextStyle(fontSize: 48.sp, height: 66 / 48),
            ),
            padding: EdgeInsets.symmetric(horizontal: 56.w, vertical: 34.h),
            borderSide: BorderSide(width: 4.w, color: const Color(0xff313E50)),
          ),
        ],
      ),
    );
  }

  // 时间转换
  int getDateTime(String date, {String? type}) {
    DateTime dateTime = DateFormat('yyyy-MM-dd HH:mm:ss.SSS').parse(date);

    if (type == 'end') {
      DateTime endDateTime = DateTime(
        dateTime.year,
        dateTime.month,
        dateTime.day,
        23,
        59,
        59,
      );
      return endDateTime.millisecondsSinceEpoch;
    }
    DateTime startDateTime = DateTime(
      dateTime.year,
      dateTime.month,
      dateTime.day,
      0,
      0,
      0,
    );
    return startDateTime.millisecondsSinceEpoch;
  }
}
