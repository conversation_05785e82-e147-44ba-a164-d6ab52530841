import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/models/http/file.model.dart';
import 'package:surgsmart/src/modules/file/file/file.controller.dart';
import 'package:surgsmart/src/widgets/empty.widget.dart';

class FileList extends StatefulWidget {
  const FileList({
    super.key,
    required this.list,
    this.onDelete,
    this.onExport,
    required this.controller,
  });

  final FileController controller;
  final List<SurgeryFileInfo> list;
  final Future Function(SurgeryFileStatus file)? onDelete;
  final Future Function(SurgeryFileStatus file)? onExport;

  @override
  State<FileList> createState() => _FileListState();
}

class _FileListState extends State<FileList> {
  List<SurgeryFileInfo>? list;

  late FileController controller = widget.controller;

  // 选中的id
  List<SurgeryFileStatus> checkedIds = [];

  bool disabled = false;

  @override
  void initState() {
    super.initState();
    list = widget.list;
  }

  @override
  void didUpdateWidget(covariant FileList oldWidget) {
    super.didUpdateWidget(oldWidget);
    disabled = widget.list.isEmpty;
    if (list != widget.list) {
      list = widget.list;
    }
    // 所有 ids 列表发生改变时，清空选中的 ids 列表
    controller = widget.controller;
    checkedIds = controller.checkedIds;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        children: [
          ValueListenableBuilder(
            valueListenable: controller.checkAll,
            builder: (context, value, child) {
              return TableHeader(
                showAdmissionNumber: controller.showAdmissionNumber(),
                check: value,
                disabled: disabled,
                onChanged: (value) {
                  setState(() {
                    if (value) {
                      controller.setCheckedIds(controller.allFileIds);
                      checkedIds = controller.allFileIds;
                    } else {
                      controller.clearCheckedIds();
                      checkedIds = [];
                    }
                  });
                },
              );
            },
          ),
          Expanded(
            child:
                list == null || list!.isEmpty
                    ? EmptyPlaceholder(text: S.current.f_MtReeFd5)
                    : ListView.builder(
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: list?.length ?? 0,
                      itemBuilder: (context, index) {
                        SurgeryFileInfo file = list![index];
                        String resolution =
                            file.videoHeight == 1080 ? '1080P' : '4K';

                        return Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 48.w,
                            vertical: 37.h,
                          ),
                          margin: EdgeInsets.only(bottom: 10.h),
                          decoration: BoxDecoration(
                            color: const Color(0xff22293A),
                            borderRadius: BorderRadius.all(
                              Radius.circular(8.w),
                            ),
                          ),
                          child: Row(
                            children: [
                              Transform.scale(
                                scale: 57.r / 24,
                                child: Checkbox(
                                  value:
                                      checkedIds.any((e) => e.id == file.id)
                                          ? true
                                          : false,
                                  onChanged: (status) {
                                    setState(() {
                                      if (status == true) {
                                        controller.setCheckedIds([
                                          SurgeryFileStatus().initWith(
                                            file.toMap(),
                                          ),
                                        ]);
                                      } else {
                                        controller.removeCheckedIds([
                                          SurgeryFileStatus().initWith(
                                            file.toMap(),
                                          ),
                                        ]);
                                      }
                                      checkedIds = controller.checkedIds;
                                    });
                                  },
                                  fillColor: WidgetStateProperty.resolveWith((
                                    states,
                                  ) {
                                    if (states.contains(WidgetState.selected)) {
                                      return const Color(0xff0EC6D2);
                                    }
                                    return Colors.transparent;
                                  }),
                                  side: const BorderSide(
                                    width: 2,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                              SizedBox(width: 60.w),
                              Expanded(
                                flex: 5,
                                child: Padding(
                                  padding: EdgeInsets.only(right: 36.w),
                                  child: Text(
                                    file.procedure?.name ?? "-",
                                    style: TextStyle(
                                      fontSize: 48.sp,
                                      color: Colors.white,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 2,
                                child: Text(
                                  file.user?.name ?? "-",
                                  style: TextStyle(
                                    fontSize: 48.sp,
                                    color: Colors.white,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                              if (controller.showAdmissionNumber())
                                Expanded(
                                  flex: 4,
                                  child: Padding(
                                    padding: EdgeInsets.only(right: 36.w),
                                    child: Text(
                                      file.admissionNumber ?? '-',
                                      style: TextStyle(
                                        fontSize: 48.sp,
                                        color: Colors.white,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ),
                              Expanded(
                                flex: 4,
                                child: Text(
                                  file.surgeryTime != null
                                      ? formatTimestamp(file.surgeryTime)
                                      : '-',
                                  style: TextStyle(
                                    fontSize: 48.sp,
                                    color: Colors.white,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 3,
                                child: Text(
                                  formatBytes(file.totalFileSize as int),
                                  style: TextStyle(
                                    fontSize: 48.sp,
                                    color: Colors.white,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 2,
                                child: Text(
                                  file.videoHeight != null ? resolution : '-',
                                  style: TextStyle(
                                    fontSize: 48.sp,
                                    color: Colors.white,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 2,
                                child: Text(
                                  file.fileSyncStatus == 10
                                      ? S.current.f_9KZDqSHK
                                      : file.fileSyncStatus == 20
                                      ? S.current.f_9KZDEwhr
                                      : S.current.f_9KZDZZrL,
                                  style: TextStyle(
                                    fontSize: 48.sp,
                                    color: Colors.white,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 3,
                                child: Row(
                                  children: [
                                    if (!controller.disableExport()) ...[
                                      Button(
                                        onPressed: () {
                                          widget.onExport!(
                                            SurgeryFileStatus().initWith(
                                              file.toMap(),
                                            ),
                                          );
                                        },
                                        label: Text(
                                          S.current.f_9KZDrogH,
                                          style: TextStyle(
                                            fontSize: 48.sp,
                                            color: const Color(0xff0EC6D2),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 68.w),
                                    ],
                                     
                                    Button(
                                      onPressed: () {
                                        widget.onDelete!(
                                          SurgeryFileStatus().initWith(
                                            file.toMap(),
                                          ),
                                        );
                                      },
                                      label: Text(
                                        S.current.f_9KZDCczi,
                                        style: TextStyle(
                                          fontSize: 48.sp,
                                          color: const Color(0xffFF626C),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
          ),
        ],
      ),
    );
  }

  // 文件 size 转换
  String formatBytes(int bytes) {
    const List<String> units = ['TB', 'GB', 'MB', 'KB', 'B'];
    double size = bytes.toDouble();
    int unitIndex = 4;
    while (size >= 1024 && unitIndex >= 0) {
      size /= 1024;
      unitIndex--;
    }
    return '${size.toStringAsFixed(2)} ${units[unitIndex]}';
  }

  // 时间戳转换
  String formatTimestamp(int? timestamp) {
    // 创建DateTime对象
    var dateTime = DateTime.fromMillisecondsSinceEpoch(
      timestamp!.toInt(),
    ); // 将秒转换为毫秒
    // 格式化日期时间字符串
    var formattedDateTime =
        '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';

    return formattedDateTime;
  }
}

// TableHeader
class TableHeader extends StatefulWidget {
  const TableHeader({
    super.key,
    this.check,
    this.onChanged,
    this.disabled,
    this.showAdmissionNumber = true,
  });

  final bool? check;
  final bool? disabled;
  final bool showAdmissionNumber;

  final void Function(bool value)? onChanged;

  @override
  State<TableHeader> createState() => _TableHeaderState();
}

class _TableHeaderState extends State<TableHeader> {
  bool check = false;
  bool disabled = false;

  @override
  void initState() {
    super.initState();
    check = widget.check ?? false;
    disabled = widget.disabled ?? false;
  }

  @override
  void didUpdateWidget(covariant TableHeader oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.check != oldWidget.check) {
      check = widget.check ?? false;
    }
    disabled = widget.disabled ?? false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 48.w, vertical: 36.h),
      child: Row(
        children: [
          Transform.scale(
            scale: 57.r / 24,
            child: Checkbox(
              value: check,
              onChanged:
                  disabled
                      ? null
                      : (value) {
                        setState(() {
                          check = value!;
                          widget.onChanged!(check);
                        });
                      },
              fillColor: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.selected)) {
                  return const Color(0xff0EC6D2);
                }
                return Colors.transparent;
              }),
              splashRadius: 0,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              side: const BorderSide(width: 2, color: Colors.white),
            ),
          ),
          SizedBox(width: 60.w),
          Expanded(
            flex: 5,
            child: Text(
              S.current.f_9KZDPrBD,
              style: TextStyle(fontSize: 48.sp, color: Colors.white),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              S.current.f_9KZDsFoW,
              style: TextStyle(fontSize: 48.sp, color: Colors.white),
            ),
          ),
          if (widget.showAdmissionNumber)
            Expanded(
              flex: 4,
              child: Text(
                S.current.f_Cvxo6aSP,
                style: TextStyle(fontSize: 48.sp, color: Colors.white),
              ),
            ),
          Expanded(
            flex: 4,
            child: Text(
              S.current.f_9KZDITYd,
              style: TextStyle(fontSize: 48.sp, color: Colors.white),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              S.current.f_9KZDus2Q,
              style: TextStyle(fontSize: 48.sp, color: Colors.white),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              S.current.f_9KZDxFaT,
              style: TextStyle(fontSize: 48.sp, color: Colors.white),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              S.current.f_9KZD9wxb,
              style: TextStyle(fontSize: 48.sp, color: Colors.white),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              S.current.f_9KZDibKR,
              style: TextStyle(fontSize: 48.sp, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
