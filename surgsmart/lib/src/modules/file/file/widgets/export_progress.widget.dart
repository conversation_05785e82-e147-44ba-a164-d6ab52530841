import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/tools/string_util.dart';

/// 导出进度
// ignore: must_be_immutable
class ExportProgress extends StatefulWidget {
  const ExportProgress({
    super.key,
    this.exported,
    this.all,
    this.exportedProgress,
  });

  final int? exported;
  final int? all;
  final double? exportedProgress;

  @override
  State<ExportProgress> createState() => _ExportProgressState();
}

class _ExportProgressState extends State<ExportProgress> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void didUpdateWidget(covariant ExportProgress oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1802.w,
      height: 358.h,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.08),
        border: Border.all(width: 1.w, color: const Color(0xff999999)),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(S.current.f_9KZDRsc6, style: TextStyle(fontSize: 72.sp)),
              SizedBox(width: 40.w),
              SizedBox(
                width: 924.w,
                child: LinearPercentIndicator(
                  percent: widget.exportedProgress ?? 0,
                  lineHeight: 36.r,
                  barRadius: Radius.circular(18.r),
                  backgroundColor: const Color(0X59D8D8D8),
                  linearGradient: const LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [Color(0xFF5CF5EB), Color(0XFF56B3E3)],
                  ),
                ),
              ),
              SizedBox(width: 40.w),
              SizedBox(
                width: 200.w,
                child: Text(
                  '${((widget.exportedProgress ?? 0) * 100).toInt()}%',
                  style: TextStyle(fontSize: 72.sp),
                ),
              ),
              if ((widget.exportedProgress ?? 0) > 0.95)
                const CircularProgressIndicator(color: Colors.white,),
            ],
          ),
          Text(
            StringUtil.replaceInterpolation(S.current.f2_9KZDV10p, [
              (widget.exported ?? 0),
              (widget.all ?? 0),
            ]),
            style: TextStyle(fontSize: 64.sp),
          ),
        ],
      ),
    );
  }
}
