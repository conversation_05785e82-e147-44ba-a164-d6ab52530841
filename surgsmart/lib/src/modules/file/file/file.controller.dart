import 'dart:async';
import 'dart:io';
import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:medias_kit/core/medias_kit_ffi.dart' as ffi;
import 'package:medias_kit/medias_kit.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/apis/http/device.api.dart';
import 'package:surgsmart/src/apis/http/local.api.dart';
import 'package:surgsmart/src/models/http/doctor.model.dart';
import 'package:surgsmart/src/models/http/file.model.dart';
import 'package:surgsmart/src/models/http/procedure.model.dart';
import 'package:surgsmart/src/modules/file/file/widgets/export_progress.widget.dart';
import 'package:surgsmart/src/modules/file/file/widgets/extra_disk_list.widget.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/tools/string_util.dart';
import 'package:surgsmart/src/tools/toast.dart';
import 'package:surgsmart/src/widgets/modal.widget.dart';

/// 所属模块: file
///
/// 文件
class FileController extends AppController
    with StateMixin<List<SurgeryFileInfo>> {
  FileController(super.key, super.routerState);

  int page = 1;

  int total = 0;

  int pageSize = 4;

  String? admissionNumber;

  int? userId;

  String? procedureName;

  int? startTime;

  int? endTime;

  /// 术式列表
  List<OrgProcedureInfo> procedures = [];

  /// 主刀医生列表
  List<OrgSurgeonInfo> doctors = [];

  /// 所有的文件 ids
  List<SurgeryFileStatus> allFileIds = [];

  /// 选择的文件 ids
  List<SurgeryFileStatus> checkedIds = [];

  /// 导出相关
  /// 选择的文件 id 列表
  List<SurgeryFileStatus> chooseIds = [];

  /// 选择的文件的路径列表
  List<SurgeryFileAttribute> fileAttributes = [];

  /// 选择的磁盘路径
  final selectedUsbPath = "".nullableNotifier;

  // 磁盘信息
  final spaceInfo = HostDevice.share.getSpaceInfo().notifier;

  final checkAll = false.notifier;

  final checkEmpty = true.notifier;

  var exportedProgress = 0.0.notifier;

  int exported = 0;

  static int allExported = 0;

  final surgeryFileListInfo = SurgeryFileListInfo().initWith({}).notifier;

  Timer? timer;

  bool isShowEndExportDialog = false;
  static String? destinationFilePath;

  @override
  void onInit() {}

  @override
  void onReady() async {
    try {
      //上下游关系，先加载术式和医生数据，再加载文件数据
      await Future.wait([loadDoctorData(), loadProcedureData()]);
      await Future.wait([loadFileIds(), loadFileData()]);
      update(LoadState.success(surgeryFileListInfo.value.data));
    } catch (e) {
      update(LoadState.failure(S.current.f_N8DiLkCJ));
    }

    // 更新文件列表
    timer = Timer.periodic(
      const Duration(seconds: 5),
      (Timer timer) async => await loadFileData(),
    );
  }

  @override
  void onClose() {
    timer?.cancel();
    spaceInfo.dispose();
    checkAll.dispose();
    checkEmpty.dispose();
    selectedUsbPath.dispose();
    exportedProgress.dispose();
  }

  bool showAdmissionNumber() {
    return !AppContext.share.authorizeInfo.features.disablePatientInfo;
  }

  bool disableExport() {
    return AppContext.share.authorizeInfo.features.disableExport;
  }

  static cancelFileExport() {
    Helper.share.cancelTask(destinationFilePath ?? '');
  }

  /// 获取文件列表
  Future<void> loadFileData() async {
    surgeryFileListInfo.value =
        await HttpLocalApi<SurgeryFileListInfo>.surgeryFiles(
          admissionNumber: admissionNumber,
          userId: userId,
          procedureName: procedureName,
          startTime: startTime,
          endTime: endTime,
          page: page,
          pageSize: pageSize,
        ).request();

    total = surgeryFileListInfo.value.meta.count;

    spaceInfo.value = HostDevice.share.getSpaceInfo();
  }

  /// 删除文件
  Future<void> deleteFile(List<int> ids, {int? type}) async {
    await HttpLocalApi<ApiModel>.deleteSurgeryFile(surgeryIds: ids).request();
    if (type == 1) {
      // 批量删除
      page = 1;
    } else {
      if (total == (page - 1) * 4 + 1) {
        page -= 1;
      }
      page = page <= 0 ? 1 : page;
    }
    await loadFileIds();
    await loadFileData();
  }

  /// 获取所有文件的 ids
  Future<void> loadFileIds() async {
    checkedIds = [];
    checkAll.value = false;
    allFileIds =
        (await HttpLocalApi<SurgeryFileStatusListInfo>.surgeryFileStatus(
              admissionNumber: admissionNumber,
              userId: userId,
              procedureName: procedureName,
              startTime: startTime,
              endTime: endTime,
            ).request())
            .datas;
  }

  /// 获取术式列表
  Future<void> loadProcedureData() async {
    procedures =
        (await HttpDeviceApi<OrgProcedureListInfo>.currentOrgProcedure()
                .request())
            .datas;
  }

  /// 获取主刀医生列表
  Future<void> loadDoctorData() async {
    doctors =
        (await HttpDeviceApi<OrgSurgeonListInfo>.currentOrgSurgeon().request())
            .datas;
  }

  // 设置选择的文件 ids
  void setCheckedIds(List<SurgeryFileStatus> list) async {
    for (SurgeryFileStatus item in list) {
      if (!checkedIds.any((e) => e.id == item.id)) {
        checkedIds.add(item);
      }
    }
    checkEmpty.value = checkedIds.isEmpty;
    checkAll.value = checkedIds.length == allFileIds.length;
    await loadFileData();
  }

  void removeCheckedIds(List<SurgeryFileStatus> list) async {
    if (list.isEmpty) return;
    for (var i = 0; i < list.length; i++) {
      int index = checkedIds.indexWhere((file) => file.id == list[i].id);
      if (index >= 0) {
        checkedIds.removeAt(index);
      }
    }
    checkEmpty.value = checkedIds.isEmpty;
    checkAll.value = checkedIds.length == allFileIds.length;
    await loadFileData();
  }

  void clearCheckedIds() async {
    checkedIds = [];
    checkEmpty.value = true;
    checkAll.value = false;
    await loadFileData();
  }

  /// 删除校验
  void deleteCheck() {
    // 校验选择的 ids 是否有未同步的数据
    final unsynchronizedIds =
        chooseIds.where((e) => e.fileSyncStatus != 30).toList();
    final ids = chooseIds.map((e) => e.id).toList();
    showDeleteModal(ids: ids, unsynchronizedIds: unsynchronizedIds);
  }

  /// 删除提示弹窗
  Future<void> showDeleteModal({
    required List<int> ids,
    required List<SurgeryFileStatus> unsynchronizedIds,
  }) async {
    String message = '';
    if (chooseIds.length > 1) {
      message = StringUtil.replaceInterpolation(S.current.f1_N4rZ3qCB, [
        chooseIds.length,
      ]);
    } else {
      message = S.current.f_9KZDin17;
    }

    if (unsynchronizedIds.isNotEmpty) {
      if (unsynchronizedIds.length > 1) {
        message += StringUtil.replaceInterpolation(S.current.f1_N4IyLhdc, [
          unsynchronizedIds.length,
        ]);
      } else {
        message += S.current.f_N4IyxRoP;
      }
    }
    Modal(
      title: S.current.f_9KZDxs6w,
      cancelText: S.current.f_9KZD45AL,
      confirmText: S.current.f_9KZDYDv7,
      kind: ModalKind.dialog,
      type: ModalType.error,
      message: message,
      onConfirm: () async {
        try {
          await deleteFile(ids);
          app.logW("删除手术文件ids：${ids.toString()}");
          removeCheckedIds(chooseIds);
          ToastUtils.showToast(
            context!,
            message: S.current.f_9aUbGUbm,
            type: ToastType.success,
          );
          context?.pop();
        } catch (e) {
          app.logW("删除手术文件出错-ids：${ids.toString()}");
          ToastUtils.showToast(
            context!,
            message: '删除出错',
            type: ToastType.error,
          );
        }
      },
    ).show(context!, barrierDismissible: false);
  }

  // 导出选择弹窗
  Future<void> showChooseDiskModal() async {
    isShowEndExportDialog = false;
    bool result = await getProcedureList();
    if (!result) {
      ToastUtils.showToast(context!, message: '本地无文件数据', type: ToastType.error);
      return;
    }

    // 校验外接 U 盘大小和所选文件的所有大小
    int totalFileSize = 0;
    for (var item in fileAttributes) {
      totalFileSize += item.fileSize;
    }

    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        const Color(0xff5CF5EB).withValues(alpha: 0.7),
        const Color(0xff56B3E3).withValues(alpha: 0.7),
      ],
    );
    Modal(
      title: S.current.f_IyINoY1L,
      kind: ModalKind.modal,
      type: ModalType.info,
      onClose: () {
        selectedUsbPath.value = null;
        context?.pop();
      },
      footer: ValueListenableBuilder(
        valueListenable: selectedUsbPath,
        builder: (context, value, child) {
          return Container(
            padding: EdgeInsets.symmetric(horizontal: 64.w, vertical: 64.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    selectedUsbPath.value = null;
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 180.h,
                    padding: EdgeInsets.symmetric(horizontal: 160.w),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.white, width: 4.w),
                      borderRadius: BorderRadius.circular(90.r),
                    ),
                    child: Text(
                      S.current.f_9KZD45AL,
                      style: TextStyle(fontSize: 72.sp, color: Colors.white),
                    ),
                  ),
                ),
                SizedBox(width: 64.w),
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    if (selectedUsbPath.value == null) {
                      return;
                    } else {
                      context.pop();
                      startExportModal();
                    }
                  },
                  child: Opacity(
                    opacity: selectedUsbPath.value == null ? 0.4 : 1,
                    child: Container(
                      alignment: Alignment.center,
                      height: 180.h,
                      padding: EdgeInsets.symmetric(horizontal: 160.w),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(90.r),
                        gradient: gradient,
                      ),
                      child: Text(
                        S.current.f_9KZD5TVS,
                        style: TextStyle(fontSize: 72.sp, color: Colors.white),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
      child: ExtraDiskList(
        selectedUsbPath: selectedUsbPath.value,
        totalFileSize: totalFileSize,
        onExport: (path, {size = 0}) {
          app.logW('选择的导出路径: $path');
          selectedUsbPath.value = path;
        },
        onPullout: () {
          if (selectedUsbPath.value != null) {
            selectedUsbPath.value = null;
            context?.pop();
            showChooseDiskModal();
          }
        },
      ),
    ).show(context!, barrierDismissible: false);
  }

  // 开启导出操作
  void startExportModal() {
    // 执行导出操作
    allExported = fileAttributes.length;
    if (updateExportProgress(
      fileAttributes,
      usbPath: selectedUsbPath.value.toString(),
    )) {
      Modal(
        title: S.current.f_N6JlDFfG,
        kind: ModalKind.result,
        type: ModalType.error,
        closable: false,
        confirmText: S.current.f_9KZDW7PY,
        child: ValueListenableBuilder(
          valueListenable: exportedProgress,
          builder: (context, value, child) {
            return ExportProgress(
              all: allExported,
              exported: exported,
              exportedProgress: exportedProgress.value,
            );
          },
        ),
        onConfirm: () async {
          showStopExportDialog(app.context);
        },
      ).show(app.context, barrierDismissible: false);
    }
  }

  /// 停止导出弹窗
  void showStopExportDialog(BuildContext context) {
    isShowEndExportDialog = true;
    Modal(
      title: S.current.f_9KZDW7PY,
      kind: ModalKind.dialog,
      type: ModalType.error,
      confirmText: S.current.f_9KZDW7PY,
      cancelText: S.current.f_9KZD45AL,
      message: S.current.f_N71KjkOq,
      onConfirm: () async {
        /// 停止导出
        cancelFileExport();
        context.pop();
        isShowEndExportDialog = false;
        
        exportedProgress.value = 0;
        context.pop();
      },
      onCancel: () {
        isShowEndExportDialog = false;
        context.pop();
      },
      onClose: () {
        isShowEndExportDialog = false;
        context.pop();
      },
    ).show(context, barrierDismissible: false);
  }

  /// 导出完成弹窗
  void showExportFinishDialog(BuildContext context) {
    Modal(
      title: S.current.f_zjLzaAir,
      kind: ModalKind.result,
      type: ModalType.info,
      message: S.current.f_9KZDvxul,
      confirmText: S.current.f_9KZD5TVS,
      onConfirm: () async {
        /// 结束
        context.pop();
        if (isShowEndExportDialog) {
          context.pop();
        }
        selectedUsbPath.value = null;
      },
      onClose: () {
        context.pop();
        selectedUsbPath.value = null;
      },
    ).show(context, barrierDismissible: false);
  }

  /// 导出出错弹窗
  void showExportErrorDialog(BuildContext context) {
    Modal(
      title: S.current.f_zBM3LFAZ,
      kind: ModalKind.result,
      type: ModalType.error,
      closable: false,
      message: S.current.f_N71Kd8uQ,
      confirmText: S.current.f_9KZD5TVS,
      onConfirm: () async {
        context.pop();
        if (isShowEndExportDialog) {
          context.pop();
        }
        selectedUsbPath.value = null;
      },
      onClose: () {
        context.pop();
        if (isShowEndExportDialog) {
          context.pop();
        }
        selectedUsbPath.value = null;
      },
    ).show(context, barrierDismissible: false);
  }

  /// 导出进度
  bool updateExportProgress(
    List<SurgeryFileAttribute> fileList, {
    required String usbPath,
  }) {
    var currentFile = fileList[0];
    String sourcePath = currentFile.filePath;
    String destinationDir = "$usbPath/SurgSmart";
    try {
      Directory directory = Directory(destinationDir);
      if (!directory.existsSync()) {
        directory.createSync(recursive: true);
      }
      destinationFilePath = '$destinationDir/${currentFile.fileExportName}';
      Helper.share.copyFile(
        src: sourcePath,
        dst: destinationFilePath!,
        callback: (ffi.CopyFileInfo info) {
          if (info.state == CopyFileState.success) {
            exported += 1;
            exportedProgress.value = 1;
            if (fileList.length > 1) {
              updateExportProgress(
                fileList.sublist(1, fileList.length),
                usbPath: usbPath,
              );
            } else {
              if (context == null || selectedUsbPath.value == null) return;
              // 导出完成
              context?.pop();
              showExportFinishDialog(context!);
              exportedProgress.value = 0;
              exported = 0;
              allExported = 0;
              ToastUtils.showToast(
                context!,
                message: "手术视频已导出至磁盘SurgSmart目录下",
                type: ToastType.success,
              );
            }
          } else if (info.state == CopyFileState.failed) {
            allExported = 0;
            if (context == null || selectedUsbPath.value == null) return;
            context?.pop();
            showExportErrorDialog(context!);
          } else if (info.state == CopyFileState.canceled) {
            selectedUsbPath.value = null;
            allExported = 0;
          } else {
            exportedProgress.value = info.progress;
          }
        },
      );
      return true;
    } catch (e) {
      allExported = 0;
      showExportErrorDialog(context!);
      return false;
    }
  }

  // 获取文件路径列表
  Future<bool> getProcedureList() async {
    List<int> ids = chooseIds.map((e) => e.id).toList();

    final result =
        (await HttpLocalApi<SurgeryFileAttributeListInfo>.surgeryFileAttributes(
              surgeryIds: ids,
            ).request())
            .datas;
    fileAttributes =
        result.where((element) => element.filePath.isNotEmpty).toList();

    if (fileAttributes.isNotEmpty) {
      return true;
    }
    return false;
  }
}
