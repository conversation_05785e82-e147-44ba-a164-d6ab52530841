import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/apis/http/insight.api.dart';
import 'package:surgsmart/src/models/http/file_statistics.dart';
import 'package:surgsmart/src/models/http/report.model.dart';
import 'package:surgsmart/src/models/http/surgery.model.dart';
import 'package:surgsmart/src/tools/app_context.dart';

/// 所属模块: report
///
/// 手术报告
class SurgeryReportController extends AppController with StateMixin<Report> {
  SurgeryReportController(super.key, super.routerState);

  late final int surgeryId;

  late final SurgeryInfo surgeryInfo;

  late Report report;

  late int bodyCavityDuration = 0;

  late int surgeryDuration = 0;

  late List<SurgeryEventInfo> events;

  @override
  void onInit() {
    final arguments = routerState!.extra as ReportArguments;
    surgeryId = arguments.surgeryId;
    surgeryInfo = arguments.surgeryInfo ?? SurgeryInfo();
    app.logW('surgeryInfo: ${surgeryInfo.toMap()}');
  }

  @override
  void onReady() {
    if (AppContext.share.enableBleControl) {
      addBleControlMessage();
    }
    getReportData();
  }

  // BLE 控制消息监听
  void addBleControlMessage() {
    // PeripheralController.instance
    //     .setDataAcceptListener((AcceptData acceptData) {
    //   app.logW("-----------蓝牙数据接收-----------${acceptData.toString()}");
    //   if (AppContext.share.taskState == TaskState.install) {
    //     return;
    //   }
    //   if (acceptData.msgType == ClientMsgType.mtuNotify.index) {
    //     /// 收到mtu通知，通信建立完成,下发token和手术状态
    //     PeripheralController.instance.notify(
    //         msgType: ClientMsgType.authLoginRequest.index,
    //         data: jsonEncode({
    //           "appToken": AppContext.share.authorizeInfo.accessToken,
    //           "deviceId": AppContext.share.authorizeInfo.deviceId
    //         }));
    //     PeripheralController.instance.notify(
    //         msgType: ClientMsgType.surgeryStatus.index,
    //         data: jsonEncode({"surgeryId": null, "isLiveOngoing": false}));
    //     PeripheralController.instance.notify(
    //         msgType: ClientMsgType.networkState.index,
    //         data: jsonEncode(
    //             {"enabled": AppContext.share.networkConnected.value}));
    //   } else if (acceptData.msgType == ClientMsgType.authLoginRequest.index) {
    //     /// 设备授权信息请求
    //     PeripheralController.instance.notify(
    //         msgType: ClientMsgType.authLoginRequest.index,
    //         data: jsonEncode(
    //             {"appToken": AppContext.share.authorizeInfo.accessToken}));
    //   } else if (acceptData.msgType == ClientMsgType.surgeryStart.index) {
    //     /// 手术开启消息
    //     DeviceSurgeryStartMessage message =
    //         DeviceSurgeryStartMessage().initWith(jsonDecode(acceptData.data));
    //     eventBus
    //         .fire(EventBusInfo(type: EventBusType.startSurgery, data: message));
    //   } else if (acceptData.msgType == ClientMsgType.restartDevice.index) {
    //     /// 设备重启消息
    //     DeviceCmd.share.restartDevice();
    //   } else if (acceptData.msgType == ClientMsgType.shutdownDevice.index) {
    //     /// 设备关闭消息
    //     DeviceCmd.share.shutdownDevice();
    //   } else if (acceptData.msgType ==
    //       ClientMsgType.requestM3Disconnect.index) {
    //     /// 断开连接
    //     PeripheralController.instance.stop();
    //   } else if (acceptData.msgType == ClientMsgType.networkState.index) {
    //     PeripheralController.instance.notify(
    //         msgType: ClientMsgType.networkState.index,
    //         data: jsonEncode(
    //             {"enabled": AppContext.share.networkConnected.value}));
    //   } else if (acceptData.msgType == ClientMsgType.controlStatus.index) {
    //     DeviceControlStatus status;
    //     if (AppContext.share.taskState == TaskState.install) {
    //       status = DeviceControlStatus.systemUpdate;
    //     } else if (FileController.allExported != 0) {
    //       status = DeviceControlStatus.exportFile;
    //     } else if (AppContext.share.taskState == TaskState.upload) {
    //       status = DeviceControlStatus.uploadFile;
    //     } else if (app.currentRoute.settings.name == GoPaths.surgeryRoom) {
    //       status = DeviceControlStatus.intraOperative;
    //     } else {
    //       status = DeviceControlStatus.nothing;
    //     }
    //     PeripheralController.instance.notify(
    //         msgType: ClientMsgType.controlStatus.index,
    //         data: jsonEncode(status.toMap()));
    //   }
    // });
  }

  @override
  void onClose() {}

  /// 获取报告数据
  void getReportData() async {
    try {
      bodyCavityDuration = (await HttpInsightApi<
                      SurgeryInBodyDurationInfo>.surgeryInBodyDuration(
                  surgeryId: surgeryId)
              .request())
          .current;
      surgeryDuration =
          (await HttpInsightApi<SurgeryDurationInfo>.surgeryDuration(
                      surgeryId: surgeryId)
                  .request())
              .current;
      events = (await HttpInsightApi<SurgeryEventListInfo>.surgeryEvents(
                  surgeryId: surgeryId)
              .request())
          .datas;
      report = Report(
        bodyCavityDuration: bodyCavityDuration,
        surgeryDuration: surgeryDuration,
        events: events,
      );
      update(LoadState.success(report));
    } catch (error) {
      update(LoadState.failure(S.current.f_N8UjvEpd));
    }
  }
}

class ReportArguments {
  final int surgeryId;
  final SurgeryInfo? surgeryInfo;

  ReportArguments({required this.surgeryId, required this.surgeryInfo});
}
