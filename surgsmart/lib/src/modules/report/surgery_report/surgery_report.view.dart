import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/apis/app_domain.dart';
import 'package:surgsmart/src/models/http/report.model.dart';
import 'package:surgsmart/src/modules/report/surgery_report/widgets/header.widget.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/widgets/app_background.widget.dart';
import 'package:surgsmart/src/widgets/app_status_bar.widget.dart';

import '../../../tools/string_util.dart';
import 'surgery_report.controller.dart';

/// 所属模块: report
///
/// 手术报告
class SurgeryReportView extends AppView<SurgeryReportController> {
  const SurgeryReportView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    // 注: 如果 AppBar 与数据有关联, 则把 controller.build() 提升为顶级节点
    return Scaffold(
      body: AppBackground(
        child: controller.build(
          onSuccess: onSuccess,
          onFailure: onFailure,
          onLoading: onLoading,
          onEmpty: onEmpty,
        ),
      ),
    );
  }

  String getDisplayLabel(SurgeryEventInfo event) {
    if (Intl.getCurrentLocale() == 'zh_CN') {
      return event.label;
    }
    //算法返回的英文名称按产品翻译替换，处理国际化后Ui展示问题
    if (1110294001 == event.value) {
      return 'Stapling';
    } else if (1110295001 == event.value) {
      return 'Transect';
    }
    return StringUtil.firstUpperCase(event.name) ?? "";
  }

  Widget onSuccess(BuildContext context, Report value) {
    final report = value;
    Iterable<SurgeryEvent> eventWidgets = report.events.map((event) {
      return SurgeryEvent(
        id: event.value,
        controller: controller,
        title: getDisplayLabel(event),
        total: event.thisSurgeryCount,
        average: event.overallAverageCount,
        surgeryDuration: event.thisSurgeryDuration,
        averageDuration: event.overallAverageDuration,
      );
    });

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 180.w, vertical: 60.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Header(),
          Text(S.current.f_MWRsBLOm, style: TextStyle(fontSize: 110.sp)),
          SizedBox(height: 24.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 720.w,
                height: 716.h,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // 体腔内时长
                    TotalTime(
                      title: S.current.f_93m7ZGt8,
                      image: R.image.date(),
                      time: formatTime(report.bodyCavityDuration),
                    ),
                    // 手术总时长
                    TotalTime(
                      title: S.current.f_9DzIvYZR,
                      image: R.image.time(),
                      time: formatTime(report.surgeryDuration),
                    ),
                  ],
                ),
              ),
              ...eventWidgets,
              // 二维码扫码查看报告
              Container(
                width: 777.w,
                height: 716.h,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.08),
                  borderRadius: BorderRadius.circular(18.r),
                  border: Border.all(
                    width: 1.w,
                    color: const Color(0xff979797),
                  ),
                ),
                child:
                    AppContext.share.networkConnected.value
                        ? Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Stack(
                              children: [
                                QrImageView(
                                  data:
                                      '${AppDomain.share.webBaseUri.origin}/surgery-report?id=${controller.surgeryId}',
                                  size: 440.r,
                                  padding: EdgeInsets.all(20.r),
                                  backgroundColor: Colors.white,
                                ),
                                Positioned.fill(
                                  child: Center(
                                    child: Container(
                                      width: 72.r,
                                      height: 72.r,
                                      padding: EdgeInsets.all(8.r),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(
                                          10.r,
                                        ),
                                      ),
                                      child: Image(
                                        image: R.image.company_logo(),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 24.h),
                            Text(
                              textAlign: TextAlign.center,
                              S.current.f_MX8cvixl,
                              style: TextStyle(fontSize: 63.sp),
                            ),
                          ],
                        )
                        : Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal: 60.w,
                            vertical: 60.h,
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SvgPicture.asset(
                                AppContext.share.getNetWorkStatusIcon().keyName,
                                height: 120.h,
                              ),
                              SizedBox(height: 24.h),
                              Text(
                                S.current.f_9DzInhR4,
                                style: TextStyle(fontSize: 72.sp),
                              ),
                            ],
                          ),
                        ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const AppStatusBar(showUpdate: false),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_rounded, size: 200.sp, color: Colors.white),
                SizedBox(height: 40.h),
                Text(error, style: TextStyle(fontSize: 64.sp)),
                SizedBox(height: 40.h),
                Button(
                  onPressed: () {
                    context.pop();
                  },
                  label: Text(
                    S.current.f_HF0wmcnY,
                    style: TextStyle(fontSize: 64.sp),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget? onLoading(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const AppStatusBar(showUpdate: false),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CupertinoActivityIndicator(
                  radius: 160.r,
                  color: const Color(0xFF297BFF),
                ),
                SizedBox(height: 40.h),
                Text(S.current.f_MX8caoa5, style: TextStyle(fontSize: 64.sp)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }

  // 手术时长转换
  String formatTime(int time) {
    String formattedDateTime = '';
    if (time < 60) {
      formattedDateTime = '00:00:${_addLeadingZero(time)}';
    } else if (time >= 60 && time < 3600) {
      formattedDateTime =
          '00:${_addLeadingZero(time ~/ 60)}:${_addLeadingZero(time % 60)}';
    } else if (time >= 3600) {
      formattedDateTime =
          '${_addLeadingZero(time ~/ 3600)}:${_addLeadingZero(time % 3600 ~/ 60)}:${_addLeadingZero(time % 60)}';
    }
    return formattedDateTime;
  }

  String _addLeadingZero(int value) {
    return value < 10 ? '0$value' : '$value';
  }
}

/// 体腔内/总时长组件
class TotalTime extends StatefulWidget {
  const TotalTime({
    super.key,
    required this.title,
    required this.time,
    required this.image,
  });

  final String title;
  final String time;
  final ImageProvider<Object> image;

  @override
  State<TotalTime> createState() => _TotalTimeState();
}

class _TotalTimeState extends State<TotalTime> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 750.w,
      height: 345.h,
      //padding: EdgeInsets.only(right: 48.w),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(18.r),
        border: Border.all(width: 1.w, color: const Color(0xff979797)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Column(
            children: [
              60.verticalSpace,
              Image(image: widget.image, width: 100.w),
            ],
          ),
          SizedBox(width: 20.w),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  widget.title,
                  textAlign: TextAlign.center,
                  style: TextStyle(height: 1.0, fontSize: 90.sp),
                ),
                ShaderMask(
                  shaderCallback:
                      (bounds) => const LinearGradient(
                        begin: Alignment.topCenter,
                        colors: [Color(0xFF5CF5EB), Color(0xFF56B3E3)],
                        end: Alignment.bottomCenter,
                      ).createShader(bounds),
                  child: Text(
                    widget.time,
                    style: GoogleFonts.roboto(
                      shadows: [
                        Shadow(
                          blurRadius: 4,
                          color: Colors.black.withValues(alpha: 0.5),
                          offset: const Offset(0, 2),
                        ),
                      ],
                      textStyle: TextStyle(
                        color: Colors.white,
                        fontSize: 120.sp,
                        fontWeight: FontWeight.w500,
                        height: 130 / 120,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 事件组件
class SurgeryEvent extends StatefulWidget {
  const SurgeryEvent({
    super.key,
    required this.controller,
    required this.id,
    required this.title,
    required this.total,
    required this.average,
    required this.surgeryDuration,
    required this.averageDuration,
  });
  final SurgeryReportController controller;
  final int id;
  final String title;
  final int total;
  final double average;
  final int surgeryDuration;
  final double averageDuration;

  @override
  State<SurgeryEvent> createState() => _SurgeryEventState();
}

class _SurgeryEventState extends State<SurgeryEvent> {
  @override
  Widget build(BuildContext context) {
    bool upDown = true;
    int upDownText = 0;
    if (widget.id == 1110291001) {
      upDown = widget.surgeryDuration >= widget.averageDuration;
      upDownText =
          (upDown
                  ? (widget.surgeryDuration - widget.averageDuration)
                  : (widget.averageDuration - widget.surgeryDuration))
              .round();
    } else {
      upDown = widget.total >= widget.average;
      upDownText =
          (upDown
                  ? (widget.total - widget.average)
                  : (widget.average - widget.total))
              .round();
    }
    Color color = upDown ? const Color(0xffD4353F) : const Color(0xff23D9BD);

    return Container(
      width: 469.w,
      height: 716.h,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(18.r),
        border: Border.all(width: 1.w, color: const Color(0xff979797)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Text(widget.title, style: TextStyle(fontSize: 90.sp, height: 1.0)),
          if (widget.id != 1110291001)
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                ShaderMask(
                  shaderCallback:
                      (bounds) => const LinearGradient(
                        begin: Alignment.topCenter,
                        colors: [Color(0xFF5CF5EB), Color(0xFF56B3E3)],
                        end: Alignment.bottomCenter,
                      ).createShader(bounds),
                  //机器人切割闭合特殊处理
                  child: Text(
                    (1110294001 == widget.id &&
                            widget
                                    .controller
                                    .surgeryInfo
                                    .procedure
                                    ?.featureFlags
                                    ?.isStaplerAmputationDisabled ==
                                true)
                        ? '- '
                        : widget.total.toString(), //机器人
                    style: GoogleFonts.roboto(
                      shadows: [
                        Shadow(
                          blurRadius: 4,
                          color: Colors.black.withValues(alpha: 0.5),
                          offset: const Offset(0, 2),
                        ),
                      ],
                      textStyle: TextStyle(
                        color: Colors.white,
                        fontSize: 180.sp,
                        fontWeight: FontWeight.w500,
                        height: 0.75,
                      ),
                    ),
                  ),
                ),
                ShaderMask(
                  shaderCallback:
                      (bounds) => const LinearGradient(
                        begin: Alignment.topCenter,
                        colors: [Color(0xFF5CF5EB), Color(0xFF56B3E3)],
                        end: Alignment.bottomCenter,
                      ).createShader(bounds),
                  child: Text(
                    S.current.f_EM4kiGUf,
                    style: GoogleFonts.roboto(
                      shadows: [
                        Shadow(
                          blurRadius: 4,
                          color: Colors.black.withValues(alpha: 0.5),
                          offset: const Offset(0, 2),
                        ),
                      ],
                      textStyle: TextStyle(
                        color: Colors.white,
                        fontSize: 56.sp,
                        fontWeight: FontWeight.w500,
                        height: 1,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          if (widget.id == 1110291001)
            ShaderMask(
              shaderCallback:
                  (bounds) => const LinearGradient(
                    begin: Alignment.topCenter,
                    colors: [Color(0xFF5CF5EB), Color(0xFF56B3E3)],
                    end: Alignment.bottomCenter,
                  ).createShader(bounds),
              child: Text(
                formatTime(widget.surgeryDuration),
                style: GoogleFonts.roboto(
                  shadows: [
                    Shadow(
                      blurRadius: 4,
                      color: Colors.black.withValues(alpha: 0.5),
                      offset: const Offset(0, 2),
                    ),
                  ],
                  textStyle: TextStyle(
                    color: Colors.white,
                    fontSize: widget.surgeryDuration < 3600 ? 160.sp : 90.sp,
                    fontWeight: FontWeight.w500,
                    height: 0.75,
                  ),
                ),
              ),
            ),
          Text(
            S.current.f_9DzIuMyu,
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 56.sp),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: //机器人特殊展示
                (upDownText == 0 ||
                        (1110294001 == widget.id &&
                            widget
                                    .controller
                                    .surgeryInfo
                                    .procedure
                                    ?.featureFlags
                                    ?.isStaplerAmputationDisabled ==
                                true))
                    ? [Text('—', style: TextStyle(fontSize: 72.sp))]
                    : [
                      Icon(
                        upDown
                            ? Icons.arrow_drop_up_rounded
                            : Icons.arrow_drop_down_rounded,
                        size: 100.sp,
                        color: color,
                      ),
                      Text(
                        widget.id == 1110291001
                            ? formatTime(upDownText)
                            : '$upDownText',
                        style: TextStyle(fontSize: 72.sp, color: color),
                      ),
                    ],
          ),
        ],
      ),
    );
  }

  // 手术时长转换
  String formatTime(int time) {
    String formattedDateTime = '';
    if (time < 60) {
      formattedDateTime = '00:${_addLeadingZero(time)}';
    } else if (time >= 60 && time < 3600) {
      formattedDateTime =
          '${_addLeadingZero(time ~/ 60)}:${_addLeadingZero(time % 60)}';
    } else if (time >= 3600) {
      formattedDateTime =
          '${_addLeadingZero(time ~/ 3600)}:${_addLeadingZero(time % 3600 ~/ 60)}:${_addLeadingZero(time % 60)}';
    }
    return formattedDateTime;
  }

  String _addLeadingZero(int value) {
    return value < 10 ? '0$value' : '$value';
  }
}
