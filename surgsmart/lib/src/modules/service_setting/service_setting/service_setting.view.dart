import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:surgsmart/src/modules/setting/setting/views/network/network.controller.dart';
import 'package:surgsmart/src/modules/setting/setting/views/network/network.view.dart';
import 'package:surgsmart/src/widgets/app_background.widget.dart';

import 'service_setting.controller.dart';

/// 所属模块: service_setting
///
/// 域名/网络服务设置
class ServiceSettingView extends AppView<ServiceSettingController> {
  const ServiceSettingView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: AppBackground(
      child: controller.build(
        onSuccess: onSuccess,
        onFailure: onFailure,
        onLoading: onLoading,
        onEmpty: onEmpty,
      ),
    ));
  }

  Widget onSuccess(BuildContext context, ApiModel value) {
    return ValueListenableBuilder(
        valueListenable: controller.type,
        builder: (context, type, child) {
          return getContentByType(context);
        });
  }

  Widget serviceUrlSetting() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        getHeader(
          title: '域名设置',
          onNext: () {
            controller.type.value = ServiceType.network;
          },
        ),
        200.verticalSpace,
        SizedBox(
          width: 2420.w,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '请输入域名或IP，连接服务',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 72.sp,
                ),
              ),
              Container(
                width: double.infinity,
                height: 198.h,
                margin: EdgeInsets.only(top: 24.h),
                padding: EdgeInsets.all(46.r),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.r),
                  border: Border.all(
                    color: Colors.white,
                    width: 2.r,
                  ),
                ),
                child: Text(
                  'https://',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 72.sp,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }

  Widget getContentByType(BuildContext context) {
    if (controller.type.value == ServiceType.domain) {
      return serviceUrlSetting();
    } else if (controller.type.value == ServiceType.network ||
        controller.type.value == ServiceType.networkSetting) {
      bool networkSetting = controller.type.value == ServiceType.networkSetting;
      return Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
        getHeader(
          title: networkSetting ? '' : '请选择并设置网络',
          backText: networkSetting ? '返回' : '上一步',
          onNext: networkSetting
              ? null
              : () {
                  controller.type.value = ServiceType.authorization;
                },
          onBack: () {
            if (networkSetting) {
              context.pop();
            } else {
              controller.type.value = ServiceType.domain;
            }
          },
        ),
        Expanded(
          child: Container(
            height: double.infinity,
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 64.w, vertical: 48.h),
            margin: EdgeInsets.only(left: 154.w, right: 154.w.w, bottom: 56.h),
            decoration: BoxDecoration(
              color: const Color(0xff1D2632),
              borderRadius: BorderRadius.circular(24.w),
              border: Border.all(
                width: 8.w,
                color: const Color(0xff313E50),
              ),
            ),
            child: NetworkView(
              key: key,
              binding: (key) => NetworkController(key, null,
                  isSystemSetting:
                      controller.type.value == ServiceType.networkSetting),
            ),
          ),
        ),
      ]);
    }
    return Text(controller.type.value.name);
  }

  getHeader(
      {String? title,
      String? backText,
      Function()? onNext,
      Function()? onBack}) {
    return SizedBox(
      height: 180.h,
      child: Stack(
        children: [
          if (onBack != null)
            Positioned(
              left: 154.w,
              top: 58.h,
              width: 240.w,
              height: 78.h,
              child: Button(
                onPressed: onBack,
                icon: Icon(
                  Icons.arrow_back_ios,
                  size: 56.r,
                  color: Colors.white,
                ),
                foregroundColor: Colors.white,
                label: Text(
                  backText ?? "上一步",
                  style: TextStyle(
                    fontSize: 56.sp,
                    color: Colors.white,
                  ),
                ),
                betweenSpace: 15.w,
              ),
            ),
          if (title != null)
            Center(
              child: Text(
                title,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 80.sp,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          if (onNext != null)
            Positioned(
              right: 154.w,
              top: 58.h,
              width: 240.w,
              height: 78.h,
              child: Button(
                onPressed: onNext,
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      "下一步",
                      style: TextStyle(
                        fontSize: 56.sp,
                        color: Colors.white,
                      ),
                    ),
                    15.horizontalSpace,
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 56.r,
                      color: Colors.white,
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}
