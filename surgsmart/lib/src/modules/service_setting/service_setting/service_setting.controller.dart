import 'package:app_foundation/app_foundation.dart';

enum ServiceType {
  domain,
  network,
  networkSetting,

  authorization;

  static Map<ServiceType, String> labels = {
    ServiceType.domain: '域名设置',
    ServiceType.network: '网络设置',
    ServiceType.authorization: '认证授权',
  };
}

/// 所属模块: service_setting
///
/// 域名、网络设置
class ServiceSettingController extends AppController with StateMixin<ApiModel> {
  ServiceSettingController(super.key, super.routerState);

  final type = ServiceType.domain.notifier;

  @override
  void onInit() {
    if(routerState?.extra != null) {
      final extra = (routerState?.extra as Map<String, dynamic>);
      type.value = extra['type'] as ServiceType;
    }

    update(LoadState.success(ApiModel()));
  }

  @override
  void onReady() {}

  @override
  void onClose() {}
}
