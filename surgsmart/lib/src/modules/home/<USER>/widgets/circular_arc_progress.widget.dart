import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CircularArcProgress extends StatelessWidget {
  const CircularArcProgress({
    super.key,
    required this.available,
    required this.max,
    required this.usedSpace,
  });

  final double usedSpace;
  final double available;
  final double max;

  @override
  Widget build(BuildContext context) {
    double fontSize = 120.sp;
    if (available > 99) {
      fontSize = 100.sp;
    }
    return Stack(
      children: [
        CustomPaint(
          painter: _CircularArcPainter(
            backgroundColor: const Color(0xFF000000).withValues(alpha: 0.1),
            color:
                usedSpace / max > 0.8
                    ? const Color(0xffcc2d48)
                    : const Color(0xFF0EC6D2),
            radius: 180.r,
            value: usedSpace / max,
            width: 22.w,
          ),
          size: Size(320.w, 160.r),
        ),
        Positioned.fill(
          top: 48.h,
          child: Center(
            child: Text.rich(
              TextSpan(
                text: '${available.toInt()}',
                children: [
                  TextSpan(
                    text: 'h',
                    style: TextStyle(color: Colors.white, fontSize: 48.sp),
                  ),
                ],
              ),
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w500,
                fontSize: fontSize,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class _CircularArcPainter extends CustomPainter {
  final Color backgroundColor;
  final Color color;
  final double radius;
  final double width;
  final double value;

  _CircularArcPainter({
    required this.backgroundColor,
    required this.color,
    required this.radius,
    required this.width,
    required this.value,
  });

  @override
  void paint(Canvas canvas, Size size) {
    var paint =
        Paint()
          ..color = backgroundColor
          ..strokeCap = StrokeCap.round
          ..strokeWidth = width
          ..style = PaintingStyle.stroke;
    final rect = Rect.fromCircle(
      center: Offset(size.width / 2, size.width / 2),
      radius: radius - width / 2,
    );
    canvas.drawArc(rect, pi, pi, false, paint);
    paint.color = color;
    canvas.drawArc(rect, pi, pi * value, false, paint);
  }

  @override
  bool shouldRepaint(_CircularArcPainter oldDelegate) {
    return true;
  }
}
