import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/tools/app_context.dart';

class BleScanner extends StatefulWidget {
  final bool roomScan;
  const BleScanner({super.key, this.roomScan = false});

  @override
  State<BleScanner> createState() => BleScannerState();
}

class BleScannerState extends State<BleScanner> {
  final RegExp regex = RegExp(r'^([0-9A-Fa-f]{2}-){5}([0-9A-Fa-f]{2})$');
  //设备名称获取超时时间记录S
  int deviceNameTimeout = 0;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: AppContext.share.bleConnected,
      builder: (context, bleConnected, child) {
        deviceNameTimeout = 0;
        if (widget.roomScan) {
          return Padding(
            padding: EdgeInsets.symmetric(vertical: 25.h),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Image(
                      image: R.image.qrcode_icon(),
                      width: 120.r,
                      height: 120.r,
                    ),
                    Text(
                      S.current.f_9aUb5hVs,
                      style: TextStyle(
                        fontSize: 80.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                        height: 100 / 80,
                      ),
                    ),
                  ],
                ),
                bleConnected
                    ? connectedWidget(widget.roomScan)
                    : scanWidget(widget.roomScan),
              ],
            ),
          );
        }
        return Container(
          width: 642.w,
          height: double.infinity,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: R.image.qr_background(),
              fit: BoxFit.fill,
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xffB5D7F3).withValues(alpha: 0.3),
                offset: const Offset(0, 0),
                blurRadius: 32.w,
              ),
            ],
          ),
          child:
              bleConnected
                  ? connectedWidget(widget.roomScan)
                  : scanWidget(widget.roomScan),
        );
      },
    );
  }

  Widget scanWidget(bool roomScan) {
    return SizedBox.shrink();

    // return ValueListenableBuilder(
    //   valueListenable: AppContext.share.bleAdapterState,
    //   builder: (context, value, child) {
    //     String? address =
    //         PeripheralController.instance
    //             .getDeviceConnectionInfo()
    //             ?.adapterAddress;

    //     if (PeripheralController.instance
    //             .getDeviceConnectionInfo()
    //             ?.adapterState ==
    //         false) {
    //       return hintWidget(widget.roomScan, '请确认蓝牙开关是否开启');
    //     }
    //     if (address == null) {
    //       PeripheralController.instance.start();
    //       Future.delayed(500.milliseconds, () {
    //         setState(() {});
    //       });
    //       return hintWidget(widget.roomScan, '正在获取蓝牙适配器信息');
    //     }
    //     String advertName =
    //         PeripheralController.instance
    //             .getDeviceConnectionInfo()!
    //             .adapterName;
    //     String codeData =
    //         "${AppDomain.share.webBaseUri}?appid=wx9798566f0a933929&path=pages/bluetooth/index.html#wechat-redirect&advertName=$advertName&adapterAddress=$address";
    //     return Column(
    //       crossAxisAlignment: CrossAxisAlignment.center,
    //       children: [
    //         SizedBox(height: roomScan ? 40.h : 50.h),
    //         Stack(
    //           children: [
    //             QrImageView(
    //               data: codeData,
    //               size: 500.r,
    //               backgroundColor: Colors.transparent,
    //               eyeStyle: const QrEyeStyle(
    //                 color: Colors.white,
    //                 eyeShape: QrEyeShape.square,
    //               ),
    //               dataModuleStyle: const QrDataModuleStyle(
    //                 color: Colors.white,
    //                 dataModuleShape: QrDataModuleShape.square,
    //               ),
    //             ),
    //             Positioned.fill(
    //               child: Center(
    //                 child: Container(
    //                   width: 92.r,
    //                   height: 92.r,
    //                   padding: EdgeInsets.all(8.r),
    //                   decoration: BoxDecoration(
    //                     color: const Color(0xff344968),
    //                     borderRadius: BorderRadius.circular(10.r),
    //                   ),
    //                   child: Image(image: R.image.logo_only()),
    //                 ),
    //               ),
    //             ),
    //           ],
    //         ),
    //         if (!roomScan) ...[
    //           SizedBox(height: 70.h),
    //           DottedDashedLine(
    //             height: 10.h,
    //             width: 520.w,
    //             axis: Axis.horizontal,
    //             dashColor: Colors.white,
    //           ),
    //         ],
    //         SizedBox(height: 50.h),
    //         Text(S.current.f_42h1zCDW, style: TextStyle(fontSize: 48.sp)),
    //       ],
    //     );
    //   },
    // );
  }

  Widget connectedWidget(bool roomScan) {
    return SizedBox.shrink();

    // return Column(
    //   children: [
    //     Padding(
    //       padding: EdgeInsets.only(
    //         top: roomScan ? 50.h : 200.h,
    //         left: 40.w,
    //         right: 40.w,
    //         bottom: 40.h,
    //       ),
    //       child: Image(
    //         image: R.image.ble_device(),
    //         height: 154.h,
    //         fit: BoxFit.cover,
    //       ),
    //     ),
    //     FutureBuilder(
    //       future: Future.delayed(500.milliseconds),
    //       builder: (context, snapshot) {
    //         if (snapshot.connectionState == ConnectionState.done) {
    //           String? deviceName =
    //               PeripheralController.instance
    //                   .getDeviceConnectionInfo()
    //                   ?.deviceName;
    //           if (deviceName != null) {
    //             if (regex.hasMatch(deviceName)) {
    //               if (deviceNameTimeout > 10) {
    //                 deviceName = "未知设备";
    //               } else {
    //                 Future.delayed(500.milliseconds).then((value) {
    //                   deviceNameTimeout++;

    //                   setState(() {});
    //                 });
    //                 deviceName = "";
    //               }
    //             }
    //             return SizedBox(
    //               height: 66.h,
    //               child: Text(
    //                 deviceName,
    //                 style: TextStyle(
    //                   fontSize: 48.sp,
    //                   overflow: TextOverflow.ellipsis,
    //                 ),
    //                 maxLines: 1,
    //               ),
    //             );
    //           }
    //         }
    //         return SizedBox(height: 66.h);
    //       },
    //     ),
    //     SizedBox(height: 15.h),
    //     Text(
    //       '已连接',
    //       style: TextStyle(fontSize: 48.sp, color: const Color(0xff00E9F8)),
    //     ),
    //     SizedBox(height: roomScan ? 40.h : 100.h),
    //     if (!roomScan)
    //       DottedDashedLine(
    //         height: 10.h,
    //         width: 520.w,
    //         axis: Axis.horizontal,
    //         dashColor: Colors.white,
    //       ),
    //     SizedBox(height: 30.h),
    //     GestureDetector(
    //       behavior: HitTestBehavior.opaque,
    //       onTap: () {
    //         if (Throttle().checkPass("disconnect")) {
    //           ToastUtils.showToast(context, message: "正在断开请稍等..");
    //           PeripheralController.instance.notify(
    //             msgType: ClientMsgType.m3Disconnect.index,
    //             data: "",
    //             priority: true,
    //           );
    //           Future.delayed(
    //             50.milliseconds,
    //           ).then((value) => PeripheralController.instance.stop());
    //         }
    //       },
    //       child: Container(
    //         width: 526.w,
    //         height: 100.h,
    //         decoration: BoxDecoration(
    //           color: const Color(0xff0EC6D2).withValues(alpha: 0.1),
    //           border: Border.all(width: 4.w, color: const Color(0xff0EC6D2)),
    //           borderRadius: BorderRadius.circular(16.w),
    //         ),
    //         child: Center(
    //           child: Text('断开连接', style: TextStyle(fontSize: 48.sp)),
    //         ),
    //       ),
    //     ),
    //   ],
    // );
  }

  Widget hintWidget(bool roomScan, String hint) {
    return SizedBox.shrink();
    // return GestureDetector(
    //   onTap: () async {
    //     PeripheralController.instance.start();
    //   },
    //   child: Column(
    //     children: [
    //       SizedBox(height: 50.h),
    //       Container(
    //         width: 500.r,
    //         height: 500.r,
    //         decoration: BoxDecoration(
    //           border: Border.all(
    //             color: const Color(0x73FFFFFF),
    //           ),
    //           borderRadius: BorderRadius.circular(8.r),
    //           image: DecorationImage(
    //             image: R.image.qrcode_bg(),
    //           ),
    //         ),
    //         foregroundDecoration: BoxDecoration(
    //           image: DecorationImage(
    //             image: R.image.what_mask(),
    //           ),
    //         ),
    //       ),
    //       if (!roomScan) SizedBox(height: 50.h),
    //       if (!roomScan)
    //         DottedDashedLine(
    //           height: 10.h,
    //           width: 520.w,
    //           axis: Axis.horizontal,
    //           dashColor: Colors.white,
    //         ),
    //       SizedBox(height: roomScan ? 30.h : 60.h),
    //       Text(
    //         hint,
    //         style: TextStyle(fontSize: 48.sp),
    //         textAlign: TextAlign.center,
    //       ),
    //     ],
    //   ),
    // );
  }
}
