import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:dotted_dashed_line/dotted_dashed_line.dart';
import 'package:surgsmart/src/apis/mqtt/device_control.api.dart';
import 'package:surgsmart/src/tools/app_context.dart';

class PhoneScanner extends StatefulWidget {
  final String? url;

  const PhoneScanner({super.key, required this.url});

  @override
  State<PhoneScanner> createState() => _PhoneScannerState();
}

class _PhoneScannerState extends State<PhoneScanner> {
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: AppContext.share.networkConnected,
      builder: (context, networkConnected, child) {
        return Container(
          width: 642.w,
          height: double.infinity,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: R.image.qr_background(),
              fit: BoxFit.fill,
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xffB5D7F3).withValues(alpha: 0.3),
                offset: const Offset(0, 0),
                blurRadius: 32.w,
              ),
            ],
          ),
          child: Column(
            children: [
              SizedBox(height: 32.h),
              Text(S.current.f_42h1zCDW, style: TextStyle(fontSize: 48.sp)),
              SizedBox(height: 24.h),
              if (networkConnected)
                Stack(
                  children: [
                    QrImageView(
                      data: widget.url.toString(),
                      size: 500.r,
                      backgroundColor: Colors.transparent,
                      eyeStyle: const QrEyeStyle(
                        color: Colors.white,
                        eyeShape: QrEyeShape.square,
                      ),
                      dataModuleStyle: const QrDataModuleStyle(
                        color: Colors.white,
                        dataModuleShape: QrDataModuleShape.square,
                      ),
                    ),
                    Positioned.fill(
                      child: Center(
                        child: Container(
                          width: 92.r,
                          height: 92.r,
                          padding: EdgeInsets.all(8.r),
                          decoration: BoxDecoration(
                            color: const Color(0xff344968),
                            borderRadius: BorderRadius.circular(10.r),
                          ),
                          child: Image(image: R.image.logo_only()),
                        ),
                      ),
                    ),
                  ],
                ),
              if (!networkConnected)
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 100.h),
                  child: SvgPicture.asset(
                    AppContext.share.getNetWorkStatusIcon().keyName,
                    height: 280.h,
                    fit: BoxFit.cover,
                  ),
                ),
              SizedBox(height: 32.h),
              DottedDashedLine(
                height: 10.h,
                width: 520.w,
                axis: Axis.horizontal,
                dashColor: Colors.white,
              ),
              SizedBox(height: 24.h),
              if (networkConnected)
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    AppContext.share.controlToken.refresh(
                      discardLast: (token) {
                        if (token != null) {
                          MqttDeviceControlApi.broadcastControlStatus(
                            controlToken: token,
                            status: DeviceControlStatus.tokenExpired,
                          ).publish();
                        }
                        return true;
                      },
                    );
                  },
                  child: Container(
                    width: 526.w,
                    height: 100.h,
                    decoration: BoxDecoration(
                      color: const Color(0xff0EC6D2).withValues(alpha: 0.1),
                      border: Border.all(
                        width: 4.w,
                        color: const Color(0xff0EC6D2),
                      ),
                      borderRadius: BorderRadius.circular(16.w),
                    ),
                    child: Center(
                      child: Text(
                        S.current.f_43uAHda1,
                        style: TextStyle(fontSize: 48.sp),
                      ),
                    ),
                  ),
                ),
              if (!networkConnected)
                Text(S.current.f_zSstejiI, style: TextStyle(fontSize: 48.sp)),
            ],
          ),
        );
      },
    );
  }
}
