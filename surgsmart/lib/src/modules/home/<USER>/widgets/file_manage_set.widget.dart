import 'dart:math';

import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:medias_kit/core/medias_kit_ffi.dart' as ffi;
import 'package:percent_indicator/percent_indicator.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/models/mqtt/file.model.dart';
import 'package:surgsmart/src/modules/home/<USER>/widgets/circular_arc_progress.widget.dart';
import 'package:surgsmart/src/routes/go_paths.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';
import 'package:surgsmart/src/tools/string_util.dart';
import 'package:surgsmart/src/widgets/click_animation.widget.dart';

class FileManageSet extends StatelessWidget {
  const FileManageSet({super.key, this.fileStats, required this.spaceInfo});

  final AllFileStatsInfo? fileStats;
  final ffi.HostDeviceSpaceInfo spaceInfo;
  static int lastPredicatedUploadTime = 0; //预计时间闪烁太快，产品需要优化慢一点
  static int lastUpdateTime = 0;
  static String formatPredicatedUploadTime = '';

  @override
  Widget build(BuildContext context) {
    bool isShowSync = fileStats?.progress != 1.0 && fileStats?.progress != 0;
    // 计算可录制时长、可用空间占比
    int resolution = AppPreferences.resolution.intValue ?? 1080;
    double maxRecordingTime = 1;
    double availableRecordingTime = 1;
    double usedSpace = 0;
    if (resolution == 2160) {
      maxRecordingTime = spaceInfo.capacity * 8 / (24 * 1024 * 1024 * 3600);
      availableRecordingTime =
          spaceInfo.available * 8 / (24 * 1024 * 1024 * 3600);
    } else {
      maxRecordingTime = spaceInfo.capacity * 8 / (6 * 1024 * 1024 * 3600);
      availableRecordingTime =
          spaceInfo.available * 8 / (6 * 1024 * 1024 * 3600);
    }
    usedSpace = maxRecordingTime - availableRecordingTime;

    return Column(
      children: [
        // 文件管理,为点击缩放效果此处固定高度
        SizedBox(
          width: 642.w,
          height: 571.h,
          child: ClickAnimateContainer(
            onTap: () => app.openInner(GoPaths.file),
            child: Container(
              width: double.infinity,
              height: double.infinity,
              margin: EdgeInsets.only(bottom: 64.w),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Color(0xFF374762), Color(0xff25456d)],
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xffB5D7F3).withValues(alpha: 0.54),
                    offset: const Offset(0, 0),
                    blurRadius: 32.w,
                  ),
                ],
                border: Border.all(
                  color: const Color(0xffD9EBF6).withValues(alpha: 0.54),
                  width: 4.w,
                ),
                borderRadius: BorderRadius.all(Radius.circular(32.w)),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(height: 48.h),
                  // 有同步
                  isShowSync
                      ? Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '${S.current.f_43uA1spi} ${(max(min(fileStats!.progress, 1), 0) * 100).toInt()}%',
                              style: TextStyle(fontSize: 64.sp),
                            ),
                            SizedBox(
                              width: 452.w,
                              child: LinearPercentIndicator(
                                percent: max(min(fileStats!.progress, 1), 0),
                                lineHeight: 30.h,
                                barRadius: Radius.circular(15.r),
                                backgroundColor: const Color(0X59D8D8D8),
                                linearGradient: const LinearGradient(
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                  colors: [
                                    Color(0xFF5CF5EB),
                                    Color(0XFF56B3E3),
                                  ],
                                ),
                              ),
                            ),
                            Text(
                              predicatedTime(),
                              style: TextStyle(fontSize: 48.sp),
                            ),
                          ],
                        ),
                      )
                      :
                      // 无同步
                      Expanded(
                        child: CircularArcProgress(
                          usedSpace: usedSpace,
                          available: availableRecordingTime,
                          max: maxRecordingTime,
                        ),
                      ),
                  SizedBox(height: 20.h),
                  if (!isShowSync)
                    Text(
                      S.current.f_43uA5Ogz,
                      style: TextStyle(fontSize: 48.sp),
                    ),
                  SizedBox(height: 20.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image(
                        image: R.image.file(),
                        width: 100.w,
                        height: 100.w,
                        fit: BoxFit.cover,
                      ),
                      SizedBox(width: 20.w),
                      Text(
                        S.current.f_43uAm9gr,
                        style: TextStyle(fontSize: 72.sp),
                      ),
                    ],
                  ),
                  SizedBox(height: 32.h),
                ],
              ),
            ),
          ),
        ),
        SizedBox(
          width: 642.w,
          height: 252.h,
          child: ClickAnimateContainer(
            onTap: () => app.openInner(GoPaths.setting),
            child: Container(
              height: double.infinity,
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Color(0xFF374762), Color(0xff25456d)],
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xffB5D7F3).withValues(alpha: 0.54),
                    offset: const Offset(0, 0),
                    blurRadius: 32.w,
                  ),
                ],
                border: Border.all(
                  color: const Color(0xffD9EBF6).withValues(alpha: 0.54),
                  width: 4.w,
                ),
                borderRadius: BorderRadius.all(Radius.circular(32.w)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image(
                    image: R.image.system_setting(),
                    width: 100.w,
                    height: 100.w,
                    fit: BoxFit.cover,
                  ),
                  SizedBox(width: 20.w),
                  Text(S.current.f_43uAIxZj, style: TextStyle(fontSize: 72.sp)),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  String formatTimestamp(int seconds) {
    if (seconds < 60) {
      return '$seconds ${S.current.f_QKYizx9Z}';
    } else if (seconds < 3600) {
      var minutes = (seconds / 60).floor();
      return '$minutes ${S.current.f_QrCZ9iUJ}';
    } else {
      var hours = (seconds / 3600).floor();
      return StringUtil.replaceInterpolation(S.current.f2_RfJqERRM, [
        hours,
        ((seconds % 3600) / 60).floor(),
      ]);
    }
  }

  //解决闪烁太快bug
  String predicatedTime() {
    if (DateTime.now().millisecondsSinceEpoch - lastUpdateTime > 1000) {
      lastUpdateTime = DateTime.now().millisecondsSinceEpoch;
      lastPredicatedUploadTime = fileStats!.predicatedUploadTime;
    }
    formatPredicatedUploadTime = formatTimestamp(lastPredicatedUploadTime);
    return StringUtil.replaceInterpolation(S.current.f1_43uA7bgG, [
      formatPredicatedUploadTime,
    ]);
  }
}
