import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:medias_kit/medias_kit.dart';
import 'package:screen_retriever/screen_retriever.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/apis/http/auth.api.dart';
import 'package:surgsmart/src/apis/http/device.api.dart';
import 'package:surgsmart/src/apis/http/insight.api.dart';
import 'package:surgsmart/src/apis/http/surgery.api.dart';
import 'package:surgsmart/src/apis/http/upgrade.api.dart';
import 'package:surgsmart/src/apis/http/upload.api.dart';
import 'package:surgsmart/src/apis/mqtt/device_control.api.dart';
import 'package:surgsmart/src/apis/mqtt/device_file.api.dart';
import 'package:surgsmart/src/apis/mqtt/device_status.api.dart';
import 'package:surgsmart/src/models/client_msg_type.model.dart';
import 'package:surgsmart/src/models/event_bus.model.dart';
import 'package:surgsmart/src/models/http/auth.model.dart';
import 'package:surgsmart/src/models/http/file_state.mode.dart';
import 'package:surgsmart/src/models/http/file_statistics.dart';
import 'package:surgsmart/src/models/http/surgery.model.dart';
import 'package:surgsmart/src/models/http/version.mode.dart';
import 'package:surgsmart/src/models/mqtt/device.model.dart';
import 'package:surgsmart/src/models/mqtt/file.model.dart';
import 'package:surgsmart/src/models/network_device_model.dart';
import 'package:surgsmart/src/modules/file/file/file.controller.dart';
import 'package:surgsmart/src/modules/home/<USER>/widgets/file_manage_set.widget.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/surgery_room.controller.dart';
import 'package:surgsmart/src/routes/go_paths.dart';
import 'package:surgsmart/src/tools/device_cmd.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';
import 'package:surgsmart/src/tools/device_log.dart';
import 'package:surgsmart/src/tools/event_bus.dart';
import 'package:surgsmart/src/tools/multi_window.dart';
import 'package:surgsmart/src/tools/string_util.dart';
import 'package:surgsmart/src/widgets/modal.widget.dart';

/// 所属模块: home
///
/// 首页
class HomeController extends AppController
    with StateMixin<ApiModel>, ScreenListener {
  HomeController(super.key, super.routerState);

  @override
  RouteObserver<Route>? get routeObserver => AppRouteObserver.share;

  /// 文件整体同步状态信息
  final allFileStatsInfo = AllFileStatsInfo().initWith({}).notifier;

  /// 手术统计信息
  var surgeryStatisticInfo = SurgeryStatisticInfo().initWith({});

  /// 版本信息
  final versionInfo = VersionInfo().initWith({}).notifier;

  bool needShowRefreshAlert = true;
  bool isShowUpdateAlert = false;
  int lastTaskStateCheckTime = 0;

  //ble defaultDeviceName正则,例：6F-9F-C4-AF-1A-FB
  final RegExp regex = RegExp(r'^([0-9A-Fa-f]{2}-){5}([0-9A-Fa-f]{2})$');

  final spaceInfo = HostDevice.share.getSpaceInfo().notifier;

  MqttApiObserver? onDeviceRestartObserver;
  MqttApiObserver? onDeviceShutdownObserver;
  MqttApiObserver? onAuthLoginObserver;
  MqttApiObserver? onAppRestartObserver;
  MqttApiObserver? onSurgeryStartObserver;
  MqttApiObserver? onAppTaskStateObserver;

  MqttApiObserver? onFileStatsObserver;
  StreamSubscription? startSurgeryObserver;
  StreamSubscription? taskStateObserver;
  StreamSubscription? ethernetObserver;
  StreamSubscription? staplingReportObserver;

  late Timer connectTimer;

  @override
  void didPopNext() {
    super.didPopNext();

    addBleControlMessage();

    HttpInsightApi<SurgeryStatisticInfo>.surgeryStatistic().request().then(
      (value) => surgeryStatisticInfo = value,
    );

    checkVersion();

    spaceInfo.value = HostDevice.share.getSpaceInfo();
  }

  @override
  void onInit() {
    powerListener();
    taskStateChange();
    screenRetriever.addListener(this);

    //启动检测显示多窗口
    MultiWindow.showWindowToAllScreens();

    if (AppContext.share.enableBleControl) {
      startBle();
    }
    connectTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      //10s检测一次网络连接状态
      AppContext.share.checkNetWorkConnect();
      //每分钟检查一次任务状态，1、定时检测更新/下载状态；2、执行某任务完结后触发其他任务
      if (DateTime.now().millisecondsSinceEpoch - lastTaskStateCheckTime >=
          1000 * 60) {
        lastTaskStateCheckTime = DateTime.now().millisecondsSinceEpoch;
        checkVersion();
      }
      // 记录设备运行状态
      checkDeviceRunStatus();
    });
  }

  /// 网络恢复通知尝试
  void broadcastAppStatus() {
    if (AppContext.share.mqttConnected.value) {
      MqttDeviceControlApi.broadcastAppStatus().publish();
      publishOnlineStatus();
    }
    taskStateChange();
  }

  void publishOnlineStatus() {
    MqttDeviceStatusApi.publishDeviceOnline(
      deviceId: AppContext.share.authorizeInfo.deviceId,
      isOnline: true,
    ).publish(retain: true);
  }

  //通知相关状态更新
  void broadcastDeviceStatus(
    ClientMsgType type, {
    DeviceControlStatus? controlStatus,
    String? token,
  }) {
    if (AppContext.share.enableBleControl) {
      // switch (type) {
      //   case ClientMsgType.controlStatus:
      //     PeripheralController.instance.notify(
      //       msgType: ClientMsgType.controlStatus.index,
      //       data: jsonEncode(controlStatus?.toMap()),
      //     );
      //     if (controlStatus == DeviceControlStatus.systemUpdate) {
      //       bleDisconnect();
      //     }
      //     break;
      //   case ClientMsgType.diskFull:
      //     PeripheralController.instance.notify(msgType: type.index, data: "");
      //     break;
      //   default:
      //     break;
      // }
    } else {
      if (AppContext.share.controlToken.last?.isNotEmpty != true) {
        return;
      }
      switch (type) {
        case ClientMsgType.controlStatus:
          MqttDeviceControlApi.broadcastControlStatus(
            controlToken: token!,
            status: controlStatus!,
          ).publish();
          break;
        case ClientMsgType.diskFull: //mqtt无此协议，暂通知创建手术失败
          MqttDeviceControlApi.broadcastControlStatus(
            controlToken: token!,
            status: DeviceControlStatus.createSurgeryFailed,
          ).publish();
          break;
        default:
          break;
      }
    }
  }

  @override
  void onReady() async {
    subscriptEventBus();

    try {
      final authorizeInfo =
          await HttpAuthApi<AuthorizeInfo>.authorize(
            machineCode: AppContext.share.machineCode,
            licenseCode: AppContext.share.licenseCode,
          ).request();

      HttpApiType.surgsmart.update(
        globalHeaders: {"Authorization": "Bearer ${authorizeInfo.accessToken}"},
      );
      AppContext.share.authorizeInfo = authorizeInfo;
      app.logW(authorizeInfo.toMap());

      //上传设备信息
      upDeviceInfo();

      /// 检查版本更新
      await checkVersion();

      /// 检查是否存在手术
      checkOngoingSurgery().whenComplete(() async {
        update(LoadState.success(ApiModel()));
      });
    } catch (error) {
      app.logE(error.toString());
      update(LoadState.failure("授权失败, 请联系售后服务!"));
      return;
    }

    initNetwork();

    /// 检查本地磁盘信息
    checkDiskFileInfo();

    /// 订阅本地 MQTT 消息
    subscriptFileStatusMessage();

    if (AppContext.share.enableBleControl) {
      // 监听蓝牙遥控器写入命令
      addBleControlMessage();
    } else {
      /// 订阅远端遥控器 MQTT 消息
      subscriptRemoteControlMessage();
    }

    /// 订阅设备网络状态
    subscriptNetworkStatus();
    AppContext.share.networkConnected.addListener(subscriptNetworkStatus);

    AppContext.share.mqttConnected.addListener(broadcastAppStatus);

    update(LoadState.success(ApiModel()));
  }

  @override
  void onClose() {
    connectTimer.cancel();
    AppContext.share.networkConnected.removeListener(subscriptNetworkStatus);
    AppContext.share.mqttConnected.removeListener(broadcastAppStatus);

    onDeviceRestartObserver?.cancel();
    onDeviceShutdownObserver?.cancel();
    onAuthLoginObserver?.cancel();
    onAppRestartObserver?.cancel();
    onSurgeryStartObserver?.cancel();
    onAppTaskStateObserver?.cancel();

    onFileStatsObserver?.cancel();
    startSurgeryObserver?.cancel();
    taskStateObserver?.cancel();
    ethernetObserver?.cancel();
    staplingReportObserver?.cancel();

    allFileStatsInfo.dispose();
    versionInfo.dispose();
    bleDisconnect();
    DeviceCmd.share.disablePowerButton(disable: false);
    screenRetriever.removeListener(this);
  }

  @override
  void onScreenEvent(String eventName) async {
    debugPrint('onScreenEvent1: $eventName');
    if (eventName == 'display-removed') {
      List<String> closedNames = await MultiWindow.getDisConnectedWindowNames();
      eventBus.fire(
        EventBusInfo(type: EventBusType.onScreenRemove, data: closedNames),
      );
    } else if (eventName == 'display-added') {
      await MultiWindow.showWindowToAllScreens();
    }

    eventBus.fire(
      EventBusInfo(
        type: EventBusType.screenEvent,
        data: MultiWindow.extensionScreens().isNotEmpty,
      ),
    );
  }

  // BLE 控制消息监听
  void addBleControlMessage() {
    // PeripheralController.instance.setDataAcceptListener((
    //   AcceptData acceptData,
    // ) {
    //   app.logW("-----------蓝牙数据接收-----------${acceptData.toString()}");
    //   if (AppContext.share.taskState == TaskState.install) {
    //     return;
    //   }

    //   if (acceptData.msgType == ClientMsgType.mtuNotify.index) {
    //     /// 收到mtu通知，通信建立完成,下发token和手术状态
    //     PeripheralController.instance.notify(
    //       msgType: ClientMsgType.authLoginRequest.index,
    //       data: jsonEncode({
    //         "appToken": AppContext.share.authorizeInfo.accessToken,
    //         "deviceId": AppContext.share.authorizeInfo.deviceId,
    //       }),
    //     );
    //     PeripheralController.instance.notify(
    //       msgType: ClientMsgType.surgeryStatus.index,
    //       data: jsonEncode({"surgeryId": null, "isLiveOngoing": false}),
    //     );

    //     PeripheralController.instance.notify(
    //       msgType: ClientMsgType.networkState.index,
    //       data: jsonEncode({
    //         "enabled": AppContext.share.networkConnected.value,
    //       }),
    //     );
    //   } else if (acceptData.msgType == ClientMsgType.restartDevice.index) {
    //     /// 设备重启消息
    //     DeviceCmd.share.restartDevice();
    //   } else if (acceptData.msgType == ClientMsgType.shutdownDevice.index) {
    //     /// 设备关闭消息
    //     DeviceCmd.share.shutdownDevice();
    //   } else if (acceptData.msgType == ClientMsgType.restartApp.index) {
    //     /// 设备APP重启消息
    //     DeviceCmd.share.restartApp();
    //   } else if (acceptData.msgType == ClientMsgType.authLoginRequest.index) {
    //     /// 设备授权信息请求
    //     PeripheralController.instance.notify(
    //       msgType: ClientMsgType.authLoginRequest.index,
    //       data: jsonEncode({
    //         "appToken": AppContext.share.authorizeInfo.accessToken,
    //       }),
    //     );
    //   } else if (acceptData.msgType == ClientMsgType.surgeryStart.index) {
    //     /// 手术开启消息
    //     DeviceSurgeryStartMessage message = DeviceSurgeryStartMessage()
    //         .initWith(jsonDecode(acceptData.data));
    //     startSurgery(message);
    //   } else if (acceptData.msgType ==
    //       ClientMsgType.requestM3Disconnect.index) {
    //     /// 断开连接
    //     PeripheralController.instance.stop();
    //   } else if (acceptData.msgType == ClientMsgType.networkState.index) {
    //     PeripheralController.instance.notify(
    //       msgType: ClientMsgType.networkState.index,
    //       data: jsonEncode({
    //         "enabled": AppContext.share.networkConnected.value,
    //       }),
    //     );
    //   } else if (acceptData.msgType == ClientMsgType.controlStatus.index) {
    //     /// 设备当前状态请求

    //     DeviceControlStatus status;
    //     if (AppContext.share.taskState == TaskState.install) {
    //       status = DeviceControlStatus.systemUpdate;
    //     } else if (FileController.allExported != 0) {
    //       status = DeviceControlStatus.exportFile;
    //     } else if (AppContext.share.taskState == TaskState.upload) {
    //       status = DeviceControlStatus.uploadFile;
    //     } else if (app.currentRoute.settings.name == GoPaths.surgeryRoom) {
    //       status = DeviceControlStatus.intraOperative;
    //     } else {
    //       status = DeviceControlStatus.nothing;
    //     }
    //     broadcastDeviceStatus(
    //       ClientMsgType.controlStatus,
    //       controlStatus: status,
    //     );
    //   }
    // });
  }

  /// 订阅本地 MQTT 消息
  void subscriptFileStatusMessage() {
    onFileStatsObserver = MqttDeviceFileApi<AllFileStatsInfo>.onStatsMessage()
        .listen((message) {
          allFileStatsInfo.value = message;
          //app.logW("文件同步mq:${allFileStatsInfo.value.toString()}");
        });
  }

  /// 订阅远端遥控器 MQTT 消息
  void subscriptRemoteControlMessage() {
    /// 监听设备重启消息
    onDeviceRestartObserver =
        MqttDeviceControlApi<DeviceControlMessage>.onRestartRequest().listen((
          message,
        ) {
          app.logW("重启-------------");
          DeviceCmd.share.restartApp();
        });

    /// 监听设备关闭消息
    onDeviceShutdownObserver =
        MqttDeviceControlApi<DeviceControlMessage>.onDeviceShutdown().listen((
          message,
        ) {
          DeviceCmd.share.shutdownDevice();
        });

    /// 监听设备授权信息请求
    onAuthLoginObserver =
        MqttDeviceControlApi<DeviceControlMessage>.onAuthLoginRequest().listen((
          message,
        ) {
          MqttDeviceControlApi.broadcastAuthToken(
            controlToken: message.token!,
            authToken: AppContext.share.authorizeInfo.accessToken,
          ).publish();
        });

    /// 监听设备APP重启消息
    onAppRestartObserver =
        MqttDeviceControlApi<DeviceControlMessage>.onAppRestartRequest().listen(
          (message) {
            DeviceCmd.share.restartApp();
          },
        );

    /// 监听设备手术开启消息
    onSurgeryStartObserver =
        MqttDeviceControlApi<DeviceSurgeryStartMessage>.onSurgeryStartRequest()
            .listen((message) {
              startSurgery(message);
            });

    /// 设备当前状态请求
    onAppTaskStateObserver =
        MqttDeviceControlApi<DeviceControlMessage>.onDeviceState().listen((
          message,
        ) {
          DeviceControlStatus status;
          if (AppContext.share.taskState == TaskState.install) {
            status = DeviceControlStatus.systemUpdate;
          } else if (FileController.allExported != 0) {
            status = DeviceControlStatus.exportFile;
          } else if (AppContext.share.taskState == TaskState.upload) {
            status = DeviceControlStatus.uploadFile;
          } else if (app.currentRoute.settings.name == GoPaths.surgeryRoom) {
            status = DeviceControlStatus.intraOperative;
          } else {
            status = DeviceControlStatus.nothing;
          }
          broadcastDeviceStatus(
            ClientMsgType.controlStatus,
            controlStatus: status,
            token: message.token,
          );
        });
  }

  /// 订阅设备网络状态
  void subscriptNetworkStatus() {
    HostDevice.share.setOnline(AppContext.share.networkConnected.value);
    if (AppContext.share.networkConnected.value) {
      onDeviceRestartObserver?.restore();
      onDeviceShutdownObserver?.restore();
      onAuthLoginObserver?.restore();
      onAppRestartObserver?.restore();
      onSurgeryStartObserver?.restore();
      onAppTaskStateObserver?.restore();

      onlineStatusHandler();
    } else {
      onDeviceRestartObserver?.cancel();
      onDeviceShutdownObserver?.cancel();
      onAuthLoginObserver?.cancel();
      onAppRestartObserver?.cancel();
      onSurgeryStartObserver?.cancel();
      onAppTaskStateObserver?.cancel();
      allFileStatsInfo.value = AllFileStatsInfo().initWith({});
    }
  }

  void subscriptEventBus() {
    // 订阅手术开启
    startSurgeryObserver = eventBus
        .on<EventBusInfo<DeviceSurgeryStartMessage>>()
        .listen((event) {
          if (event.type == EventBusType.startSurgery && event.data != null) {
            startSurgery(event.data!);
          }
        });
    //任务状态改变
    taskStateObserver = eventBus.on<EventBusInfo>().listen((event) {
      if (event.type == EventBusType.taskStateChange) {
        taskStateChange();
      }
    });

    /// 有线网络可用监听
    ethernetObserver = eventBus.on<EventBusInfo>().listen((event) async {
      if (event.type == EventBusType.ethernetAvailable) {
        app.logW("监听有线网络状态: ${event.data}}");
        if (event.data == true) {
          await AppPreferences.selectedNetwork.setString(
            NetWorkType.ethernet.name,
          );
          initNetwork();
        }
      }
    });

    /// 切割闭合异常监听
    staplingReportObserver = eventBus.on<EventBusInfo>().listen((event) async {
      if (event.type == EventBusType.staplingAnomaly &&
          AppContext.share.networkConnected.value) {
        staplingReport();
      }
    });
  }

  /// 检查是否存在手术
  Future<void> checkOngoingSurgery() async {
    final surgery = await HttpSurgeryApi<SurgeryInfo>.ongoing().request();
    if (surgery.id == -1) {
      return;
    }
    final enableAdvanceAi = AppPreferences.enableAdvanceAi.boolValue;
    Navigator.of(
      context!,
    ).popUntil((route) => route.settings.name == GoPaths.home);

    await app.openInner(
      GoPaths.surgeryRoom,
      arguments: {
        "surgeryId": surgery.id,
        "enableAdvanceAi": enableAdvanceAi,
        "isNewSurgery": false,
      },
    );
  }

  /// 检查版本更新
  Future<void> checkVersion() async {
    try {
      versionInfo.value =
          await HttpUpgradeApi<VersionInfo>.checkVersion().request();
      if (AppPreferences.showUpdateAlertVersionId.stringValue !=
          versionInfo.value.newVersionIdentifier) {
        needShowRefreshAlert = true;
      }
      taskStateChange();
    } catch (e) {
      taskStateChange();
    }
  }

  /// 检查设备运行状态
  void checkDeviceRunStatus() async {
    Map<String, dynamic> info = await DeviceCmd.share.runStatus();
    info['deviceId'] = AppContext.share.authorizeInfo.deviceId;
    info['agoraGatewayRtt'] = SurgeryRoomController.agoraRtcStats?.gatewayRtt;
    app.deviceLog(info);
  }

  /// 检查本地磁盘信息
  void checkDiskFileInfo() {
    spaceInfo.value = HostDevice.share.getSpaceInfo();

    HttpInsightApi<SurgeryStatisticInfo>.surgeryStatistic().request().then(
      (value) => surgeryStatisticInfo = value,
    );
  }

  /// 展示更新弹窗
  void showSystemUpdateModal() {
    isShowUpdateAlert = true;
    Modal(
      title: S.current.f_9Tx3J3nv,
      kind: ModalKind.dialog,
      type: ModalType.info,
      cancelText: versionInfo.value.isForce ? null : S.current.f_9Tx3u1Ya,
      confirmText: S.current.f_9Tx3x473,
      closable: false,
      message: StringUtil.replaceInterpolation(
        versionInfo.value.isForce
            ? S.current.f1_9Tx3xdwC
            : S.current.f1_9Tx3CRSk,
        [versionInfo.value.newVersion],
      ),
      onConfirm: () {
        needShowRefreshAlert = false;
        app.context.pop();
        isShowUpdateAlert = false;
        update(LoadState.success(ApiModel()));
        app.openInner(GoPaths.systemUpdate, arguments: versionInfo.value);
      },
      onCancel: () {
        isShowUpdateAlert = false;
        needShowRefreshAlert = false;
        app.context.pop();
        update(LoadState.success(ApiModel()));
      },
    ).show(context!, barrierDismissible: false);

    //后端沟通此字段可能为空，不存储作为对比
    if (versionInfo.value.newVersionIdentifier.isNotEmpty) {
      AppPreferences.showUpdateAlertVersionId.setString(
        versionInfo.value.newVersionIdentifier,
      );
    }
  }

  //遥控开启手术
  void startSurgery(DeviceSurgeryStartMessage message) {
    if (versionInfo.value.isForce) {
      return;
    }
    if (app.currentRoute.settings.name == GoPaths.systemUpdate) {
      broadcastDeviceStatus(
        ClientMsgType.controlStatus,
        controlStatus: DeviceControlStatus.systemUpdate,
        token: message.token,
      );
      return;
    }
    if (AppRouteObserver.share.histories.any(
      (route) => route.settings.name == GoPaths.surgeryRoom,
    )) {
      broadcastDeviceStatus(
        ClientMsgType.controlStatus,
        controlStatus: DeviceControlStatus.surgeryExist,
        token: message.token,
      );
      return;
    }
    int resolution = AppPreferences.resolution.intValue ?? 1080;
    double availableRecordingTime = 1;
    if (resolution == 2160) {
      availableRecordingTime =
          spaceInfo.value.available * 8 / (24 * 1024 * 1024 * 3600);
    } else {
      availableRecordingTime =
          spaceInfo.value.available * 8 / (6 * 1024 * 1024 * 3600);
    }
    if (availableRecordingTime < 10) {
      broadcastDeviceStatus(ClientMsgType.diskFull, token: message.token);
      return;
    }

    HttpSurgeryApi<SurgeryInfo>.create(
          userId: message.userId,
          procedureName: message.procedureName,
          admissionNumber: message.admissionNumber,
          sex: message.sex,
          age: message.age,
          department: message.department,
        )
        .request()
        .then((surgery) async {
          final enableAdvanceAi = message.enableAdvanceAi ?? true;
          await AppPreferences.enableAdvanceAi.setBool(enableAdvanceAi);
          Navigator.of(
            context!,
          ).popUntil((route) => route.settings.name == GoPaths.home);
          app.openInner(
            GoPaths.surgeryRoom,
            arguments: {
              "surgeryId": surgery.id,
              "enableAdvanceAi": enableAdvanceAi,
            },
          );
        })
        .onError((error, stackTrace) {
          broadcastDeviceStatus(
            ClientMsgType.controlStatus,
            controlStatus: DeviceControlStatus.createSurgeryFailed,
          );
        });
  }

  void startBle() {
    //监听设备连接信息
    // PeripheralController.instance.setConnectionStateListener((
    //   DeviceConnectionState connectionState,
    // ) {
    //   app.logW("-----------连接状态-----------${connectionState.toString()}}");

    //   if (!AppContext.share.bleConnected.value && connectionState.connected) {
    //     ToastUtils.showToast(
    //       context!,
    //       message: "遥控器已连接",
    //       type: ToastType.success,
    //     );
    //   } else if (AppContext.share.bleConnected.value &&
    //       !connectionState.connected) {
    //     ToastUtils.showToast(context!, message: "遥控器已断开", type: ToastType.info);
    //   }
    //   AppContext.share.bleConnected.value = connectionState.connected;

    //   //蓝牙开关开启，尝试启动BLE
    //   if (AppContext.share.bleAdapterState.value == false &&
    //       connectionState.adapterState) {
    //     PeripheralController.instance.start();
    //   }

    //   AppContext.share.bleAdapterState.value = connectionState.adapterState;

    //   //连接成功后立即获取设备名称可能失败，这里再次尝试获取
    //   if (connectionState.connected &&
    //       (connectionState.deviceName == null ||
    //           regex.hasMatch(connectionState.deviceName!))) {
    //     Future.delayed(500.milliseconds).then((value) {
    //       PeripheralController.instance.start();
    //     });
    //   }
    // });
    // //启动BLE
    // PeripheralController.instance.start();
  }

  //发送BLE断开通知
  void bleDisconnect() {
    //PeripheralController.instance.stop();
  }

  void powerListener() async {
    Process process = await DeviceCmd.share.powerButton();
    process.stdout.transform(const SystemEncoding().decoder).listen((
      data,
    ) async {
      if (data.toString().contains("KEY_POWER") &&
          data.toString().contains("SYN_REPORT")) {
        showPowerDialog();
      }
      if (data.toString().contains("Testing")) {
        bool res = await DeviceCmd.share.disablePowerButton();
        if (!res) {
          process.kill();
        }
      }
    });

    process.stderr.transform(const SystemEncoding().decoder).listen((data) {
      app.logE('Power Error: $data');
      DeviceCmd.share.disablePowerButton(disable: false);
      process.kill();
    });
  }

  //收到任务状态改变事件，统一处理执行任务
  void taskStateChange() async {
    app.logW("当前任务：${AppContext.share.taskState.name}");

    /// 检查更新
    if (app.currentRoute.settings.name == GoPaths.home &&
        versionInfo.value.hasNewVersion &&
        (versionInfo.value.isForce || versionInfo.value.allowInstall) &&
        !isShowUpdateAlert &&
        needShowRefreshAlert) {
      showSystemUpdateModal();

      /// 强更通知遥控
      if (versionInfo.value.isForce) {
        broadcastDeviceStatus(
          ClientMsgType.controlStatus,
          controlStatus: DeviceControlStatus.forceUpdateDialog,
          token: AppContext.share.controlToken.last,
        );
      }
      return;
    }

    /// 正在协同或直播
    if (AppContext.share.taskState == TaskState.synergyOrLive) {
      if (app.currentRoute.settings.name != GoPaths.surgeryRoom) {
        AppContext.share.taskState = TaskState.nothing;
      } else {
        // 停止上传
        HttpUpLoadApi.stop().request();
        // 停止下载
        HttpUpgradeApi.stopDownload().request();
        return;
      }
    }
    if (AppContext.share.taskState == TaskState.install) {
      if (app.currentRoute.settings.name != GoPaths.systemUpdate) {
        AppContext.share.taskState = TaskState.nothing;
      } else {
        //正在更新则停止上传任务
        HttpUpLoadApi.stop().request();

        broadcastDeviceStatus(
          ClientMsgType.controlStatus,
          controlStatus: DeviceControlStatus.systemUpdate,
          token: AppContext.share.controlToken.last,
        );
        return;
      }
    }
    //修正上传状态
    final fileStatus = await HttpUpLoadApi<FileStatus>.check().request();
    if (AppContext.share.taskState == TaskState.upload) {
      if (!fileStatus.hasRequireUploadFile) {
        AppContext.share.taskState = TaskState.nothing;
      } else {
        //就算在上传也每次告知上传，避免服务上传过程出现异常等,再次唤醒
        HttpUpLoadApi.start().request();
        return;
      }
    }

    if (AppContext.share.taskState == TaskState.download &&
        versionInfo.value.allowInstall) {
      //检查下载状态
      AppContext.share.taskState = TaskState.nothing;
      taskStateChange();
      return;
    }

    // 如果当前空闲
    if (AppContext.share.taskState == TaskState.nothing) {
      //有未上传
      if (fileStatus.hasRequireUploadFile) {
        HttpUpgradeApi.stopDownload().request();
        HttpUpLoadApi.start().request().then((value) {
          AppContext.share.taskState = TaskState.upload;
        });
        return;
      }

      /// 查询是否有更新包
      if (versionInfo.value.hasNewVersion && !versionInfo.value.allowInstall) {
        HttpUpgradeApi.startDownload().request().then((value) {
          AppContext.share.taskState = TaskState.download;
        });
      }
    }
  }

  final String powerTag = "showPowerDialog";

  /// 电源按钮始终浮于顶层，避免被其他弹窗覆盖
  void showPowerDialog() {
    if (app.existedOverlay(tag: powerTag)) {
      return;
    }
    eventBus.fire(EventBusInfo(type: EventBusType.closeSelect));
    String cancelText = S.current.f_7j1BBSoq;
    String confirmText = S.current.f_7j1BDkim;
    String message = S.current.f_7j1BW0Fq;
    if (app.currentRoute.settings.name == GoPaths.surgeryRoom) {
      //手术中
      message = S.current.f_7j1BTjW4;
    } else if (AppContext.share.taskState == TaskState.install) {
      message = S.current.f_7j1BHjcd;
      confirmText = S.current.f_o5jEiu1k;
    } else if (FileController.allExported != 0) {
      message = S.current.f_7j1BG3B2;
    } else if (AppContext.share.taskState == TaskState.upload) {
      message = StringUtil.replaceInterpolation(S.current.f1_7j1BfrfN, [
        FileManageSet.formatPredicatedUploadTime,
      ]);
    } else {
      cancelText = S.current.f_9KZD45AL;
      confirmText = S.current.f_9KZD5TVS;
    }
    bool normalShutdown = confirmText == S.current.f_9KZD5TVS;

    app.addOverlay(
      tag: powerTag,
      child: Positioned.fill(
        child: Container(
          decoration: BoxDecoration(color: Colors.black.withValues(alpha: 0.6)),
          child: Center(
            child: Modal(
              title: S.current.f_7cNYbi0K,
              kind: ModalKind.dialog,
              type: normalShutdown ? ModalType.error : ModalType.warning,
              cancelText: cancelText,
              confirmText: confirmText,
              message: message,
              onConfirm: () async {
                app.removeOverlay(tag: powerTag);

                if (normalShutdown) {
                  DeviceCmd.share.shutdownDevice();
                }
              },
              onCancel: () async {
                app.removeOverlay(tag: powerTag);

                if (normalShutdown) return;
                FileController.cancelFileExport();
                switch (AppContext.share.taskState) {
                  case TaskState.upload:
                    HttpUpLoadApi.stop().request();
                    break;
                  case TaskState.install:
                  case TaskState.synergyOrLive:
                  case TaskState.download:
                  case TaskState.nothing:
                    break;
                }
                DeviceCmd.share.shutdownDevice();
              },
              onClose: () {
                app.removeOverlay(tag: powerTag);
              },
            ),
          ),
        ),
      ),
    );
  }

  // 设备开机提交ip等设备信息
  void upDeviceInfo() async {
    MqttDeviceControlApi.broadcastAppStatus().publish();
    String info = await DeviceCmd.share.getIpInfo();
    HttpDeviceApi.currentInfo(info).request().then((value) {}).onError((
      err,
      t,
    ) {
      app.logE("提交ip失败");
    });
  }

  /// 初始化控制网络是否禁用
  Future<void> initNetwork() async {
    String? selectedNetwork = AppPreferences.selectedNetwork.stringValue;
    Features features = AppContext.share.authorizeInfo.features;

    if (selectedNetwork == null) {
      // 兼容线上机器，此处保持线上机器网络形式不做设置
      // AppContext.share.switchNetwork(
      //     features.networkWired ? NetWorkType.ethernet : NetWorkType.none);
    } else if (selectedNetwork == NetWorkType.none.name) {
      AppContext.share.switchNetwork(NetWorkType.none);
    } else {
      for (var type in NetWorkType.values) {
        bool enable = true;
        if (type == NetWorkType.ethernet) {
          enable = features.networkWired;
        } else if (type == NetWorkType.wifi) {
          enable = features.networkWifi;
        } else if (type == NetWorkType.gsm) {
          enable = features.networkCellular;
        }

        if (selectedNetwork == type.name) {
          if (enable) {
            AppContext.share.switchNetwork(type);
          } else {
            AppContext.share.switchNetwork(NetWorkType.none);
            AppPreferences.selectedNetwork.setString(NetWorkType.none.name);
          }
        }
      }
    }
  }

  // 网络恢复业务处理
  void onlineStatusHandler() {
    publishOnlineStatus();
    staplingReport();
  }

  // 切割闭合事件检查上报
  Future<void> staplingReport() async {
    String? staplingRecord = AppPreferences.staplingRecord.stringValue;
    if (staplingRecord != null) {
      Map<String, dynamic> staplingMap = jsonDecode(staplingRecord);
      staplingMap.removeWhere((key, value) => value);
      try {
        for (var entry in staplingMap.entries) {
          ApiModel apiModel =
              await HttpSurgeryApi<ApiModel>.staplingReport(
                surgeryId: int.parse(entry.key),
              ).request();
          if (apiModel.map?['alarm_status'] == true) {
            staplingMap[entry.key] = true;
          }
        }
      } catch (e) {
        app.logE(e.toString());
      }
      await AppPreferences.staplingRecord.setString(jsonEncode(staplingMap));
    }
  }
}
