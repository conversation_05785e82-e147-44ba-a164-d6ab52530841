import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/src/modules/home/<USER>/widgets/ble_scanner.widget.dart';
import 'package:surgsmart/src/modules/home/<USER>/widgets/file_manage_set.widget.dart';
import 'package:surgsmart/src/modules/home/<USER>/widgets/phone_scanner.widget.dart';
import 'package:surgsmart/src/modules/home/<USER>/widgets/start_surgery.widget.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/widgets/app_background.widget.dart';
import 'package:surgsmart/src/widgets/app_status_bar.widget.dart';
import 'home.controller.dart';

/// 所属模块: home
///
/// 首页
class HomeView extends AppView<HomeController> {
  const HomeView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    // 注: 如果 AppBar 与数据有关联, 则把 controller.build() 提升为顶级节点
    return Scaffold(
      body: AppBackground(
        child: controller.build(
          onSuccess: onSuccess,
          onFailure: onFailure,
          onLoading: onLoading,
          onEmpty: onEmpty,
        ),
      ),
    );
  }

  Widget onSuccess(BuildContext context, ApiModel value) {
    return Column(
      children: [
        ValueListenableBuilder(
          valueListenable: controller.versionInfo,
          builder: (context, versionInfo, child) {
            return AppStatusBar(
              showUpdate: false,
              checkUpdate: versionInfo.hasNewVersion,
              newVersion: versionInfo.newVersion,
            );
          },
        ),
        Expanded(
          child: Container(
            padding: EdgeInsets.only(bottom: 90.h, right: 154.w, left: 154.w),
            child: Row(
              children: [
                //网络操作设备
                if (!AppContext.share.enableBleControl)
                  ValueListenableBuilder(
                    valueListenable: AppContext.share.controlToken.current,
                    builder: (context, value, child) {
                      return PhoneScanner(
                        url: AppContext.share.controlToken.url,
                      );
                    },
                  ),
                //蓝牙操作设备，初始化需一定时间
                if (AppContext.share.enableBleControl)
                  FutureBuilder(
                    future: Future.delayed(1.seconds),
                    builder: (context, snapshot) {
                      return const BleScanner();
                    },
                  ),

                // 开始手术
                ValueListenableBuilder(
                  valueListenable: controller.spaceInfo,
                  builder: (context, spaceInfo, child) {
                    return Expanded(
                      child: StartSurgery(
                        surgeryCount: controller.surgeryStatisticInfo,
                        spaceInfo: spaceInfo,
                      ),
                    );
                  },
                ),
                // 文件管理、设置
                ValueListenableBuilder(
                  valueListenable: controller.spaceInfo,
                  builder: (context, spaceInfo, child) {
                    return ValueListenableBuilder(
                      valueListenable: controller.allFileStatsInfo,
                      builder: (
                        BuildContext context,
                        fileStats,
                        Widget? child,
                      ) {
                        return FileManageSet(
                          spaceInfo: spaceInfo,
                          fileStats: fileStats,
                        );
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return const Column(
      children: [AppStatusBar(showUpdate: false), Expanded(child: SizedBox())],
    );
  }
}
