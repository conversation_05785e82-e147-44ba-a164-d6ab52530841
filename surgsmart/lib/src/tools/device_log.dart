import 'package:app_foundation/app_foundation.dart';
import 'package:logger/logger.dart';

extension DeviceLogger on Application {
  /// 用于设备运行状态的日志记录
  void setDeviceLog(
    Level level, {
    LogFilter? filter,
    LogPrinter? printer,
    LogOutput? output,
  }) {
    _devLogger = Logger(
      level: level,
      filter: filter ?? _AppLogFilter(),
      printer: printer ?? _AppPrettyPrinter(),
      output: output,
    );
  }

  static var _devLogger = Logger(
    filter: _AppLogFilter(),
    printer: _AppPrettyPrinter(),
    level: Level.all,
  );

  /// 信息日志 [Level.info]
  ///
  /// - [message] 日志内容
  void deviceLog(dynamic message) {
    _devLogger.log(Level.info, message);
  }
}

class _AppLogFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    return event.level.value >= level!.value;
  }
}

class _AppPrettyPrinter extends PrettyPrinter {
  _AppPrettyPrinter()
      : super(
          stackTraceBeginIndex: 2,
          methodCount: 3,
        ) {
    PrettyPrinter.defaultLevelEmojis[Level.trace] = '🐥 ';
    PrettyPrinter.defaultLevelEmojis[Level.debug] = '🪲 ';
    PrettyPrinter.defaultLevelEmojis[Level.info] = '💡 ';
    PrettyPrinter.defaultLevelEmojis[Level.warning] = '😿 ';
    PrettyPrinter.defaultLevelEmojis[Level.error] = '😾 ';
    PrettyPrinter.defaultLevelEmojis[Level.fatal] = '🙀 ';
  }

  static final _titleMap = {
    Level.trace: 'Level.trace',
    Level.debug: 'Level.debug',
    Level.info: 'Level.info',
    Level.warning: 'Level.warning',
    Level.error: 'Level.error',
    Level.fatal: 'Level.fatal',
  };

  @override
  List<String> log(LogEvent event) {
    final color = _getLevelColor(event.level);
    var prefix = 'Time: ${getTime(event.time)}';
    prefix += ' =====> ';
    prefix += _titleMap[event.level]!;
    final lines = [
      "\n",
      color(prefix),
    ];
    lines.addAll(super.log(event));
    return lines;
  }

  @override
  String getTime(DateTime time) {
    return time.dateFormat("YYYY-MM-DD hh:mm:ss.SSS");
  }

  AnsiColor _getLevelColor(Level level) {
    AnsiColor? color;
    if (colors) {
      color = levelColors?[level] ?? PrettyPrinter.defaultLevelColors[level];
    }
    return color ?? const AnsiColor.none();
  }
}
