import 'dart:convert';
import 'package:app_foundation/app_foundation.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'package:screen_retriever/screen_retriever.dart';

class MultiWindow {
  //屏幕和窗口的映射集合
  static final Map<String, WindowController> _windowControllers = {};

  static get getAllWindowControllers => _windowControllers;

  // 屏幕列表
  static final List<Display> _screens = [];

  // 获取所有显示器
  static Future<List<Display>> getAllDisplays() async {
    _screens.clear();
    _screens.addAll(await screenRetriever.getAllDisplays());
    for (var screen in _screens) {
      app.logW("屏幕名称：${screen.name}");
    }
    return _screens;
  }

  // 获取扩展屏幕列表
  static List<Display> extensionScreens() {
    return _screens
        .where((screen) => screen.name?.startsWith('eDP') != true)
        .toList();
  }

  static Future<bool> createShowMultiWindow({required Display display}) async {
    WindowController windowController = await DesktopMultiWindow.createWindow(
      jsonEncode({'targetDisplay': display.toJson()}),
    );
    _windowControllers[display.name!] = windowController;
    windowController.show();
    return true;
  }

  static Future<Map<String, dynamic>> sendAllWindowMsg({
    required String method,
    dynamic arguments,
  }) async {
    Map<String, dynamic> results = {};

    for (var name in _windowControllers.keys) {
      try {
        results[name] = await sendWindowMsg(
          displayName: name,
          method: method,
          arguments: arguments,
        );
      } catch (e) {
        app.logE(e.toString());
      }
    }

    return results;
  }

  static Future<dynamic> sendWindowMsg({
    required String displayName,
    required String method,
    dynamic arguments,
  }) async {
    WindowController? windowController = _windowControllers[displayName];
    if (windowController == null) {
      return null;
    }
    final result = await DesktopMultiWindow.invokeMethod(
      windowController.windowId,
      method,
      arguments,
    );
    return result;
  }

  static Future<void> closeWindow({required String displayName}) async {
    var controller = _windowControllers[displayName];
    if (controller == null) return;
    _windowControllers.remove(displayName);
    await controller.close();
  }

  static Future<void> hideWindow({required String displayName}) async {
    var controller = _windowControllers[displayName];
    if (controller == null) return;
    await controller.hide();
  }

  //在所有已连接的屏幕上显示窗口
  static Future<void> showWindowToAllScreens() async {
    await getAllDisplays();
    List<Display> screens = extensionScreens();

    for (var screen in screens) {
      if (screen.name == null) continue;
      if (_windowControllers.containsKey(screen.name)) {
        _windowControllers[screen.name]!.show();
      } else {
        await createShowMultiWindow(display: screen);
      }
    }
  }

  //在所有已连接的屏幕上显示窗口
  // static Future<void> showWindowToAllScreens() async {
  //   await getAllDisplays();
  //   List<Display> screens = extensionScreens();

  //   /// 检查新增屏幕
  //   _windowControllers.forEach((name, controller) {
  //     screens.removeWhere((screen) => screen.name == name);
  //   });
  //   for (var screen in screens) {
  //     if (screen.name == null) continue;
  //     await createShowMultiWindow(display: screen);
  //   }
  // }

  /// 在所有已断开连接的屏幕上关闭或隐藏窗口
  //默认关闭窗口，如果isHide为true则隐藏窗口
  static Future<void> closeDisConnectedWindows({
    bool isHide = false,
    List<String>? names,
  }) async {
    names ??= await getDisConnectedWindowNames();
    for (String name in names) {
      if (isHide) {
        //todo 这里隐藏后无法恢复
        //await hideWindow(displayName: name);
      } else {
        await closeWindow(displayName: name);
      }
    }
  }

  /// 获取所有已断开连接的窗口名称
  static Future<List<String>> getDisConnectedWindowNames() async {
    List<Display> screens = await getAllDisplays();
    List<String> closeWindows = [];
    //检查已断开的屏幕
    _windowControllers.forEach((key, value) {
      if (!screens.any((screen) => screen.name == key)) {
        closeWindows.add(key);
      }
    });
    return closeWindows;
  }
}
