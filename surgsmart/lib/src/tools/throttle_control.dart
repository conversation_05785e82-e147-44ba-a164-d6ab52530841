import 'dart:collection';
// 节流控制

class Throttle {
  Throttle._();
  static final Throttle _instance = Throttle._();

  static Throttle get instance => _instance;

  factory Throttle() => _instance;

  final LinkedHashMap<String, int> clickTimes = LinkedHashMap<String, int>();


  bool checkPass(String key, {int intervalMs = 1000}) {
    //default 1s内只能触发第一次
    final now = DateTime.now().millisecondsSinceEpoch;
    if (clickTimes.containsKey(key) &&
        now - (clickTimes[key] ?? 0) < intervalMs) {
      return false;
    }
    if (clickTimes.length > 100) {
      //存放最近100个事件
      clickTimes.remove(clickTimes.keys.first);
    }
    clickTimes[key] = now;

    return true;
  }
}
