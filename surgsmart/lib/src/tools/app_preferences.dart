import 'package:shared_preferences/shared_preferences.dart';

enum AppPreferences {
  /// 选中的科室组织id
  ///
  /// 类型: [int]
  department,

  /// 分辨率
  ///
  /// 类型: [int]
  resolution,

  /// 帧率
  ///
  /// 类型: [int]
  frameRate,

  /// 是否启用高级 AI, 仅用于恢复手术
  ///
  /// 类型: [bool]
  enableAdvanceAi,

  /// 直播标题, 仅用于恢复手术
  ///
  /// 类型: [String]
  lastLiveTitle,

  /// 遥控器令牌
  ///
  /// 类型: [String]
  controlToken,

  /// 麦克风设置名称
  ///
  /// 类型: [String]
  microphoneSettings,

  /// 扬声器设置名称
  ///
  /// 类型: [String]
  loudspeakerSettings,

  /// 已经显示的更新弹框版本id
  ///
  /// 类型: [String]
  showUpdateAlertVersionId,

  /// 外置摄像头开关状态
  ///
  /// 类型: [bool]
  cameraState,

  /// 外置摄像头视频显示
  ///
  /// 类型: [bool]
  showCameraVideo,

  /// 扬声器提示音设置 0~65535
  ///
  /// 类型: [String]
  deviceTipVolume,

  /// 术中记录视频信号状态,用于判断是否全程无信号输入
  ///
  /// 类型: [bool]
  noSignalAll,

  /// 私有化服务端地址
  ///
  /// 类型: [String]
  privateServerUrl,

  /// 当前选择的网络类型
  ///
  /// 类型: [String]
  selectedNetwork,

  /// 移动网络配置记录，不考虑跳过App在操作系统设置
  ///
  /// 类型: [json String]
  mobileNetworkInfo,

  /// 切割闭合异常上报记录
  ///
  /// 类型: [json String]
  staplingRecord;

  static late final SharedPreferences _prefs;

  static Future<void> init() async {
    SharedPreferences.setPrefix("surgsmart.");
    _prefs = await SharedPreferences.getInstance();
  }

  Future<bool> remove() => _prefs.remove(name);

  bool get isContains => _prefs.containsKey(name);

  bool? get boolValue => _prefs.getBool(name);

  int? get intValue => _prefs.getInt(name);

  double? get doubleValue => _prefs.getDouble(name);

  String? get stringValue => _prefs.getString(name);

  List<String>? get stringListValue => _prefs.getStringList(name);

  Future<bool> setBool(bool value) => _prefs.setBool(name, value);

  Future<bool> setInt(int value) => _prefs.setInt(name, value);

  Future<bool> setDouble(double value) => _prefs.setDouble(name, value);

  Future<bool> setString(String value) => _prefs.setString(name, value);

  Future<bool> setStringList(List<String> value) =>
      _prefs.setStringList(name, value);
}
