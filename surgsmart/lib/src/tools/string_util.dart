class StringUtil {
  /// 替换占位符，用于国际化文本处理
  ///
  /// [template] 模板字符串
  /// [values] 替换值
  ///
  /// 返回替换后的字符串
  static String replaceInterpolation(String template, List<Object> values) {
    RegExp regExp = RegExp(r'{{(\d+)}}');
    // 查找起始下标
    Iterable<RegExpMatch> matches = regExp.allMatches(template);
    List<int> numbers = [];
    for (var match in matches) {
      int? index = int.tryParse(match.group(1) ?? '');
      if (index == null) continue;
      numbers.add(index);
    }
    if (numbers.isEmpty) return template;
    numbers.sort();
    if (numbers.first < 0 ||
        (numbers.last - numbers.first + 1) > values.length) {
      return template;
    }

    // 使用正则表达式替换占位符
    String result = template.replaceAllMapped(regExp, (match) {
      String? group = match.group(1);
      if (group == null) {
        return '';
      }
      // 获取正则表达式匹配的索引
      int index = int.parse(group);
      return values[index - numbers.first].toString();
    });

    return result;
  }

  /// 将首字母大写
  static String? firstUpperCase(String? str) {
    if (str?.isNotEmpty != true) return str;
    String first = str![0];
    if (RegExp(r'^[a-zA-Z]$').hasMatch(first)) {
      return first.toUpperCase() + str.substring(1);
    }
    return str;
  }
}
