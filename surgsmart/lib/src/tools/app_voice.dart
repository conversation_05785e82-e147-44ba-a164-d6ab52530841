import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/src/widgets/smart_voice.widget.dart';

class AppVoice {
  static final String tag = 'AppVoice';
  static int _lastShowTime = 0;

  static void show({
    required DisplayState state,
    String? content,
    int? delayedSeconds,
  }) {
    var widget = Positioned(
      width: 950.w,
      height: 210.h,
      left: 0,
      bottom: 0,
      child: SmartVoice(
        state: state,
        content: content,
      ),
    );
    app.addOverlay(tag: tag, child: widget);
    _lastShowTime = DateTime.now().millisecondsSinceEpoch;
    if (state == DisplayState.activate) {
      Future.delayed(Duration(seconds: 2)).then((val) {
        if (DateTime.now().millisecondsSinceEpoch - _lastShowTime < 1900) {
          return;
        }
        show(state: DisplayState.listening);
      });
    } else if (state == DisplayState.listening) {
      Future.delayed(Duration(seconds: 6)).then((val) {
        if (DateTime.now().millisecondsSinceEpoch - _lastShowTime > 5500) {
          dismiss();
        }
      });
    }

    if (delayedSeconds != null) {
      Future.delayed(Duration(seconds: delayedSeconds)).then((val) {
        dismiss();
      });
    }
  }

  static void dismiss() {
    app.removeOverlay(tag: tag);
  }
}
