import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class EmptyPlaceholder extends StatelessWidget {
  const EmptyPlaceholder({super.key, this.text});

  final String? text;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Text(
        text ?? '暂无数据',
        style: TextStyle(
          color: Colors.white.withValues(alpha: 0.45),
          fontSize: 48.sp,
          height: 32.h / 24.sp,
        ),
      ),
    );
  }
}
