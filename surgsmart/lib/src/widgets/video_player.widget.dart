import 'package:flutter/material.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';

/// 视频播放组件，支持本地和网络视频播放
class VideoWidget extends StatefulWidget {
  final void Function(VideoController controller)? playListener;

  final List<String> path;
  final BoxFit fit;
  final double scale;
  final bool showControls;
  final bool loop;
  final double rate;

  /// 构造参数说明：
  /// - [path] 视频文件路径，支持本地和网络视频
  /// - [fit] 视频填充方式，默认为`BoxFit.contain`
  /// - [scale] 视频缩放比例，默认为1.0
  /// - [showControls] 是否显示控制栏，默认为`false`
  /// - [loop] 是否循环播放，默认为`false`
  /// - [rate] 视频播放速度，默认为1.0
  const VideoWidget({
    required this.path,
    this.fit = BoxFit.contain,
    this.scale = 1.0,
    this.rate = 1.0,
    this.showControls = false,
    this.loop = false,
    this.playListener,
    super.key,
  });
  @override
  State<VideoWidget> createState() => _VideoWidgetState();
}

class _VideoWidgetState extends State<VideoWidget> {
  late final Player player = Player();
  late final VideoController videoController = VideoController(
    player,
    configuration: VideoControllerConfiguration(scale: widget.scale),
  );

  @override
  void initState() {
    super.initState();
    if (widget.path.isEmpty) {
      return;
    }
    player.setVolume(0);
    player.setRate(widget.rate);
    if (widget.loop) {
      player.setPlaylistMode(PlaylistMode.loop);
    }
    List<Media> medias =
        widget.path.map((path) {
          return Media(path);
        }).toList();
    player.open(Playlist(medias));
    widget.playListener?.call(videoController);
  }

  @override
  void dispose() {
    player.stop();
    player.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Video(
      fit: widget.fit,
      controller: videoController,
      controls: NoVideoControls,
    );
  }
}
