import 'package:flutter/material.dart';

class TextSpanDescription {
  final int startIndex;
  final int spanLength;
  final TextStyle style;

  TextSpanDescription(
      {required this.startIndex,
      required this.spanLength,
      required this.style});
}

class TextSpanWidget extends StatelessWidget {
  const TextSpanWidget(
      {super.key,
      required this.text,
      required this.descriptions,
      this.defaultStyle = const TextStyle()});

  final String text;
  final TextStyle defaultStyle;
  final List<TextSpanDescription> descriptions;

  @override
  Widget build(BuildContext context) {
    if (descriptions.isEmpty || text.isEmpty) {
      return Text(text, style: defaultStyle);
    }
    List<TextSpan> textSpan = [];
    for (int i = 0; i < descriptions.length; i++) {
      TextSpanDescription description = descriptions[i];
      if (description.startIndex < 0 || description.startIndex >= text.length) {
        return Text(text, style: defaultStyle);

        // throw Exception(
        //     'startIndex out of range, description.startIndex = ${description.startIndex}, text.length = ${text.length}');
      }
      if (description.spanLength < 0 ||
          description.startIndex + description.spanLength > text.length) {
        return Text(text, style: defaultStyle);

        //throw Exception('spanLength out of range');
      }

      int startIndex = 0;
      int endIndex = description.startIndex;

      if (i != 0) {
        startIndex =
            descriptions[i - 1].startIndex + descriptions[i - 1].spanLength;
      }
      textSpan.add(TextSpan(
          text: text.substring(startIndex, endIndex), style: defaultStyle));
      textSpan.add(TextSpan(
          text: text.substring(description.startIndex,
              description.startIndex + description.spanLength),
          style: description.style));
      if (i == descriptions.length - 1) {
        textSpan.add(TextSpan(
            text: text.substring(
                description.startIndex + description.spanLength, text.length),
            style: defaultStyle));
      }
    }

    return Text.rich(
      TextSpan(
        style: defaultStyle,
        children: textSpan,
      ),
    );
  }
}
