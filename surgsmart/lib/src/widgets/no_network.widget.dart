import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/src/tools/app_context.dart';

class NoNetwork extends StatelessWidget {
  const NoNetwork({super.key, this.size, this.fontSize, this.message});

  final double? size;
  final double? fontSize;
  final String? message;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgPicture.asset(
            AppContext.share.getNetWorkStatusIcon().keyName,
            height: size ?? 160.h,
          ),
          SizedBox(
            height: 24.h,
          ),
          Text(
            message ?? S.current.f_zSstejiI,
            style: TextStyle(
              fontSize: fontSize ?? 48.sp,
            ),
          ),
        ],
      ),
    );
  }
}
