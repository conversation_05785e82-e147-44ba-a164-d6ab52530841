import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/r.g.dart';

class EndoscopeRemind extends StatefulWidget {
  const EndoscopeRemind({
    super.key,
  });

  @override
  State<StatefulWidget> createState() {
    return EndoscopeRemindState();
  }
}

class EndoscopeRemindState extends State<EndoscopeRemind> {

  @override
  void initState() {
    super.initState();

  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 220.h,
      margin: EdgeInsets.all(16.r),
      child: Row(
        children: [
          Text(
            "镜头偏移\n请移动腔镜",
            style: TextStyle(
                fontSize: 36.sp,
                color: Colors.white,
                fontWeight: FontWeight.w400),
          ),
          Image(image: R.image.logo_only()),
        ],
      ),
    );
  }
}
