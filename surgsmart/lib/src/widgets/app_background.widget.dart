import 'package:flutter/material.dart';
import 'package:surgsmart/r.g.dart';

class AppBackground extends StatelessWidget {
  final Color? color;
  final double radius;
  final double width;
  final double height;
  final EdgeInsetsGeometry? padding;

  final Widget child;

  const AppBackground({
    super.key,
    this.color,
    this.padding,
    this.radius = 0,
    this.height = double.infinity,
    this.width = double.infinity,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      clipBehavior: Clip.hardEdge,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(radius),
        color: Colors.black,
        image:
            color == null
                ? DecorationImage(
                  fit: BoxFit.fill,
                  image: ResizeImage(
                    R.image.background(),
                    width: 50,
                    height: 50,
                  ),
                )
                : null,
      ),
      width: width,
      height: height,
      padding: padding,
      child: child,
    );
  }
}
