import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

class ClickAnimateContainer extends StatefulWidget {
  final double? width;
  final double? height;
  final Color? defaultColor;
  final Color? tapDownColor;
  final double? borderRadius;
  final void Function()? onTap;
  final Widget child;
  const ClickAnimateContainer({
    super.key,
    this.onTap,
    this.defaultColor,
    this.tapDownColor,
    this.borderRadius,
    this.width,
    this.height,
    required this.child,
  });

  @override
  State<StatefulWidget> createState() {
    return ClickAnimateContainerState();
  }
}

class ClickAnimateContainerState extends State<ClickAnimateContainer> {
  double? childWidth;
  double? childHeight;
  double? width;
  double? height;
  Color? bgColor;
  Duration animatedDuration = const Duration(milliseconds: 100);
  final GlobalKey _key = GlobalKey();
  RenderBox? renderBox;

  @override
  void initState() {
    if (widget.width != null || widget.height != null) {
      childWidth = widget.width;
      childHeight = widget.height;
      width = widget.width;
      height = widget.height;
    } else {
      WidgetsBinding.instance.addPostFrameCallback(_getSize);
    }
    super.initState();
  }

  void _getSize(duration) {
    renderBox = _key.currentContext?.findRenderObject() as RenderBox?;
    childWidth = renderBox?.size.width;
    childHeight = renderBox?.size.height;
    width = childWidth;
    height = childHeight;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTapDown: (details) {
        if (widget.onTap == null) return;
        setState(() {
          bgColor = widget.tapDownColor;
          if (childWidth != null) {
            width = childWidth! * 0.9;
          }
          if (childHeight != null) {
            height = childHeight! * 0.9;
          }
        });

        Future.delayed(animatedDuration).then((value) {
          if (mounted) {
            setState(() {
              bgColor = null;
              if (childWidth != null) {
                width = childWidth;
              }
              if (childHeight != null) {
                height = childHeight;
              }
            });
          }
        });
      },
      onTap: () {
        if (widget.onTap == null) return;
        Future.delayed(240.milliseconds).then((value) {
          widget.onTap?.call();
        });
      },
      child: Center(
        child: AnimatedContainer(
          key: _key,
          duration: animatedDuration,
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: bgColor ?? widget.defaultColor,
            borderRadius: widget.borderRadius == null
                ? null
                : BorderRadius.circular(widget.borderRadius!),
          ),
          child: widget.child,
        ),
      ),
    );
  }
}
