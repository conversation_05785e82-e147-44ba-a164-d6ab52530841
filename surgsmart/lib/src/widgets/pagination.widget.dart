import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/src/tools/string_util.dart';

class Pagination extends StatefulWidget {
  const Pagination({
    super.key,
    this.currentPage = 1,
    this.showTotal = true,
    this.size = 25,
    required this.total,
    this.onPaginate,
  });

  final int currentPage;
  final bool showTotal;
  final int size;
  final int total;
  final void Function(int)? onPaginate;

  @override
  State<Pagination> createState() => _PaginationState();
}

class _PaginationState extends State<Pagination> {
  int page = 1;

  @override
  void initState() {
    page = widget.currentPage;
    if (page <= 0) {
      page = 1;
    }
    super.initState();
  }

  @override
  void didUpdateWidget(covariant Pagination oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentPage != widget.currentPage) {
      page = widget.currentPage;
    }
  }

  @override
  Widget build(BuildContext context) {
    var pages = (widget.total / widget.size).ceil();
    if (pages == 0) {
      pages = 1;
    }
    List<Widget> items = [];

    // 分页数量少于7页是全部展示
    if (pages <= 7) {
      items = List.generate(pages, (index) => buildPaginationItem(index + 1));
    } else {
      items.add(buildPaginationItem(1));
      // 当前页出于中间位置，前后都有更多按钮，以当前页为基准点，前后各2页，共5页
      if (page - 3 > 1 && page + 3 < pages) {
        items.add(buildPaginationItem(page - 3, label: '···'));
        for (var i = 0; i < 5; i++) {
          items.add(buildPaginationItem(page - 2 + i));
        }
        items.add(buildPaginationItem(page + 3, label: '···'));
        // 当前页处于前面位置，后面有更多按钮，以当前页为基准，从第1页开始到第5页
      } else if (page - 3 <= 1) {
        for (var i = 0; i < 4; i++) {
          items.add(buildPaginationItem(i + 2));
        }
        items.add(buildPaginationItem(6, label: '···'));
        // 当前页处于后面位置，前面有更多按钮，以当前页为基准，从最后一页到倒数第五页
      } else {
        items.add(buildPaginationItem(pages - 5, label: '···'));
        for (var i = 0; i < 4; i++) {
          items.add(buildPaginationItem(pages - 4 + i));
        }
      }
      items.add(buildPaginationItem(pages));
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.showTotal)
          Text(
            StringUtil.replaceInterpolation(S.current.f1_9KZDhINU, [
              widget.total,
            ]),
            style: TextStyle(fontSize: 48.sp, color: const Color(0xD9FFFFFF)),
          ),
        if (widget.showTotal) SizedBox(width: 32.w),
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: previous,
          child: Icon(Icons.chevron_left_outlined, size: 48.sp),
        ),
        SizedBox(width: 10.w),
        ...items,
        SizedBox(width: 10.w),
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: next,
          child: Icon(Icons.chevron_right_outlined, size: 48.sp),
        ),
      ],
    );
  }

  Widget buildPaginationItem(int value, {String? label}) {
    Color color = Colors.transparent;
    Color borderColor = Colors.transparent;
    if (value == page) {
      color = const Color(0xff0EC6D2).withValues(alpha: 0.15);
      borderColor = const Color(0xff0EC6D2);
    }
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => paginate(value),
      child: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          color: color,
          border: Border.all(width: 4.w, color: borderColor),
        ),
        height: 100.h,
        margin: EdgeInsets.symmetric(horizontal: 10.w),
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Text(
          label ?? '$value',
          style: TextStyle(
            color: Colors.white,
            fontSize: 48.sp,
            fontWeight: FontWeight.w500,
            height: 36.h / 28.sp,
          ),
        ),
      ),
    );
  }

  void paginate(int index) {
    if (index != page) {
      setState(() {
        page = index;
      });
      widget.onPaginate?.call(index);
    }
  }

  void previous() {
    if (page > 1) {
      widget.onPaginate?.call(page - 1);
      setState(() {
        page = page - 1;
      });
    }
  }

  void next() {
    var pages = (widget.total / widget.size).ceil();
    if (pages == 0) {
      pages = 1;
    }
    if (page < (widget.total / widget.size).ceil()) {
      widget.onPaginate?.call(page + 1);
      setState(() {
        page = page + 1;
      });
    }
  }
}
