import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CornerContainer extends StatelessWidget {
  const CornerContainer({
    super.key,
    this.cornerColor,
    this.indicatorColor,
    this.backgroundColor,
    this.showCorner = true,
    this.showIndicator = true,
    required this.child,
  });

  final Color? cornerColor;
  final Color? indicatorColor;
  final Color? backgroundColor;
  final bool showCorner;
  final bool showIndicator;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    final side = BorderSide(
      color: cornerColor ?? Colors.white.withValues(alpha: 0.45),
    );

    return Stack(
      children: <Widget>[
        Container(
          color: backgroundColor ?? Colors.white.withValues(alpha: 0.08),
          margin: EdgeInsets.all(2.w),
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          child: child,
        ),
        if (showCorner)
          Positioned(
            left: 0,
            child: Container(
              height: 16.h,
              width: 16.w,
              decoration: BoxDecoration(border: Border(top: side, left: side)),
            ),
          ),
        if (showCorner)
          Positioned(
            right: 0,
            child: Container(
              height: 16.h,
              width: 16.w,
              decoration: BoxDecoration(border: Border(top: side, right: side)),
            ),
          ),
        if (showCorner)
          Positioned(
            bottom: 0,
            left: 0,
            child: Container(
              height: 16.h,
              width: 16.w,
              decoration: BoxDecoration(
                border: Border(bottom: side, left: side),
              ),
            ),
          ),
        if (showCorner)
          Positioned(
            bottom: 0,
            right: 0,
            child: Container(
              height: 16.h,
              width: 16.w,
              decoration: BoxDecoration(
                border: Border(bottom: side, right: side),
              ),
            ),
          ),
        if (showIndicator)
          Positioned(
            right: 8.w,
            top: 8.h,
            child: Container(
              color: indicatorColor ?? Colors.white.withValues(alpha: 0.45),
              height: 8.h,
              width: 8.w,
            ),
          ),
      ],
    );
  }
}
