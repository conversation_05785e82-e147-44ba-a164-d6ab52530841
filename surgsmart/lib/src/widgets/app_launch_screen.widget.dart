import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:surgsmart/r.g.dart';

import 'app_background.widget.dart';

class AppLaunchScreen extends StatefulWidget {
  final double progress;

  const AppLaunchScreen({super.key, this.progress = 0});

  @override
  State<AppLaunchScreen> createState() => _AppLaunchScreenState();
}

class _AppLaunchScreenState extends State<AppLaunchScreen> {
  var showAnimation = false;

  @override
  Widget build(BuildContext context) {
    return AppBackground(
      color: Colors.black,
      child: Column(
        children: [
          Expanded(
            child: Center(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 40.w),
                    child: SvgPicture.asset(
                      R.svg.asset.logos.keyName,
                      width: 400.r,
                      height: 400.r,
                      colorFilter: const ColorFilter.mode(
                        Colors.white,
                        BlendMode.srcIn,
                      ),
                    ),
                  ).animate(
                    onComplete: (controller) {
                      showAnimation = true;
                      setState(() {});
                    },
                  ).fadeIn(
                    delay: 1.seconds,
                    duration: 2.seconds,
                  ),
                  if (showAnimation)
                    DefaultTextStyle(
                      style: TextStyle(
                        fontSize: 250.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                      child: AnimatedTextKit(
                        animatedTexts: [
                          TyperAnimatedText(
                            'SurgSmart M3',
                            speed: 100.ms,
                          ),
                        ],
                        isRepeatingAnimation: false,
                      ),
                    ),
                  // SizedBox(width: 39.h),
                  // Row(
                  //   mainAxisSize: MainAxisSize.min,
                  //   children: [
                  //     SizedBox(
                  //       width: 925.w,
                  //       child: LinearPercentIndicator(
                  //         percent: progress,
                  //         lineHeight: 15.r,
                  //         barRadius: Radius.circular(15.r),
                  //         backgroundColor: const Color(0X59D8D8D8),
                  //         linearGradient: const LinearGradient(
                  //           begin: Alignment.centerLeft,
                  //           end: Alignment.centerRight,
                  //           colors: [
                  //             Color(0xFF5CF5EB),
                  //             Color(0XFF56B3E3),
                  //           ],
                  //         ),
                  //       ),
                  //     ),
                  //     SizedBox(width: 38.w),
                  //     Text(
                  //       "${(progress * 100).round()}%",
                  //       style: TextStyle(color: Colors.white, fontSize: 58.sp),
                  //     ),
                  //   ],
                  // ),
                ],
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.only(top: 64.h, bottom: 48.h),
            alignment: Alignment.center,
            width: double.infinity,
            child: Text(
              "SurgSmart M3 V1",
              style: TextStyle(
                color: const Color(0xA6FFFFFF),
                fontSize: 48.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
