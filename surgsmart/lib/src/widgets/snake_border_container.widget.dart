import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/scheduler.dart';

class SnakeBorderContainer extends StatefulWidget {
  final Color? backgroundColor;

  final Color borderColor;

  final double cornerRadius;

  final double borderWidth;

  final double speed;

  final Widget child;

  const SnakeBorderContainer({
    super.key,
    this.backgroundColor,
    this.borderColor = Colors.grey,
    this.cornerRadius = 0,
    this.borderWidth = 1,
    this.speed = 1,
    required this.child,
  });

  @override
  State<SnakeBorderContainer> createState() => _SnakeBorderContainerState();
}

class _SnakeBorderContainerState extends State<SnakeBorderContainer>
    with SingleTickerProviderStateMixin {
  late final Ticker ticker;

  var time = 0;

  @override
  void initState() {
    super.initState();
    ticker = createTicker(handleTick)..start();
  }

  void handleTick(Duration elapsed) {
    setState(() {
      if (mounted) {
        time++;
      }
    });
  }

  // @override
  // void didUpdateWidget(covariant LightningBoarderContainer oldWidget) {
  //   super.didUpdateWidget(oldWidget);
  //   if (oldWidget != widget) {
  //     _time = 0;
  //   }
  // }

  @override
  void dispose() {
    ticker.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(widget.cornerRadius),
      ),
      child: _SnakeBoarder(
        key: UniqueKey(),
        color: widget.borderColor,
        cornerRadius: widget.cornerRadius,
        borderWidth: widget.borderWidth,
        speed: widget.speed,
        time: time,
        child: widget.child,
      ),
    );
  }
}

class _SnakeBoarder extends SingleChildRenderObjectWidget {
  final Color color;

  final double cornerRadius;

  final double borderWidth;

  final double speed;

  final int time;

  const _SnakeBoarder({
    super.key,
    required this.color,
    required this.cornerRadius,
    required this.borderWidth,
    required this.speed,
    required this.time,
    super.child,
  });

  @override
  RenderObject createRenderObject(BuildContext context) {
    return _LightningContainerRenderBox(
      color,
      cornerRadius,
      borderWidth,
      speed,
      time,
    );
  }
}

class _LightningContainerRenderBox extends RenderProxyBox {
  final Color color;

  final double cornerRadius;

  final double borderWidth;

  final double speed;

  final int time;

  _LightningContainerRenderBox(
    this.color,
    this.cornerRadius,
    this.borderWidth,
    this.speed,
    this.time,
  );

  @override
  void paint(PaintingContext context, Offset offset) {
    super.paint(context, offset);

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth
      ..strokeCap = StrokeCap.round
      ..shader = const LinearGradient(
        begin: Alignment(0, 0),
        end: Alignment(1, 1),
        colors: [Colors.blue, Colors.red],
      ).createShader(offset & size);

    final points = <Offset>[];

    // final totalLen = size.width + size.height / 2;

    final startPoint = offset + Offset(size.width, cornerRadius);

    points.add(startPoint);

    // double len = 0;
    // while (len < totalLen) {
    //   final last = points.last;
    //   if (last.dx == size.width && last.dy >= offset.dy + cornerRadius) {
    //     points.add(last + Offset(0, size.height - borderWidth));
    //     len = size.height - 2 * cornerRadius - last.dy;
    //   }
    // }

    final path = Path();

    path.relativeMoveTo(offset.dx + cornerRadius, offset.dy);
    path.relativeLineTo(size.width - 2 * cornerRadius, 0);
    path.relativeArcToPoint(
      Offset(cornerRadius, cornerRadius),
      radius: Radius.circular(cornerRadius),
      rotation: pi / 2,
    );
    path.relativeLineTo(0, size.height);

    // path.arcTo(
    //   Rect.fromCircle(
    //     center: offset + Offset(size.width - cornerRadius, cornerRadius),
    //     radius: cornerRadius,
    //   ),
    //   0,
    //   -pi / 2,
    //   true,
    // );
    // path.relativeLineTo(size.width, size.height);

    // path.close();

    context.canvas.drawPath(path, paint);
  }
}
