import 'dart:async';

import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/routes/go_paths.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/widgets/modal.widget.dart';

class AppStatusBar extends StatefulWidget {
  const AppStatusBar({
    super.key,
    this.showUpdate,
    this.checkUpdate,
    this.newVersion = 'V1.0.0',
  });

  final bool? showUpdate;
  final bool? checkUpdate;
  final String? newVersion;

  @override
  State<AppStatusBar> createState() => _AppStatusBarState();
}

class _AppStatusBarState extends State<AppStatusBar> {
  Timer? _timer;

  final time = "".notifier;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _timer?.cancel();
      _timer = Timer.periodic(
        const Duration(milliseconds: 100),
        (timer) async {
          time.value = DateTime.now().dateFormat("YYYY/MM/DD   hh:mm");
        },
      );
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    time.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 154.w,
        right: 158.w,
        top: 34.h,
        bottom: 55.h,
      ),
      child: Row(
        children: [
          Image(
            image: R.image.logo_variant(),
            fit: BoxFit.contain,
            width: 385.w,
            height: 90.h,
          ),
          SizedBox(width: 48.w),
          if (widget.showUpdate! && widget.checkUpdate!)
            Button(
              padding: EdgeInsets.all(15.r),
              onPressed: () {
                startExportModal(context);
              },
              label: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 72.sp,
                    color: const Color(0xff397ddd),
                  ),
                  SizedBox(width: 24.w),
                  Text(
                    "检测到新版本，立即更新",
                    style: TextStyle(fontSize: 48.sp, color: Colors.white),
                  ),
                  SizedBox(width: 24.w),
                  Icon(
                    Icons.chevron_right,
                    size: 48.sp,
                    color: Colors.white,
                  ),
                ],
              ),
            ),
          const Spacer(),
          ValueListenableBuilder(
            valueListenable: time,
            builder: (context, value, child) {
              return Text(
                value,
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 68.sp,
                  color: Colors.white,
                ),
              );
            },
          ),
          SizedBox(width: 64.w),
          ValueListenableBuilder(
            valueListenable: AppContext.share.networkConnected,
            builder: (context, networkConnected, child) {
              return SvgPicture.asset(
                AppContext.share.getNetWorkStatusIcon().keyName,
                width: 80.r,
                height: 80.r,
              );
            },
          ),
        ],
      ),
    );
  }

  // 更新弹窗提示
  void startExportModal(BuildContext context) {
    Modal(
      title: S.current.f_9Tx3J3nv,
      kind: ModalKind.dialog,
      type: ModalType.info,
      cancelText: S.current.f_9Tx3u1Ya,
      confirmText: S.current.f_9Tx3x473,
      closable: false,
      message: '检测到新版本${widget.newVersion}, 是否更新?',
      onConfirm: () async {
        context.pop();
        app.openInner(GoPaths.systemUpdate);
      },
    ).show(context, barrierDismissible: false);
  }
}
