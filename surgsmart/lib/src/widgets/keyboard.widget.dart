import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';

enum KeyType {
  number,
  letter,
  symbol,
  backspace,
  clear,
  shift,
  space,
  enter,
  symbols,
  letters,
  numbers,
}

enum KeyboardType { symbols, letters, numbers }

class KeyboardChar {
  String label;
  final String? value;
  final KeyType type;
  String? assetName;

  KeyboardChar({
    required this.label,
    this.value,
    required this.type,
    this.assetName,
  });
}

class KeyboardInput extends StatefulWidget {
  final List<List<KeyboardChar>>? keys;
  final void Function(KeyboardChar key) onKeyTap;
  final void Function(KeyboardChar key)? onLongKeyTap;

  final void Function(String char)? onChar;
  final void Function()? onEnter;
  final void Function()? onBackspace;

  const KeyboardInput({
    super.key,
    this.keys,
    this.onChar,
    this.onBackspace,
    this.onEnter,
    this.onLongKeyTap,
    required this.onKeyTap,
  });

  @override
  State<KeyboardInput> createState() => _KeyboardInputState();
}

final List<List<KeyboardChar>> defaultLetterKeys = [
  [
    'q',
    'w',
    'e',
    'r',
    't',
    'y',
    'u',
    'i',
    'o',
    'p',
  ].map((char) => KeyboardChar(label: char, type: KeyType.letter)).toList(),
  [
    'a',
    's',
    'd',
    'f',
    'g',
    'h',
    'j',
    'k',
    'l',
  ].map((char) => KeyboardChar(label: char, type: KeyType.letter)).toList(),
  [
    KeyboardChar(
      label: KeyType.shift.name,
      type: KeyType.shift,
      assetName: R.image.icon_shift().assetName,
    ),
    ...[
      'z',
      'x',
      'c',
      'v',
      'b',
      'n',
      'm',
    ].map((char) => KeyboardChar(label: char, type: KeyType.letter)),
    KeyboardChar(
      label: KeyType.backspace.name,
      type: KeyType.backspace,
      assetName: R.image.icon_backspace().assetName,
    ),
  ],
  [
    KeyboardChar(label: '123', type: KeyType.numbers),
    KeyboardChar(label: '空格', type: KeyType.space),
    KeyboardChar(label: '确认', type: KeyType.enter),
  ],
];

final List<List<KeyboardChar>> defaultNumberKeys = [
  [
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    '0',
  ].map((char) => KeyboardChar(label: char, type: KeyType.number)).toList(),
  [
    '-',
    '/',
    ':',
    ';',
    '(',
    ')',
    '\$',
    '&',
    '@',
    '"',
  ].map((char) => KeyboardChar(label: char, type: KeyType.symbol)).toList(),
  [
    KeyboardChar(label: '#+=', type: KeyType.symbols),
    ...[
      '.',
      ',',
      '?',
      '!',
      "'",
    ].map((char) => KeyboardChar(label: char, type: KeyType.symbol)),
    KeyboardChar(
      label: KeyType.backspace.name,
      type: KeyType.backspace,
      assetName: R.image.icon_backspace().assetName,
    ),
  ],
  [
    KeyboardChar(label: 'ABC', type: KeyType.letters),
    KeyboardChar(label: '空格', type: KeyType.space),
    KeyboardChar(label: '确认', type: KeyType.enter),
  ],
];

final List<List<KeyboardChar>> defaultSymbolKeys = [
  [
    '[',
    ']',
    '{',
    '}',
    '#',
    '%',
    '^',
    '*',
    '+',
    '=',
  ].map((char) => KeyboardChar(label: char, type: KeyType.symbol)).toList(),
  [
    '_',
    '\\',
    '|',
    '~',
    '<',
    '>',
    '€',
    '£',
    '¥',
    '•',
  ].map((char) => KeyboardChar(label: char, type: KeyType.symbol)).toList(),
  [
    KeyboardChar(label: '123', type: KeyType.numbers),
    ...[
      '.',
      ',',
      '?',
      '!',
      "'",
    ].map((char) => KeyboardChar(label: char, type: KeyType.symbol)),
    KeyboardChar(
      label: KeyType.backspace.name,
      type: KeyType.backspace,
      assetName: R.image.icon_backspace().assetName,
    ),
  ],
  [
    KeyboardChar(label: 'ABC', type: KeyType.letters),
    KeyboardChar(label: '空格', type: KeyType.space),
    KeyboardChar(label: '确认', type: KeyType.enter),
  ],
];

class _KeyboardInputState extends State<KeyboardInput> {
  List<List<KeyboardChar>>? keys;
  late KeyboardType keyboardType;
  bool shiftSelected = false;

  _KeyboardInputState();

  @override
  void initState() {
    keys = widget.keys ?? defaultLetterKeys;
    keyboardType = KeyboardType.letters;
    super.initState();
  }

  @override
  void didUpdateWidget(covariant KeyboardInput oldWidget) {
    if (widget.keys != oldWidget.keys) {
      setState(() {
        keys = widget.keys;
      });
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children:
          keys!.map((row) {
            return Row(
              children:
                  row.map((key) {
                    return getKeyWidget(
                      key,
                      margin: EdgeInsets.only(
                        right: row.last == key ? 0 : 15.w,
                        bottom: 15.h,
                      ),
                    );
                  }).toList(),
            );
          }).toList(),
    );
  }

  Widget getKeyWidget(KeyboardChar key, {EdgeInsets? margin}) {
    margin ??= EdgeInsets.only(right: 15.w, bottom: 15.h);
    if (key.type == KeyType.enter) {
      return getKeyButton(
        width: 375.w,
        height: 130.h,
        bgColor: Color(0xFF01946F),
        key: key,
        margin: margin,
      );
    } else if (key.type == KeyType.letters ||
        key.type == KeyType.numbers ||
        key.type == KeyType.symbols) {
      return getKeyButton(
        width: 375.w,
        height: 130.h,
        bgColor: Color(0xFF252525),
        key: key,
        margin: margin,
      );
    } else if (key.type == KeyType.backspace) {
      return getKeyButton(
        width: keyboardType == KeyboardType.letters ? 220.w : 375.w,
        height: 130.h,
        bgColor: Color(0xFF252525),
        key: key,
        margin: margin,
      );
    } else if (key.type == KeyType.shift) {
      return getKeyButton(
        width: 220.w,
        height: 130.h,
        bgColor: Color(0xFF252525),
        key: key,
        margin: margin,
      );
    } else {
      return Flexible(
        child: getKeyButton(
          width: double.infinity,
          height: 130.h,
          bgColor: Color(0xFF6B6B6B),
          key: key,
          margin: margin,
        ),
      );
    }
  }

  Widget getKeyButton({
    required double width,
    required double height,
    required Color bgColor,
    required KeyboardChar key,
    required EdgeInsets margin,
  }) {
    return Button(
      onPressed: () {
        widget.onKeyTap(key);
        keyHandler(key);
      },
      onLongPressed: () {
        widget.onLongKeyTap?.call(key);
      },
      label: Container(
        width: width,
        height: height,
        margin: margin,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          color: bgColor,
        ),
        child:
            key.assetName == null
                ? Text(
                  key.label,
                  style: TextStyle(fontSize: 46.sp, color: Colors.white),
                )
                : Image.asset(
                  key.assetName!,
                  fit: BoxFit.contain,
                  height: 51.h,
                ),
      ),
    );
  }

  void keyHandler(KeyboardChar key) {
    if (key.type == KeyType.enter) {
      widget.onEnter?.call();
    } else if (key.type == KeyType.backspace) {
      widget.onBackspace?.call();
    } else if (key.type == KeyType.shift) {
      setState(() {
        shiftSelected = !shiftSelected;
        for (var row in defaultLetterKeys) {
          for (var key in row) {
            if (key.type == KeyType.letter) {
              key.label =
                  shiftSelected
                      ? key.label.toUpperCase()
                      : key.label.toLowerCase();
            } else if (key.type == KeyType.shift) {
              key.assetName =
                  shiftSelected
                      ? R.image.icon_shift_selected().assetName
                      : R.image.icon_shift().assetName;
            }
          }
        }
      });
    } else if (key.type == KeyType.space) {
      widget.onChar?.call(' ');
    } else if (key.type == KeyType.letters) {
      keyboardType = KeyboardType.letters;
      setState(() {
        keys = defaultLetterKeys;
      });
    } else if (key.type == KeyType.numbers) {
      keyboardType = KeyboardType.numbers;
      setState(() {
        keys = defaultNumberKeys;
      });
    } else if (key.type == KeyType.symbols) {
      keyboardType = KeyboardType.symbols;
      setState(() {
        keys = defaultSymbolKeys;
      });
    } else {
      widget.onChar?.call(key.label);
    }
  }
}

class NumericKeypad extends StatefulWidget {
  const NumericKeypad({
    super.key,
    required this.onChar,
    required this.onBackspace,
    required this.onClear,
    required this.onConfirm,
    this.isDecimal = false,
  });

  final void Function(String text) onChar;
  final void Function() onBackspace;
  final void Function() onClear;
  final void Function() onConfirm;
  final bool isDecimal;

  @override
  State<NumericKeypad> createState() => __KeypadState();
}

final List<KeyboardChar> numberList = [
  ...[
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
  ].map((char) => KeyboardChar(label: char, type: KeyType.number)),
  KeyboardChar(label: S.current.f_9KZD4Peb, type: KeyType.clear),
  KeyboardChar(label: '0', type: KeyType.number),
  KeyboardChar(
    label: KeyType.backspace.name,
    type: KeyType.backspace,
    assetName: R.image.icon_backspace().assetName,
  ),
];

final List<KeyboardChar> decimalList = [
  ...[
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
  ].map((char) => KeyboardChar(label: char, type: KeyType.number)),
  KeyboardChar(label: '.', type: KeyType.symbol),
  KeyboardChar(label: '0', type: KeyType.number),
  KeyboardChar(
    label: KeyType.backspace.name,
    type: KeyType.backspace,
    assetName: R.image.icon_backspace().assetName,
  ),
];

class __KeypadState extends State<NumericKeypad> {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: 984.w,
        height: 496.h,
        child: Row(
          children: [
            Expanded(
              child: GridView.count(
                crossAxisCount: 3,
                crossAxisSpacing: 13.r,
                mainAxisSpacing: 17.r,
                childAspectRatio: 237 / 114,
                physics: const NeverScrollableScrollPhysics(),
                children:
                    (widget.isDecimal ? decimalList : numberList).map((key) {
                      return GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          if (key.type == KeyType.clear) {
                            widget.onClear();
                          } else if (key.type == KeyType.backspace) {
                            widget.onBackspace();
                          } else if (key.type == KeyType.number ||
                              key.type == KeyType.symbol) {
                            widget.onChar(key.label);
                          }
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            color: const Color(0xff434343),
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          child: Center(
                            child:
                                key.type == KeyType.backspace
                                    ? Icon(
                                      Icons.backspace_outlined,
                                      size: 60.sp,
                                      color: Colors.white,
                                    )
                                    : Text(
                                      key.label,
                                      style: TextStyle(fontSize: 60.sp),
                                    ),
                          ),
                        ),
                      );
                    }).toList(),
              ),
            ),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                widget.onConfirm();
              },
              child: Container(
                alignment: Alignment.center,
                height: 496.h,
                width: 232.w,
                margin: EdgeInsets.only(left: 16.w),
                decoration: BoxDecoration(
                  color: const Color(0xff01946F),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Text(
                  S.current.f_9KZD5TVS,
                  style: TextStyle(fontSize: 60.sp),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
