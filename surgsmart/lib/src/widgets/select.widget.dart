import 'dart:async';
import 'dart:math';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../models/event_bus.model.dart';
import '../tools/event_bus.dart';

class Select extends StatefulWidget {
  const Select({
    super.key,
    this.disabled = false,
    this.items,
    this.placeholder,
    this.width,
    this.height,
    this.onSelect,
    this.interceptOpenSelect,
    this.onEdit,
    this.checkRule,
    this.enableCheckAtInit = false,
    this.initText,
    this.borderSide = const BorderSide(color: Color(0xFF4FB3FF)),
    this.backgroundColor = Colors.white,
    this.maxLength,
    this.onClear,
    this.selectColor,
    this.maxShowCount = 6,
    this.id,
  });

  final String? initText;
  final String? id;
  final bool disabled;
  final List<SelectItem>? items;
  final String? placeholder;
  final double? width;
  final double? height;
  final void Function(int index)? onSelect;
  final void Function()? interceptOpenSelect;
  final void Function(String text)? onEdit;
  final String? Function(String text)? checkRule;
  final bool enableCheckAtInit;
  final Color backgroundColor;
  final Color? selectColor;
  final BorderSide borderSide;
  final int? maxLength;
  final int maxShowCount;

  final void Function()? onClear;

  @override
  State<Select> createState() => _SelectState();
}

class _SelectState<T> extends State<Select> {
  OverlayEntry? entry;
  final link = LayerLink();
  final focusNode = FocusNode();
  late final textController = TextEditingController(text: widget.initText);
  late final editable = widget.onEdit != null;
  String? error;
  final GlobalKey _key = GlobalKey();
  StreamSubscription<EventBusInfo>? dropdownObserver;

  @override
  void initState() {
    textController.addListener(() {
      widget.onEdit?.call(textController.text);
    });
    focusNode.addListener(() {
      if (!focusNode.hasFocus) {
        final newError = widget.checkRule?.call(textController.text);
        if (newError != error) {
          setState(() => error = newError);
        }
      }
    });

    dropdownObserver = eventBus.on<EventBusInfo>().listen((event) {
      if (event.type == EventBusType.closeSelect) {
        closeDropdown();
      } else if (event.type == EventBusType.openSelect &&
          event.data == widget.id) {
        if (mounted) {
          openDropdown(context, maxShowCount: widget.maxShowCount);
        }
      }
    });
    if (widget.enableCheckAtInit) {
      error = widget.checkRule?.call(textController.text);
    }
    super.initState();
  }

  @override
  void dispose() {
    closeDropdown();
    focusNode.dispose();
    textController.dispose();
    dropdownObserver?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (entry != null) {
      closeDropdown();
    }
    SelectItem? item = widget.items?.where((item) => item.selected).firstOrNull;
    if (item != null) {
      textController.text = item.text;
    } else if (widget.items?.isNotEmpty != true) {
      textController.text = "";
    }
    return CompositedTransformTarget(
      link: link,
      child: Container(
        key: _key,
        decoration: BoxDecoration(
          color: widget.backgroundColor,
          borderRadius: BorderRadius.circular(4.w),
          border: Border.all(
            width: widget.borderSide.width,
            color: widget.borderSide.color,
          ),
        ),
        width: widget.width ?? double.infinity,
        height: widget.height,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
              child: TextField(
                onTap: () {
                  if (!editable) {
                    if (widget.interceptOpenSelect != null) {
                      widget.interceptOpenSelect?.call();
                      return;
                    }
                    openDropdown(context, maxShowCount: widget.maxShowCount);
                  }
                },
                maxLength: widget.maxLength,
                controller: textController,
                focusNode: focusNode,
                enabled: !widget.disabled,
                readOnly: !editable,
                cursorHeight: 32.h,
                cursorColor: const Color(0xFF4FB3FF),
                onTapOutside: (event) => focusNode.unfocus(),
                style: TextStyle(color: Colors.white, fontSize: 48.sp),
                decoration: InputDecoration(
                  counterText: '',
                  labelText: error,
                  labelStyle: TextStyle(color: Colors.red, fontSize: 24.sp),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 10.w,
                    vertical: 10.h,
                  ),
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  errorBorder: InputBorder.none,
                  disabledBorder: InputBorder.none,
                  hintText: widget.placeholder,
                ),
              ),
            ),
            textController.text.isNotEmpty && widget.onClear != null
                ? GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    setState(() {
                      textController.text = '';
                      widget.onClear?.call();
                    });
                  },
                  child: Icon(Icons.close, size: 48.sp, color: Colors.white),
                )
                : widget.disabled
                ? const SizedBox.shrink()
                : GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    if (widget.interceptOpenSelect != null) {
                      widget.interceptOpenSelect?.call();
                      return;
                    }
                    openDropdown(context, maxShowCount: widget.maxShowCount);
                  },
                  child: Icon(
                    Icons.expand_more_outlined,
                    color: Colors.white,
                    size: 48.sp,
                  ),
                ),
          ],
        ),
      ),
    );
  }

  void openDropdown(BuildContext context, {int maxShowCount = 6}) {
    if (!widget.disabled) {
      if (entry == null) {
        entry = OverlayEntry(
          builder:
              (context) => Stack(
                children: [
                  Positioned.fill(
                    child: GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: closeDropdown,
                      child: const SizedBox(),
                    ),
                  ),
                  Positioned(
                    width:
                        widget.width ??
                        (_key.currentContext?.findRenderObject() as RenderBox?)
                            ?.size
                            .width ??
                        600.w,
                    child: CompositedTransformFollower(
                      followerAnchor: Alignment.topCenter,
                      link: link,
                      offset: const Offset(0, 8),
                      targetAnchor: Alignment.bottomCenter,
                      child: SelectDropdown(
                        items: widget.items,
                        onSelect: handleSelect,
                        maxShowCount: maxShowCount,
                      ),
                    ),
                  ),
                ],
              ),
        );
        Overlay.of(context).insert(entry!);
      } else {
        closeDropdown();
      }
    }
  }

  void closeDropdown() {
    entry?.remove();
    entry = null;
    final newError = widget.checkRule?.call(textController.text);
    if (newError != error) {
      setState(() => error = newError);
    }
  }

  void handleSelect(String text, int index) {
    setState(() {
      textController.text = text;
      widget.onSelect?.call(index);
      closeDropdown();
      error = widget.checkRule?.call(textController.text);
    });
  }
}

class SelectItem extends StatelessWidget {
  const SelectItem({
    super.key,
    this.disabled = false,
    this.selected = false,
    required this.text,
    this.comment,
  });

  final bool disabled;
  final String text;
  final Widget? comment;
  final bool selected;

  @override
  Widget build(BuildContext context) {
    var color = Colors.white.withValues(alpha: 0.85);
    if (disabled) {
      color = Colors.white.withValues(alpha: 0.25);
    }

    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 8.w),
      color: selected ? const Color(0xBB00849B) : null,
      child: Row(
        children: [
          Expanded(
            child: Text(
              text,
              maxLines: 1,
              style: TextStyle(
                color: color,
                fontSize: 48.sp,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          if (comment != null) ...[SizedBox(width: 8.w), comment!],
        ],
      ),
    );
  }
}

class SelectDropdown extends StatefulWidget {
  const SelectDropdown({
    super.key,
    this.items,
    this.onSelect,
    this.maxShowCount,
  });

  final List<SelectItem>? items;
  final int? maxShowCount;
  final void Function(String, int index)? onSelect;

  @override
  State<SelectDropdown> createState() => _SelectDropdownState();
}

class _SelectDropdownState extends State<SelectDropdown> {
  late ScrollController scrollController;
  @override
  Widget build(BuildContext context) {
    var children = <Widget>[];
    if (widget.items?.isNotEmpty == true) {
      children = List.generate(
        widget.items!.length,
        (index) => GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () => handleTap(widget.items![index], index),
          child: widget.items![index],
        ),
      );
    } else {
      children = [const SelectItem(disabled: true, text: '暂无数据')];
    }
    return Material(
      borderRadius: BorderRadius.circular(16.w),
      child: Container(
        height: (min(children.length, widget.maxShowCount ?? 6) * 92.h) + 22.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.w),
          border: Border.all(width: 4.w, color: const Color(0xff313E50)),
          boxShadow: [
            BoxShadow(
              color: const Color(0xff313E50).withValues(alpha: 0.5),
              offset: const Offset(0, 4),
              blurRadius: 8.w,
              spreadRadius: 8.w,
            ),
          ],
          color: const Color(0xff1D2632),
        ),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.w),
        child: MediaQuery.removePadding(
          context: context,
          removeTop: true,
          child: Scrollbar(
            controller: scrollController,
            thumbVisibility: children.length > 5,
            child: ScrollConfiguration(
              behavior: const MaterialScrollBehavior().copyWith(
                physics: const BouncingScrollPhysics(),
                dragDevices: {PointerDeviceKind.touch, PointerDeviceKind.mouse},
              ),
              child: ListView.builder(
                controller: scrollController,
                itemCount: children.length,
                itemBuilder: (BuildContext context, int index) {
                  return children[index];
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  void handleTap(SelectItem item, int index) {
    if (!item.disabled) {
      widget.onSelect?.call(item.text, index);
    }
  }

  @override
  void initState() {
    super.initState();
    scrollController = ScrollController();
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }
}
