import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

enum ModalType { info, success, warning, error }

enum ModalKind { modal, dialog, result }

class Modal extends StatefulWidget {
  const Modal({
    super.key,
    required this.kind,
    required this.type,
    this.closable = true,
    this.title,
    this.child,
    this.message,
    this.footer,
    this.confirmText,
    this.cancelText,
    this.onClose,
    this.onConfirm,
    this.onCancel,
    this.messageFontSize,
    this.header,
  });

  final ModalKind kind;
  final ModalType type;
  final bool closable;
  final String? title; // 标题
  final Widget? child; // 内容
  final String? message; // result 类型 的提示文字
  final Widget? header; // 顶部自定义
  final Widget? footer; // 底部自定义
  final String? confirmText; // 确认文字提示
  final String? cancelText; // 取消文字提示
  final double? messageFontSize;
  final void Function()? onClose; // 关闭
  final void Function()? onConfirm; // 确认
  final void Function()? onCancel; // 取消

  @override
  State<Modal> createState() => _ModalState();

  void show(BuildContext context, {bool barrierDismissible = false}) {
    showDialog(
      barrierDismissible: barrierDismissible,
      context: context,
      builder: (context) => Center(child: this),
    );
  }
}

class _ModalState extends State<Modal> {
  static final double defaultFontSize = 96.sp;

  @override
  Widget build(BuildContext context) {
    Widget header = const SizedBox();
    if (widget.header != null) {
      header = widget.header!;
    } else if (widget.closable || widget.title?.isNotEmpty == true) {
      header = _ModalHeader(
        fontSize: (widget.messageFontSize ?? defaultFontSize) * 0.76,
        type: widget.type,
        closable: widget.closable,
        title: widget.title,
        onClose: widget.onClose,
      );
    }

    Widget currentFooter =
        widget.footer ??
        ModalFooter(
          fontSize: (widget.messageFontSize ?? defaultFontSize) * 0.76,
          modalKind: widget.kind,
          type: widget.type,
          confirmText: widget.confirmText,
          cancelText: widget.cancelText,
          onConfirm: widget.onConfirm,
          onCancel: widget.onCancel,
        );

    return Material(
      borderRadius: BorderRadius.circular(30.r),
      child: Container(
        width: 2702.w,
        height: 940.h,
        decoration: BoxDecoration(
          border: Border.all(
            width: 4.w,
            color: const Color(0xFFFFFFFF).withValues(alpha: 0.48),
          ),
          color: Colors.black,
          borderRadius: BorderRadius.circular(30.r),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            header,
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(top: 64.h, left: 64.w, right: 64.w),
                child:
                    widget.child ??
                    Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 40.w),
                        child: Text(
                          widget.message ?? "",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: widget.messageFontSize ?? defaultFontSize,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
              ),
            ),
            currentFooter,
          ],
        ),
      ),
    );
  }
}

// header
class _ModalHeader extends StatefulWidget {
  const _ModalHeader({
    this.closable = true,
    this.title,
    this.onClose,
    this.type = ModalType.info,
    this.fontSize = 72,
  });
  final double fontSize;
  final bool closable;
  final String? title;
  final ModalType type;

  final void Function()? onClose;

  @override
  State<_ModalHeader> createState() => __ModalHeaderState();
}

LinearGradient getGradientColors(ModalType type) {
  List<Color> colors = [const Color(0xB25CF5EB), const Color(0xB256B3E3)];

  if (type == ModalType.info) {
    colors = [const Color(0xB25CF5EB), const Color(0xB256B3E3)];
  } else if (type == ModalType.success) {
    colors = [const Color(0xB212C471), const Color(0xB2009851)];
  } else if (type == ModalType.warning) {
    //colors = [const Color(0xB2FFC375), const Color(0xB2E68A11)];
    colors = [const Color(0xB25CF5EB), const Color(0xB256B3E3)];
  } else if (type == ModalType.error) {
    colors = [const Color(0xB2FC8C73), const Color(0xB2E20505)];
  }

  return LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: colors,
  );
}

class __ModalHeaderState extends State<_ModalHeader> {
  @override
  Widget build(BuildContext context) {
    Icon icon;
    if (widget.type == ModalType.info) {
      icon = Icon(
        Icons.info_outline,
        color: const Color(0xFF0EC6D2),
        size: 100.sp,
      );
    } else if (widget.type == ModalType.success) {
      icon = Icon(
        Icons.check_circle_outline,
        color: const Color(0xFF23D9BD),
        size: 100.sp,
      );
    } else if (widget.type == ModalType.warning) {
      icon = Icon(
        Icons.warning_amber_rounded,
        color: const Color(0xFFFF4E4E),
        size: 100.sp,
      );
    } else if (widget.type == ModalType.error) {
      icon = Icon(
        Icons.error_outline,
        color: const Color(0xFFFF4E4E),
        size: 100.sp,
      );
    } else {
      icon = Icon(
        Icons.info_outline,
        color: const Color(0xFF0EC6D2),
        size: 100.sp,
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: const Color(0xff313E50),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(25.w),
          topRight: Radius.circular(25.w),
        ),
      ),
      padding: EdgeInsets.symmetric(horizontal: 36.w, vertical: 24.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          icon,
          SizedBox(width: 24.w),
          if (widget.title?.isNotEmpty == true)
            Expanded(
              child: Text(
                widget.title!,
                style: TextStyle(
                  color: const Color(0xFFffffff),
                  fontSize: widget.fontSize,
                  fontWeight: FontWeight.w500,
                  height: 1,
                ),
              ),
            ),
          if (widget.closable)
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: handleCancel,
              child: Container(
                width: 80.w,
                height: 80.h,
                decoration: BoxDecoration(
                  border: Border.all(width: 4.w, color: Colors.white),
                  borderRadius: BorderRadius.all(Radius.circular(40.w)),
                ),
                child: Icon(Icons.close, color: Colors.white, size: 64.w),
              ),
            ),
        ],
      ),
    );
  }

  void handleCancel() {
    widget.onClose?.call();
    if (widget.onClose == null) {
      Navigator.of(context).pop();
    }
  }
}

// modal 类型 footer
class ModalFooter extends StatelessWidget {
  const ModalFooter({
    super.key,
    this.onConfirm,
    this.onCancel,
    this.confirmText,
    this.cancelText,
    this.type = ModalType.info,
    this.modalKind = ModalKind.modal,
    this.fontSize = 70,
  });
  final ModalKind modalKind;
  final double fontSize;
  final ModalType type;
  final String? confirmText;
  final String? cancelText;
  final void Function()? onConfirm;
  final void Function()? onCancel;

  @override
  Widget build(BuildContext context) {
    final radius = BorderRadius.circular(90.r);
    if (confirmText == null && cancelText == null) {
      return const SizedBox.shrink();
    }
    return Container(
      padding: EdgeInsets.only(
        left: 64.w,
        right: 64.w,
        bottom: 120.h,
        top: 64.h,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (modalKind == ModalKind.modal) const Spacer(),
          if (cancelText?.isNotEmpty == true)
            Padding(
              padding: EdgeInsets.only(
                right:
                    confirmText != null
                        ? modalKind == ModalKind.dialog
                            ? 200.w
                            : 64.w
                        : 0,
              ),
              child: Button(
                onPressed: () => handleCancel(context),
                borderRadius: radius,
                label: Container(
                  alignment: Alignment.center,
                  padding: EdgeInsets.symmetric(
                    horizontal: 110.w,
                    vertical: 16.h,
                  ),
                  constraints: BoxConstraints(minWidth: 480.w),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.white, width: 4.w),
                    borderRadius: radius,
                  ),
                  child: Text(
                    cancelText!,
                    style: TextStyle(fontSize: fontSize, color: Colors.white),
                  ),
                ),
              ),
            ),
          if (confirmText?.isNotEmpty == true)
            Button(
              onPressed: () => handleConfirm(context),
              tapDownColor: getGradientColors(
                type,
              ).colors[1].withValues(alpha: 1),
              borderRadius: radius,
              label: Container(
                alignment: Alignment.center,
                padding: EdgeInsets.symmetric(
                  horizontal: 110.w,
                  vertical: 16.h,
                ),
                decoration: BoxDecoration(
                  borderRadius: radius,
                  gradient: getGradientColors(type),
                ),
                constraints: BoxConstraints(minWidth: 480.w),
                child: Text(
                  confirmText!,
                  style: TextStyle(fontSize: fontSize, color: Colors.white),
                ),
              ),
            ),
        ],
      ),
    );
  }

  void handleCancel(context) {
    onCancel?.call();
    if (onCancel == null) {
      Navigator.of(context).pop();
    }
  }

  void handleConfirm(context) {
    onConfirm?.call();
    if (onConfirm == null) {
      Navigator.of(context).pop();
    }
  }
}
