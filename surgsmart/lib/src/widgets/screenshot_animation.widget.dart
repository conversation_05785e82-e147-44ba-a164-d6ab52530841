import 'dart:io';
import 'package:flutter/material.dart';

class ScreenshotAnimateWidget extends StatefulWidget {
  final Color? borderColor;
  final double? borderRadius;
  final String? imagePath;
  const ScreenshotAnimateWidget({
    super.key,
    this.borderColor = Colors.white,
    this.borderRadius = 20,
    this.imagePath,
  });

  @override
  State<StatefulWidget> createState() {
    return ScreenshotAnimateWidgetState();
  }
}

class ScreenshotAnimateWidgetState extends State<ScreenshotAnimateWidget>
    with TickerProviderStateMixin {
  double? childWidth;
  double? childHeight;
  double? width;
  double? height;
  Duration animatedDuration = const Duration(milliseconds: 300);
  final awaitTime = 500;
  final GlobalKey _key = GlobalKey();
  int lastBuildTime = 0;
  late AnimationController _controller;
  late Animation<Offset> _slideAnimation;
  //持久化截图路径用以对比，避免外层widget build后误触发截图动画
  static String? tempImagePath;
  BoxDecoration? boxDecoration;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback(_getSize);

    _controller = AnimationController(
      duration: animatedDuration,
      vsync: this,
    );
    Tween<Offset> tween = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0.0, -1.0),
    );
    _slideAnimation = tween.animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    super.initState();
  }

  void _getSize(duration) {
    RenderBox? renderBox =
        _key.currentContext?.findRenderObject() as RenderBox?;
    childWidth = renderBox?.size.width;
    childHeight = renderBox?.size.height;
    width = childWidth;
    height = childHeight;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    startAnimation();
    return SlideTransition(
      position: _slideAnimation,
      child: Center(
        child: AnimatedContainer(
          key: _key,
          duration: animatedDuration,
          width: width,
          height: height,
          decoration: boxDecoration,
          child: const SizedBox(
            width: double.infinity,
            height: double.infinity,
          ),
        ),
      ),
    );
  }

  void startAnimation() {
    if (_controller.isAnimating ||
        DateTime.now().millisecondsSinceEpoch - lastBuildTime <
            animatedDuration.inMilliseconds * 2 + awaitTime) {
      return;
    }
    boxDecoration = null;
    String uriPath = "";
    if (widget.imagePath == null || widget.imagePath!.isEmpty) {
      return;
    }
    if (tempImagePath == widget.imagePath) {
      return;
    } else {
      tempImagePath = widget.imagePath;
      Uri? uri = Uri.tryParse(widget.imagePath!);
      if (uri != null) {
        uriPath = uri.path;
      }
    }

    boxDecoration = BoxDecoration(
        image: DecorationImage(image: FileImage(File(uriPath))),
        borderRadius: BorderRadius.circular(widget.borderRadius!),
        border: Border.all(color: Colors.white, width: 10));
    lastBuildTime = DateTime.now().millisecondsSinceEpoch;

    Future.delayed(Duration.zero).then((value) {
      setState(() {
        if (childWidth != null) {
          width = childWidth! * 0.8;
        }
        if (childHeight != null) {
          height = childHeight! * 0.8;
        }
      });
      //缩放0.5s后开始平移动画
      Future.delayed(Duration(
              milliseconds: awaitTime + animatedDuration.inMilliseconds))
          .then((value) {
        if (mounted) {
          _controller.forward().then((value) {
            setState(() {
              boxDecoration = null;
              width = childWidth;
              height = childHeight;
            });
            _controller.reset();
          });
        }
      });
    });
  }
}
