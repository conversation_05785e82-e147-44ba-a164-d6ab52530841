import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/generated/l10n.dart';

// ignore: must_be_immutable
class DateSelect extends StatefulWidget {
  DateSelect({
    super.key,
    this.width,
    this.height,
    this.backgroundColor,
    this.textColor,
    this.text,
    this.placeholder,
    required this.onConfirm,
    this.onClear,
  });

  double? width;
  double? height;
  Color? backgroundColor;
  Color? textColor;
  String? text;
  String? placeholder;

  final void Function(DateTime?) onConfirm;
  final void Function()? onClear;

  @override
  State<DateSelect> createState() => _DateSelectState();
}

class _DateSelectState extends State<DateSelect> {
  OverlayEntry? entry;
  final link = LayerLink();
  final focusNode = FocusNode();
  late String? text;

  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    text = widget.text;
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: link,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => {openDropdown(context)},
        child: Container(
          width: widget.width ?? 480.w,
          height: widget.height ?? 102.h,
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          decoration: BoxDecoration(
            color: widget.backgroundColor ?? const Color(0xff1D2632),
            border: Border.all(width: 4.w, color: const Color(0xff313E50)),
            borderRadius: BorderRadius.all(Radius.circular(8.w)),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  text ?? (widget.placeholder ?? S.current.f_9KZDITYd),
                  style: TextStyle(
                    fontSize: 48.sp,
                    color:
                        widget.textColor ??
                        (text == null
                            ? Colors.white.withValues(alpha: 0.6)
                            : Colors.white),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              text == null
                  ? Icon(
                    Icons.keyboard_arrow_down_outlined,
                    size: 48.sp,
                    color: widget.textColor ?? Colors.white,
                  )
                  : GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      setState(() {
                        text = null;
                        _selectedDate = DateTime.now();
                        widget.onClear!();
                      });
                    },
                    child: Icon(
                      Icons.close,
                      size: 48.sp,
                      color: widget.textColor ?? Colors.white,
                    ),
                  ),
            ],
          ),
        ),
      ),
    );
  }

  void openDropdown(BuildContext context) {
    if (entry == null) {
      entry = OverlayEntry(
        builder:
            (context) => Stack(
              children: [
                Positioned.fill(
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: closeDropdown,
                    child: const SizedBox(),
                  ),
                ),
                Positioned(
                  width: 772.w,
                  child: CompositedTransformFollower(
                    followerAnchor: Alignment.topLeft,
                    link: link,
                    offset: const Offset(0, 8),
                    targetAnchor: Alignment.bottomLeft,
                    child: SelectDropdown(
                      year: _selectedDate.year,
                      month: _selectedDate.month,
                      day: _selectedDate.day,
                      onSelect: handleSelect,
                    ),
                  ),
                ),
              ],
            ),
      );
      Overlay.of(context).insert(entry!);
    } else {
      closeDropdown();
    }
  }

  void closeDropdown() {
    entry?.remove();
    entry = null;
  }

  void handleSelect({year, month, day}) {
    _selectedDate = DateTime(year, month, day);
    setState(() {
      text =
          "${_selectedDate.year}/${_selectedDate.month}/${_selectedDate.day}";
      closeDropdown();
      widget.onConfirm(_selectedDate);
    });
  }
}

class SelectDropdown extends StatefulWidget {
  const SelectDropdown({
    super.key,
    required this.onSelect,
    this.year,
    this.month,
    this.day,
  });

  final int? year;
  final int? month;
  final int? day;
  final void Function({int year, int month, int day}) onSelect;

  @override
  State<SelectDropdown> createState() => _SelectDropdownState();
}

class _SelectDropdownState extends State<SelectDropdown> {
  late ScrollController yearScrollController;
  late ScrollController monthScrollController;
  late ScrollController dayScrollController;

  DateTime selectedDate = DateTime.now();

  late int selectedYear;
  late int selectedMonth;
  late int selectedDay;

  List<int> years = List.generate(
    DateTime.now().year - 2017,
    (index) => 2018 + index,
  );
  List<int> months = List.generate(12, (index) => index + 1);
  List<int> days = [];

  void updateDaysInMonth(int year, int month) {
    int daysInMonth = DateTime(year, month + 1, 0).day;
    setState(() {
      days = List.generate(daysInMonth, (index) => index + 1);
      if (!days.contains(selectedDay)) {
        selectedDay = DateTime.now().day;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      borderRadius: BorderRadius.circular(16.w),
      child: Container(
        height: 724.h,
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.w),
          border: Border.all(width: 4.w, color: const Color(0xff313E50)),
          boxShadow: [
            BoxShadow(
              color: const Color(0xff313E50).withValues(alpha: 0.5),
              offset: const Offset(0, 4),
              blurRadius: 8.w,
              spreadRadius: 8.w,
            ),
          ],
          color: const Color(0xff1D2632),
        ),
        child: Column(
          children: [
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(24.h),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    width: 3.w,
                    color: const Color(0xff313E50),
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '$selectedYear/$selectedMonth/$selectedDay',
                    style: TextStyle(fontSize: 48.sp),
                  ),
                  TextButton(
                    onPressed: () {
                      widget.onSelect(
                        year: selectedYear,
                        month: selectedMonth,
                        day: selectedDay,
                      );
                    },
                    child: Text(
                      S.current.f_9KZD5TVS,
                      style: TextStyle(
                        fontSize: 48.sp,
                        color: const Color(0xff23D9BD),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 232.w,
                    height: double.infinity,
                    margin: EdgeInsets.only(right: 16.w),
                    decoration: BoxDecoration(
                      border: Border(
                        right: BorderSide(
                          width: 3.w,
                          color: const Color(0xff313E50),
                        ),
                      ),
                    ),
                    child: Scrollbar(
                      controller: yearScrollController,
                      thumbVisibility: years.length > 5,
                      child: ScrollConfiguration(
                        behavior: const MaterialScrollBehavior().copyWith(
                          physics: const BouncingScrollPhysics(),
                          dragDevices: {
                            PointerDeviceKind.touch,
                            PointerDeviceKind.mouse,
                          },
                        ),
                        child: ListView.builder(
                          controller: yearScrollController,
                          itemCount: years.length,
                          itemBuilder: (BuildContext context, int index) {
                            return GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap:
                                  () => {
                                    setState(() {
                                      selectedYear = years[index];
                                      updateDaysInMonth(
                                        selectedYear,
                                        selectedMonth,
                                      );
                                      if (index >= 2 &&
                                          index < years.length - 2) {
                                        yearScrollController.animateTo(
                                          100.h * index - 200.h,
                                          duration: const Duration(
                                            milliseconds: 300,
                                          ),
                                          curve: Curves.ease,
                                        );
                                      }
                                    }),
                                  },
                              child: Container(
                                padding: EdgeInsets.symmetric(vertical: 18.h),
                                decoration: BoxDecoration(
                                  color:
                                      years[index] == selectedYear
                                          ? const Color(0xff00849B)
                                          : Colors.transparent,
                                  borderRadius: BorderRadius.all(
                                    Radius.circular(4.w),
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    years[index].toString(),
                                    style: TextStyle(fontSize: 48.sp),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                  Container(
                    width: 232.w,
                    height: double.infinity,
                    margin: EdgeInsets.only(right: 16.w),
                    decoration: BoxDecoration(
                      border: Border(
                        right: BorderSide(
                          width: 3.w,
                          color: const Color(0xff313E50),
                        ),
                      ),
                    ),
                    child: Scrollbar(
                      controller: monthScrollController,
                      thumbVisibility: years.length > 5,
                      child: ScrollConfiguration(
                        behavior: const MaterialScrollBehavior().copyWith(
                          physics: const BouncingScrollPhysics(),
                          dragDevices: {
                            PointerDeviceKind.touch,
                            PointerDeviceKind.mouse,
                          },
                        ),
                        child: ListView.builder(
                          controller: monthScrollController,
                          itemCount: months.length,
                          itemBuilder: (BuildContext context, int index) {
                            return GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap:
                                  () => {
                                    setState(() {
                                      selectedMonth = months[index];
                                      updateDaysInMonth(
                                        selectedYear,
                                        selectedMonth,
                                      );
                                      if (index >= 2 &&
                                          index < months.length - 2) {
                                        monthScrollController.animateTo(
                                          100.h * index - 200.h,
                                          duration: const Duration(
                                            milliseconds: 300,
                                          ),
                                          curve: Curves.ease,
                                        );
                                      }
                                    }),
                                  },
                              child: Container(
                                padding: EdgeInsets.symmetric(vertical: 18.h),
                                decoration: BoxDecoration(
                                  color:
                                      months[index] == selectedMonth
                                          ? const Color(0xff00849B)
                                          : Colors.transparent,
                                  borderRadius: BorderRadius.all(
                                    Radius.circular(4.w),
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    months[index].toString(),
                                    style: TextStyle(fontSize: 48.sp),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 232.w,
                    height: double.infinity,
                    child: Scrollbar(
                      controller: dayScrollController,
                      thumbVisibility: years.length > 5,
                      child: ScrollConfiguration(
                        behavior: const MaterialScrollBehavior().copyWith(
                          physics: const BouncingScrollPhysics(),
                          dragDevices: {
                            PointerDeviceKind.touch,
                            PointerDeviceKind.mouse,
                          },
                        ),
                        child: ListView.builder(
                          controller: dayScrollController,
                          itemCount: days.length,
                          itemBuilder: (BuildContext context, int index) {
                            return GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap:
                                  () => {
                                    setState(() {
                                      selectedDay = days[index];
                                      if (index >= 2 &&
                                          index < days.length - 2) {
                                        dayScrollController.animateTo(
                                          100.h * index - 200.h,
                                          duration: const Duration(
                                            milliseconds: 300,
                                          ),
                                          curve: Curves.ease,
                                        );
                                      }
                                    }),
                                  },
                              child: Container(
                                padding: EdgeInsets.symmetric(vertical: 18.h),
                                decoration: BoxDecoration(
                                  color:
                                      days[index] == selectedDay
                                          ? const Color(0xff00849B)
                                          : Colors.transparent,
                                  borderRadius: BorderRadius.all(
                                    Radius.circular(4.w),
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    days[index].toString(),
                                    style: TextStyle(fontSize: 48.sp),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _updateDaysInMonth(int year, int month) {
    int daysInMonth = DateTime(year, month + 1, 0).day;
    days = List.generate(daysInMonth, (index) => index + 1);
  }

  @override
  void initState() {
    super.initState();
    selectedYear = widget.year ?? DateTime.now().year;
    selectedMonth = widget.month ?? DateTime.now().month;
    selectedDay = widget.day ?? DateTime.now().day;
    yearScrollController = ScrollController();
    monthScrollController = ScrollController();
    dayScrollController = ScrollController();
    _updateDaysInMonth(selectedDate.year, selectedDate.month);

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      int yearIndex = years.indexOf(selectedYear);
      int monthIndex = months.indexOf(selectedMonth);
      int dayIndex = days.indexOf(selectedDay);
      if (yearIndex >= 4) {
        yearScrollController.jumpTo(
          (years.indexOf(selectedYear) * 100.h - 200.h).toDouble(),
        );
      }
      if (monthIndex >= 4) {
        monthScrollController.jumpTo(
          (months.indexOf(selectedMonth) * 100.h - 200.h).toDouble(),
        );
      }
      if (dayIndex >= 4) {
        dayScrollController.jumpTo(
          (days.indexOf(selectedDay) * 100.h - 200.h).toDouble(),
        );
      }
    });
  }

  @override
  void dispose() {
    yearScrollController.dispose();
    monthScrollController.dispose();
    dayScrollController.dispose();
    super.dispose();
  }
}
