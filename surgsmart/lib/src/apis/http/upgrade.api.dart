import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/models/http/version.mode.dart';

class HttpUpgradeApi<T extends AppModel> extends HttpApi<T> {
  /// 检查版本
  HttpUpgradeApi.checkVersion()
      : super(
          apiType: HttpApiType.upgrader,
          method: HttpApiMethod.get,
          path: "/api/upgrade/check/",
          query: {},
          body: {},
          model: VersionInfo() as T,
        );

  /// 获取当前版本
  HttpUpgradeApi.currentVersions()
      : super(
    apiType: HttpApiType.upgrader,
    method: HttpApiMethod.get,
    path: "/api/upgrade/current-versions/",
    query: {},
    body: {},
    model: VersionData() as T,
  );

/*
  /// 开始更新
  HttpUpgradeApi.start()
      : super(
          apiType: HttpApiType.upgrader,
          method: HttpApiMethod.post,
          path: "/api/upgrade/",
          query: {},
          body: {},
          model: ApiModel() as T,
        );

  /// 更新状态
  HttpUpgradeApi.status()
      : super(
          apiType: HttpApiType.upgrader,
          method: HttpApiMethod.get,
          path: "/api/upgrade/status/",
          query: {},
          body: {},
          model: UpgradeStatusInfo() as T,
        );
*/

  /// 下载安装包
  HttpUpgradeApi.startDownload()
      : super(
          apiType: HttpApiType.upgrader,
          method: HttpApiMethod.post,
          path: "/api/upgrade/download/start/",
          query: {},
          body: {},
          model: ApiModel() as T,
        );

  /// 停止下载安装包
  HttpUpgradeApi.stopDownload()
      : super(
          apiType: HttpApiType.upgrader,
          method: HttpApiMethod.post,
          path: "/api/upgrade/download/stop/",
          query: {},
          body: {},
          model: ApiModel() as T,
        );

  /// 安装更新包
  HttpUpgradeApi.installUpdate()
      : super(
          apiType: HttpApiType.upgrader,
          method: HttpApiMethod.post,
          path: "/api/upgrade/install/",
          query: {},
          body: {},
          model: ApiModel() as T,
        );
}
