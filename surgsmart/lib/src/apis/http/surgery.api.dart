import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/models/http/surgery.model.dart';

class HttpSurgeryApi<T extends AppModel> extends HttpApi<T> {
  /// 创建手术
  ///
  /// - [userId] 用户ID
  ///
  /// - [procedureName] 手术名称
  ///
  /// - [admissionNumber] 住院号
  ///
  /// - [sex] 性别
  ///
  /// - [age] 年龄
  HttpSurgeryApi.create({
    required int userId,
    required String procedureName,
    String? admissionNumber,
    String? department,
    int? sex,
    int? age,
    List<int>? participantIds,
  }) : super(
         apiType: HttpApiType.surgsmart,
         method: HttpApiMethod.post,
         path: "/surgery/",
         query: {},
         body: {
           "user_id": userId,
           "procedure_name": procedureName,
           'surgery_time': DateTime.now().millisecondsSinceEpoch,
           "admission_number": admissionNumber,
           "department": department,
           "patient_sex": sex,
           "patient_age": age,
           "participant_ids": participantIds,
         },
         model: SurgeryInfo() as T,
       );

  /// 开始手术
  ///
  /// - [surgeryId] 手术ID
  HttpSurgeryApi.start({required int surgeryId})
    : super(
        apiType: HttpApiType.surgsmart,
        method: HttpApiMethod.post,
        path: "/surgery/$surgeryId/start/",
        query: {},
        body: {},
        model: ApiModel() as T,
      );

  /// 手术信息
  ///
  /// - [surgeryId] 手术ID
  HttpSurgeryApi.info({required int surgeryId})
    : super(
        apiType: HttpApiType.surgsmart,
        method: HttpApiMethod.get,
        path: "/surgery/$surgeryId/",
        query: {},
        body: {},
        model: SurgeryInfo() as T,
      );

  /// 停止手术
  ///
  /// - [surgeryId] 手术ID
  HttpSurgeryApi.stop({required int surgeryId})
    : super(
        apiType: HttpApiType.surgsmart,
        method: HttpApiMethod.post,
        path: "/surgery/$surgeryId/stop/",
        query: {},
        body: {},
        model: ApiModel() as T,
      );

  /// 手术ping
  ///
  /// - [surgeryId] 手术ID
  HttpSurgeryApi.ping({required int surgeryId})
    : super(
        apiType: HttpApiType.surgsmart,
        method: HttpApiMethod.post,
        path: "/surgery/$surgeryId/ping/",
        query: {},
        body: {},
        model: ApiModel() as T,
      );

  /// 正在继续的手术
  HttpSurgeryApi.ongoing()
    : super(
        apiType: HttpApiType.surgsmart,
        method: HttpApiMethod.get,
        path: "/surgery/ongoing/",
        query: {},
        body: {},
        model: SurgeryInfo() as T,
      );

  /// 睿标记
  ///
  /// - [surgeryId] 手术ID
  ///
  /// - [timePoint] 相对时间点
  ///
  /// - [imagePath] 图片路径
  HttpSurgeryApi.markTime({
    required int surgeryId,
    required int timePoint,
    required String imagePath,
  }) : super(
         apiType: HttpApiType.surgsmart,
         method: HttpApiMethod.post,
         path: "/surgery/$surgeryId/review/quick/",
         query: {},
         body: {'time_point': timePoint, 'image_path': imagePath},
         model: SurgeryMarkTimeInfo() as T,
       );

  /// 设置体腔外模糊
  ///
  /// - [surgeryId] 手术ID
  ///
  /// - [status] 模糊状态
  HttpSurgeryApi.setOutBlur({required int surgeryId, required bool status})
    : super(
        apiType: HttpApiType.surgsmart,
        method: HttpApiMethod.put,
        path: "/surgery/$surgeryId/is-bodyout-masked/",
        body: {'is_bodyout_masked': status},
        query: {},
        model: SurgeryBodyOutMaskedInfo() as T,
      );

  /// 切割闭合手术状态上报
  ///
  /// - [surgeryId] 手术ID
  HttpSurgeryApi.staplingReport({required int surgeryId})
    : super(
        apiType: HttpApiType.surgsmart,
        method: HttpApiMethod.post,
        path: "/insight/surgery-analysis/$surgeryId/alarm/",
        query: {},
        body: {'alarm_title': '切割闭合次数识别异常', 'alarm_content': '切割闭合事件识别次数超过阈值'},
        model: ApiModel() as T,
      );
}
