import 'package:app_foundation/app_foundation.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/models/http/auth.model.dart';

class HttpAuthApi<T extends AppModel> extends HttpApi<T> {
  /// 设备授权
  HttpAuthApi.authorize({
    required String machineCode,
    required String licenseCode,
  }) : super(
          apiType: HttpApiType.surgsmart,
          method: HttpApiMethod.post,
          path: "/device/authorize/",
          query: {},
          body: {"machine_code": machineCode, "license": licenseCode},
          model: AuthorizeInfo() as T,
        );
}
