import 'package:app_foundation/app_foundation.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/models/mqtt/interactive.model.dart';

class MqttWhiteboardApi<T extends AppModel> extends MqttApi<T> {
  final int surgeryId;

  @override
  String get topic => "/surgsmart/surgeries/$surgeryId/whiteboard";

  /// 白板光标
  MqttWhiteboardApi.onCursorMessage({
    required this.surgeryId,
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "whiteboard:cursor",
          body: {},
          model: Graphic() as T,
        );

  /// 白板清空
  MqttWhiteboardApi.onEmptyMessage({
    required this.surgeryId,
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "whiteboard:empty",
          body: {},
          model: GraphicSet() as T,
        );

  /// 白板路径
  MqttWhiteboardApi.onPointPathMessage({
    required this.surgeryId,
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "whiteboard:point:path",
          body: {},
          model: Graphic() as T,
        );

  /// 白板重绘
  MqttWhiteboardApi.onRedoMessage({
    required this.surgeryId,
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "whiteboard:redo",
          body: {},
          model: GraphicSet() as T,
        );

  /// 白板回退
  MqttWhiteboardApi.onUndoMessage({
    required this.surgeryId,
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "whiteboard:undo",
          body: {},
          model: GraphicSet() as T,
        );
}
