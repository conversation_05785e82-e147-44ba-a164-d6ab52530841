import 'package:app_foundation/app_foundation.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/models/mqtt/file.model.dart';

class MqttDeviceFileApi<T extends AppModel> extends MqttApi<T> {
  @override
  String get topic => "/surgsmart/device";

  MqttDeviceFileApi.onProgressMessage({
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.local,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "device:file:progress",
          body: {},
          model: ApiModel() as T,
        );

  MqttDeviceFileApi.onStatsMessage({
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.local,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "device:file:stats",
          body: {},
          model: AllFileStatsInfo() as T,
        );

  MqttDeviceFileApi.onResultMessage({
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.local,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "device:file:result",
          body: {},
          model: ApiModel() as T,
        );
}
