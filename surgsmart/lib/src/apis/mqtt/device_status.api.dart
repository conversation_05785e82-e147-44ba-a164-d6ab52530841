import 'package:app_foundation/app_foundation.dart';
import 'package:meta/meta.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:surgsmart/src/apis/api.dart';

class MqttDeviceStatusApi<T extends AppModel> extends MqttApi<T> {
  /// 以下代码, 避免恢复订阅时, 源事件丢失, 详见 [MqttApiObserver] 实现逻辑
  /// ```
  /// if (onMessage == null) {
  ///   return super.listen(null);
  /// }
  /// ```
  @useResult
  @override
  MqttApiObserver listen(void Function(T message)? onMessage) {
    if (onMessage == null) {
      return super.listen(null);
    }
    return super.listen((message) {
      onMessage(message);
    });
  }

  /// 发布设备手术状态信息
  ///
  /// - [deviceId] 设备id
  ///
  /// - [surgeryId] 手术ID, 表示是否有手术
  ///
  /// - [isLiveOngoing] 是否正在直播
  MqttDeviceStatusApi.publishSurgeryStatus({
    required int? deviceId,
    required int? surgeryId,
    required bool isLiveOngoing,
    required bool isExternalCameraOn,
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyPublish,
          qos: MqttQos.exactlyOnce,
          topic: "/surgsmart/device/$deviceId/surgery/status",
          type: "device:surgery:status",
          body: {
            "surgeryId": surgeryId,
            "isLiveOngoing": isLiveOngoing,
            "isExternalCameraOn": isExternalCameraOn
          },
          model: ApiModel() as T,
        );

  /// 发布设备应用状态信息(目前用于虚拟手术间业务)
  ///
  /// - [deviceId] 设备id
  ///
  /// - [liveUserCount] 直播人员数量
  ///
  /// - [consultationUserCount] 协同人员数量
  ///
  /// - [phaseId] 当前阶段ID
  MqttDeviceStatusApi.publishActiveStatus({
    required int? deviceId,
    required int? liveUserCount,
    required int consultationUserCount,
    required int? phaseId,
    bool isThumbnailUpdated = false,
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyPublish,
          qos: MqttQos.exactlyOnce,
          topic: "/surgsmart/devices/$deviceId/status/app",
          type: "device:status:app",
          body: {
            "consultation_user_count": consultationUserCount,
            "live_user_count": liveUserCount,
            "phase_id": phaseId,
            "is_thumbnail_updated": isThumbnailUpdated,
          },
          model: ApiModel() as T,
        );

  /// 发布设备在线状态
  ///
  /// - [deviceId] 设备id
  ///
  /// - [isOnline] 表示是否在线
  ///
  MqttDeviceStatusApi.publishDeviceOnline({
    required int? deviceId,
    required bool isOnline,
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyPublish,
          qos: MqttQos.exactlyOnce,
          topic: "/surgsmart/devices/$deviceId/status/online",
          type: "device:status:online",
          body: {"is_online": isOnline},
          model: ApiModel() as T,
        );
}
