import 'package:app_foundation/app_foundation.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:surgsmart/src/apis/api.dart';

class MqttRoomControlApi<T extends AppModel> extends MqttApi<T> {
  /// - [id] 用户id
  /// - [surgId] 手术id
  /// - [id] 控制协同麦克风关闭
  MqttRoomControlApi.broadcastSynergyMicrophoneState({
    required int id,
    required int surgId,
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyPublish,
          qos: MqttQos.atMostOnce,
          topic: "/surgsmart/surgeries/$surgId/consultation",
          type: "consultation:microphone:mute",
          body: {"id": id},
          model: ApiModel() as T,
        );
}
