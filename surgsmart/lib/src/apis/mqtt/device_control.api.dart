import 'package:app_foundation/app_foundation.dart';
import 'package:meta/meta.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/models/mqtt/device.model.dart';
import 'package:surgsmart/src/tools/app_context.dart';

/// 远程遥控错误
enum DeviceControlStatus {
  /// 成功
  success(200, "操作成功!"),

  nothing(201, "设备空闲"),

  exportFile(202, "正在导出文件"),

  uploadFile(203, "正在上传文件"),

  intraOperative(204, "正在手术"),

  ready(205, "设备已就绪"),

  /// 无效令牌
  invalidToken(400, "令牌无效, 请重新扫描二维码!"),

  /// 令牌过期
  tokenExpired(401, "令牌过期, 请重新扫描二维码!"),

  /// 手术存在
  surgeryExist(402, "手术存在, 请先结束手术!"),

  /// 创建手术失败
  createSurgeryFailed(403, "创建手术失败!"),

  /// 强制更新弹窗
  forceUpdateDialog(404, "检测到新版本，请更新"),

  /// 系统更新中
  systemUpdate(405, "系统更新中，请稍等...");

  /// 错误码
  final int code;

  /// 错误信息
  final String message;

  /// 构造函数
  ///
  /// - [code] 错误码
  ///
  /// - [message] 错误信息
  const DeviceControlStatus(this.code, this.message);

  Map<String, dynamic> toMap() => {"code": code, "message": message};
}

class MqttDeviceControlApi<T extends AppModel> extends MqttApi<T> {
  /// 控制令牌
  final String? _controlToken;

  @override
  String get topic {
    final deviceId = AppContext.share.authorizeInfo.deviceId;
    if (_controlToken?.isNotEmpty == true) {
      return "/surgsmart/controller/device/$deviceId/$_controlToken";
    } else {
      return "/surgsmart/controller/device/$deviceId";
    }
  }

  /// 校验远端控制令牌
  ///
  /// - [remoteToken] 远端控制令牌
  ///
  /// - [onRefreshed] 令牌刷新回调 lastToken 原控制令牌, 需要踢掉
  DeviceControlStatus verifyControlToken({
    required String? remoteToken,
    required void Function(String lastToken) onRefreshed,
  }) {
    if (remoteToken == null || remoteToken.isEmpty) {
      return DeviceControlStatus.invalidToken;
    }
    if (remoteToken == AppContext.share.controlToken.current.value) {
      AppContext.share.controlToken.refresh(
        discardLast: (lastToken) {
          if (lastToken?.isNotEmpty == true) {
            onRefreshed(lastToken!);
          }
          return true;
        },
      );
    }
    if (remoteToken != AppContext.share.controlToken.last) {
      return DeviceControlStatus.tokenExpired;
    }
    return DeviceControlStatus.success;
  }

  /// 以下代码, 避免恢复订阅时, 源事件丢失, 详见 [MqttApiObserver] 实现逻辑
  /// ```
  /// if (onMessage == null) {
  ///   return super.listen(null);
  /// }
  /// ```
  @useResult
  @override
  MqttApiObserver listen(void Function(T message)? onMessage) {
    if (onMessage == null) {
      return super.listen(null);
    }
    return super.listen((message) {
      //如果正在安装更新，不接收遥控指令
      if (AppContext.share.taskState == TaskState.install) {
        return;
      }
      final token = (message as dynamic).token;
      final status = verifyControlToken(
        remoteToken: token,
        onRefreshed: (lastToken) {
          MqttDeviceControlApi.broadcastControlStatus(
            controlToken: lastToken,
            status: DeviceControlStatus.tokenExpired,
          ).publish();
        },
      );
      if (DeviceControlStatus.success != status) {
        /// 异常抛出
        MqttDeviceControlApi.broadcastControlStatus(
          controlToken: token,
          status: status,
        ).publish();
        return;
      }
      onMessage(message);
    });
  }

  /// 广播设备 APP 状态
  ///
  /// - [status] 设备状态，默认为: ready
  ///
  /// - [message] 设备状态消息，默认为: 设备APP已就绪
  MqttDeviceControlApi.broadcastAppStatus({
    String status = 'ready',
    String message = '设备已就绪',
    super.enableLog = false,
  })  : _controlToken = null,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyPublish,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "controller:app:status",
          body: {"status": status, "message": message},
          model: ApiModel() as T,
        );

  /// 广播设备服务器授权令牌
  ///
  /// - [controlToken] 设备控制令牌
  ///
  /// - [authToken] 服务器授权令牌
  MqttDeviceControlApi.broadcastAuthToken({
    required String controlToken,
    required String authToken,
    super.enableLog = false,
  })  : _controlToken = controlToken,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyPublish,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "controller:auth:status",
          body: {"appToken": authToken},
          model: ApiModel() as T,
        );

  /// 广播设备麦克风设置状态
  ///
  /// - [controlToken] 设备控制令牌
  ///
  /// - [enabled] 麦克风状态
  MqttDeviceControlApi.broadcastMicrophoneSettingStatus({
    required String controlToken,
    required bool enabled,
    super.enableLog = false,
  })  : _controlToken = controlToken,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyPublish,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "controller:microphone:status",
          body: {"enabled": enabled},
          model: ApiModel() as T,
        );

  /// 广播设备体腔外功能设置状态
  ///
  /// - [controlToken] 设备控制令牌
  ///
  /// - [enabled] 体腔外功能状态
  MqttDeviceControlApi.broadcastBodyoutMaskSettingStatus({
    required String controlToken,
    required bool enabled,
    super.enableLog = false,
  })  : _controlToken = controlToken,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyPublish,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "controller:bodyoutmask:status",
          body: {"enabled": enabled},
          model: ApiModel() as T,
        );

  /// 广播设备手术状态信息
  ///
  /// - [controlToken] 设备控制令牌
  ///
  /// - [surgeryId] 手术ID, 表示是否有手术
  ///
  /// - [isLiveOngoing] 是否正在直播
  MqttDeviceControlApi.broadcastSurgeryStatus({
    required String controlToken,
    required int? surgeryId,
    required bool isLiveOngoing,
    required bool isExternalCameraOn,
    super.enableLog = false,
  })  : _controlToken = controlToken,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyPublish,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "controller:surgery:status",
          body: {"surgeryId": surgeryId, "isLiveOngoing": isLiveOngoing, "isExternalCameraOn": isExternalCameraOn},
          model: ApiModel() as T,
        );

  /// 广播设备控制错误信息
  ///
  /// - [controlToken] 设备控制令牌
  ///
  /// - [status] 设备控制状态
  MqttDeviceControlApi.broadcastControlStatus({
    required String controlToken,
    required DeviceControlStatus status,
    super.enableLog = false,
  })  : _controlToken = controlToken,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyPublish,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "controller:device:status",
          body: status.toMap(),
          model: ApiModel() as T,
        );

  /// 监听设备授权登录请求, 获取服务器授权令牌, 刷新设备控制令牌
  MqttDeviceControlApi.onAuthLoginRequest({
    super.enableLog = false,
  })  : _controlToken = null,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "controller:auth:login",
          body: {},
          model: DeviceControlMessage() as T,
        );

  /// 监听设备关机请求
  MqttDeviceControlApi.onDeviceShutdown({
    super.enableLog = false,
  })  : _controlToken = null,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "controller:device:shutdown",
          body: {},
          model: DeviceControlMessage() as T,
        );

  /// 监听设备重启动请求
  MqttDeviceControlApi.onRestartRequest({
    super.enableLog = false,
  })  : _controlToken = null,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "controller:device:restart",
          body: {},
          model: DeviceControlMessage() as T,
        );

  /// 监听设备 APP 重启动请求
  MqttDeviceControlApi.onAppRestartRequest({
    super.enableLog = false,
  })  : _controlToken = null,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "controller:app:restart",
          body: {},
          model: DeviceControlMessage() as T,
        );

  /// 监听设备设置获取请求
  MqttDeviceControlApi.onSettingReadRequest({
    super.enableLog = false,
  })  : _controlToken = null,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "controller:settings:read",
          body: {},
          model: DeviceControlMessage() as T,
        );

  /// 监听设备开始手术请求
  MqttDeviceControlApi.onSurgeryStartRequest({
    super.enableLog = false,
  })  : _controlToken = null,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "controller:surgery:start",
          body: {},
          model: DeviceSurgeryStartMessage() as T,
        );

  /// 监听设备结束手术请求
  MqttDeviceControlApi.onSurgeryStopRequest({
    super.enableLog = false,
  })  : _controlToken = null,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "controller:surgery:stop",
          body: {},
          model: DeviceControlMessage() as T,
        );

  /// 监听设备读取手术信息请求
  MqttDeviceControlApi.onSurgeryReadRequest({
    super.enableLog = false,
  })  : _controlToken = null,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "controller:surgery:read",
          body: {},
          model: DeviceControlMessage() as T,
        );

  /// 监听设备开始直播请求
  MqttDeviceControlApi.onLiveStartRequest({
    super.enableLog = false,
  })  : _controlToken = null,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "controller:live:start",
          body: {},
          model: DeviceStartLiveMessage() as T,
        );

  /// 监听设备结束直播请求
  MqttDeviceControlApi.onLiveStopRequest({
    super.enableLog = false,
  })  : _controlToken = null,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "controller:live:stop",
          body: {},
          model: DeviceControlMessage() as T,
        );

  /// 监听设备麦克风设置请求
  MqttDeviceControlApi.onMicrophoneSettingRequest({
    super.enableLog = false,
  })  : _controlToken = null,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "controller:microphone:status",
          body: {},
          model: DeviceSettingMessage() as T,
        );

  /// 监听设备体腔外模糊设置请求
  MqttDeviceControlApi.onBodyoutMaskSettingRequest({
    super.enableLog = false,
  })  : _controlToken = null,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "controller:bodyoutmask:status",
          body: {},
          model: DeviceSettingMessage() as T,
        );

  /// 监听设备睿标记请求
  MqttDeviceControlApi.onMarkTimeRequest({
    super.enableLog = false,
  })  : _controlToken = null,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "controller:surgery:add-review",
          body: {},
          model: DeviceControlMessage() as T,
        );

  /// 已生成出血回放视频通知
  MqttDeviceControlApi.broadcastBleedingStatus({
    required String controlToken,
    required int? surgeryId,
    required bool isBleeding,
    super.enableLog = false,
  })  : _controlToken = controlToken,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyPublish,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "ai:bleeding",
          body: {},
          model: ApiModel() as T,
        );

  /// 请求当前设备状态
  MqttDeviceControlApi.onDeviceState({
    super.enableLog = false,
  })  : _controlToken = null,
        super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "controller:device:state",
          body: {},
          model: DeviceControlMessage() as T,
        );
}
