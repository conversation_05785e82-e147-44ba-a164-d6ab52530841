import 'package:app_foundation/app_foundation.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/models/mqtt/device_ai.model.dart';

class MqttDeviceAiApi<T extends AppModel> extends MqttApi<T> {
  final int surgeryId;

  static String _makeTopic(int surgeryId, String? suffix) {
    if (suffix?.isNotEmpty == true) {
      return "/surgsmart/surgeries/$surgeryId/$suffix";
    }
    return "/surgsmart/surgeries/$surgeryId";
  }

  /// 监听ai识别消息
  MqttDeviceAiApi.onAiFullMessage({
    required this.surgeryId,
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.local,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: _makeTopic(surgeryId, "ai/"),
          type: "ai:full",
          body: {},
          model: AiFullData() as T,
        );

  /// 监听算法事件数量消息
  MqttDeviceAiApi.onEventCountMessage({
    required this.surgeryId,
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.local,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: _makeTopic(surgeryId, "ai/event"),
          type: "ai:event:count",
          body: {},
          model: EventInsightListInfo() as T,
        );

  /// 监听算法最新事件消息
  MqttDeviceAiApi.onEventLatestMessage({
    required this.surgeryId,
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.local,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: _makeTopic(surgeryId, "ai/event"),
          type: "ai:event:latest",
          body: {},
          model: ApiModel() as T,
        );

  /// 监听算法体腔内外事件消息
  MqttDeviceAiApi.onBodyMessage({
    required this.surgeryId,
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.local,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: _makeTopic(surgeryId, "ai/body"),
          type: "ai:body",
          body: {},
          model: EventOperationListInfo() as T,
        );

  /// 监听算法手术阶段消息
  MqttDeviceAiApi.onPhaseMessage({
    required this.surgeryId,
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.local,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: _makeTopic(surgeryId, "ai/phase"),
          type: "ai:phase",
          body: {},
          model: EventOperationListInfo() as T,
        );

  /// 监听算法手术CVS评分消息
  MqttDeviceAiApi.onCvsMessage({
    required this.surgeryId,
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.local,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: _makeTopic(surgeryId, "ai/cvs"),
          type: "ai:cvs",
          body: {},
          model: CvsInfo() as T,
        );

  /// 监听出血消息
  MqttDeviceAiApi.onBleedingMessage({
    required this.surgeryId,
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.local,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: _makeTopic(surgeryId, "ai/bleeding"),
          type: "ai:bleeding",
          body: {},
          model: BleedingData() as T,
        );
}
