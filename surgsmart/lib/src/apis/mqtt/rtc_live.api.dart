import 'package:app_foundation/app_foundation.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/models/mqtt/rtc_live.model.dart';

class MqttRtcLiveApi<T extends AppModel> extends MqttApi<T> {
  final int surgeryId;

  @override
  String get topic => "/surgsmart/surgeries/$surgeryId/consultation";

  /// 监听RTC协同正在说话的用户
  MqttRtcLiveApi.onSpeakerMessage({
    required this.surgeryId,
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "consultation:volume",
          body: {},
          model: SpeakerMessage() as T,
        );

  /// 监听RTC用户麦克风开关状态
  MqttRtcLiveApi.onMicrophoneMessage({
    required this.surgeryId,
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.remote,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "",
          type: "consultation:microphone:status",
          body: {},
          model: MicrophoneMessage() as T,
        );
}
