import 'package:app_foundation/app_foundation.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/models/mqtt/device_upgrade.model.dart';
import 'package:surgsmart/src/models/mqtt/upgrade_state.model.dart';

class MqttDeviceUpgradeApi<T extends AppModel> extends MqttApi<T> {
  MqttDeviceUpgradeApi.onProgressMessage({
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.local,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "/surgsmart/device/upgrade/progress",
          type: "device:upgrade",
          body: {},
          model: DeviceUpgradeProgressInfo() as T,
        );

  MqttDeviceUpgradeApi.onDownloadResultMessage({
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.local,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "/surgsmart/device/upgrade/message",
          type: "download:result",
          body: {},
          model: UpgradeStateInfo() as T,
        );

  MqttDeviceUpgradeApi.onInstallResultMessage({
    super.enableLog = false,
  }) : super(
          apiType: MqttApiType.local,
          event: MqttApiEvent.onlyListen,
          qos: MqttQos.atMostOnce,
          topic: "/surgsmart/device/upgrade/message",
          type: "install:result",
          body: {},
          model: UpgradeStateInfo() as T,
        );
}
