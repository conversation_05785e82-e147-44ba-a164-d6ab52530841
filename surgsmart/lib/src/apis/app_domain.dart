class AppDomain {
  late final Uri surgsmartUri;
  late final Uri upgraderUri;
  late final Uri remoteMqttUri;
  late final Uri localeMqttUri;
  late final Uri webBaseUri;
  late final Uri checkNetworkUri;

  static final share = AppDomain._();
  AppDomain._() {
    const mode = String.fromEnvironment('mode', defaultValue: 'development');
    late final String localHost;
    switch (mode) {
      // 设置开发环境下的域名
      case 'development':
        // 设置测试环境下的域名
        localHost = "127.0.0.1";
        webBaseUri = Uri.parse("https://app.dev.surgsmart.com");
        remoteMqttUri = Uri.parse('tcp://mqtt.dev.surgsmart.com:1883');
        checkNetworkUri = Uri.parse('https://api.dev.surgsmart.com/systime/');

      case 'staging':
        localHost = "127.0.0.1";
        webBaseUri = Uri.parse("https://app.stage.surgsmart.com");
        remoteMqttUri = Uri.parse('tcp://mqtt.stage.surgsmart.com:1883');
        checkNetworkUri = Uri.parse('https://api.stage.surgsmart.com/systime/');

        break;
      // 设置生产环境下的域名
      case 'release':
        localHost = "127.0.0.1";
        webBaseUri = Uri.parse("https://app.surgsmart.com");
        remoteMqttUri = Uri.parse('tcp://mqtt.surgsmart.com:1883');
        checkNetworkUri = Uri.parse('https://api.surgsmart.com/systime/');

        break;
      default:
        throw Exception("未知的 mode: $mode");
    }

    surgsmartUri = Uri.parse("http://$localHost:9001");
    upgraderUri = Uri.parse("http://$localHost:5005");
    localeMqttUri = Uri.parse("tcp://$localHost:1883");
  }
}
