// IT IS GENERATED BY FLR - DO NOT MODIFY BY HAND
        // YOU CAN GET MORE DETAILS ABOUT FLR FROM:
        // - https://github.com/Fly-Mix/flr-cli
        // - https://github.com/Fly-Mix/flr-vscode-extension
        // - https://github.com/Fly-Mix/flr-as-plugin
        //

        // ignore_for_file: depend_on_referenced_packages

        // ignore: unused_import
        import 'package:flutter/widgets.dart';
        // ignore: unused_import
        import 'package:flutter/services.dart' show rootBundle;
        // ignore: unused_import
        import 'package:path/path.dart' as path;
        // ignore: unused_import
        import 'package:flutter_svg/flutter_svg.dart';
        // ignore: unused_import
        import 'package:r_dart_library/asset_svg.dart';
        
        /// This `R` class is generated and contains references to static asset resources.
        class R {
          /// package name: surgsmart
          static const package = "surgsmart";
        
          /// This `R.image` struct is generated, and contains static references to static non-svg type image asset resources.
          static const image = _R_Image();
        
          /// This `R.svg` struct is generated, and contains static references to static svg type image asset resources.
          static const svg = _R_Svg();
        
          /// This `R.text` struct is generated, and contains static references to static text asset resources.
          static const text = _R_Text();
        
          /// This `R.fontFamily` struct is generated, and contains static references to static font asset resources.
          static const fontFamily = _R_FontFamily();
        }
/// Asset resource’s metadata class.
      /// For example, here is the metadata of `packages/flutter_demo/assets/images/example.png` asset:
      /// - packageName：flutter_demo
      /// - assetName：assets/images/example.png
      /// - fileDirname：assets/images
      /// - fileBasename：example.png
      /// - fileBasenameNoExtension：example
      /// - fileExtname：.png
      class AssetResource {
        /// Creates an object to hold the asset resource’s metadata.
        const AssetResource(this.assetName, {this.packageName});
      
        /// The name of the main asset from the set of asset resources to choose from.
        final String assetName;
      
        /// The name of the package from which the asset resource is included.
        final String? packageName;
      
        /// The name used to generate the key to obtain the asset resource. For local assets
        /// this is [assetName], and for assets from packages the [assetName] is
        /// prefixed 'packages/<package_name>/'.
        String get keyName => packageName == null ? assetName : "packages/$packageName/$assetName";
      
        /// The file basename of the asset resource.
        String get fileBasename {
          final basename = path.basename(assetName);
          return basename;
        }
      
        /// The no extension file basename of the asset resource.
        String get fileBasenameNoExtension {
          final basenameWithoutExtension = path.basenameWithoutExtension(assetName);
          return basenameWithoutExtension;
        }
      
        /// The file extension name of the asset resource.
        String get fileExtname {
          final extension = path.extension(assetName);
          return extension;
        }
      
        /// The directory path name of the asset resource.
        String get fileDirname {
          var dirname = assetName;
          if (packageName != null) {
            final packageStr = "packages/$packageName/";
            dirname = dirname.replaceAll(packageStr, "");
          }
          final filenameStr = "$fileBasename/";
          dirname = dirname.replaceAll(filenameStr, "");
          return dirname;
        }
      }
// ignore: camel_case_types
      class _R_Image_AssetResource {
        const _R_Image_AssetResource();
      
  /// asset: lib/assets/images/common/add.png
      // ignore: non_constant_identifier_names
      final add = const AssetResource("assets/images/common/add.png", packageName: R.package);
  /// asset: lib/assets/images/common/await_smart.png
      // ignore: non_constant_identifier_names
      final await_smart = const AssetResource("assets/images/common/await_smart.png", packageName: R.package);
  /// asset: lib/assets/images/common/background.png
      // ignore: non_constant_identifier_names
      final background = const AssetResource("assets/images/common/background.png", packageName: R.package);
  /// asset: lib/assets/images/common/call_volume_off.png
      // ignore: non_constant_identifier_names
      final call_volume_off = const AssetResource("assets/images/common/call_volume_off.png", packageName: R.package);
  /// asset: lib/assets/images/common/call_volume_on.png
      // ignore: non_constant_identifier_names
      final call_volume_on = const AssetResource("assets/images/common/call_volume_on.png", packageName: R.package);
  /// asset: lib/assets/images/common/company_logo.png
      // ignore: non_constant_identifier_names
      final company_logo = const AssetResource("assets/images/common/company_logo.png", packageName: R.package);
  /// asset: lib/assets/images/common/icon_backspace.png
      // ignore: non_constant_identifier_names
      final icon_backspace = const AssetResource("assets/images/common/icon_backspace.png", packageName: R.package);
  /// asset: lib/assets/images/common/icon_shift.png
      // ignore: non_constant_identifier_names
      final icon_shift = const AssetResource("assets/images/common/icon_shift.png", packageName: R.package);
  /// asset: lib/assets/images/common/icon_shift_selected.png
      // ignore: non_constant_identifier_names
      final icon_shift_selected = const AssetResource("assets/images/common/icon_shift_selected.png", packageName: R.package);
  /// asset: lib/assets/images/common/listen_after_smart.png
      // ignore: non_constant_identifier_names
      final listen_after_smart = const AssetResource("assets/images/common/listen_after_smart.png", packageName: R.package);
  /// asset: lib/assets/images/common/listen_before_smart.png
      // ignore: non_constant_identifier_names
      final listen_before_smart = const AssetResource("assets/images/common/listen_before_smart.png", packageName: R.package);
  /// asset: lib/assets/images/common/listen_fail.png
      // ignore: non_constant_identifier_names
      final listen_fail = const AssetResource("assets/images/common/listen_fail.png", packageName: R.package);
  /// asset: lib/assets/images/common/listen_middle_smart.png
      // ignore: non_constant_identifier_names
      final listen_middle_smart = const AssetResource("assets/images/common/listen_middle_smart.png", packageName: R.package);
  /// asset: lib/assets/images/common/listen_success.png
      // ignore: non_constant_identifier_names
      final listen_success = const AssetResource("assets/images/common/listen_success.png", packageName: R.package);
  /// asset: lib/assets/images/common/logo.png
      // ignore: non_constant_identifier_names
      final logo = const AssetResource("assets/images/common/logo.png", packageName: R.package);
  /// asset: lib/assets/images/common/logo_only.png
      // ignore: non_constant_identifier_names
      final logo_only = const AssetResource("assets/images/common/logo_only.png", packageName: R.package);
  /// asset: lib/assets/images/common/logo_variant.png
      // ignore: non_constant_identifier_names
      final logo_variant = const AssetResource("assets/images/common/logo_variant.png", packageName: R.package);
  /// asset: lib/assets/images/common/minus.png
      // ignore: non_constant_identifier_names
      final minus = const AssetResource("assets/images/common/minus.png", packageName: R.package);
  /// asset: lib/assets/images/common/no_picture.png
      // ignore: non_constant_identifier_names
      final no_picture = const AssetResource("assets/images/common/no_picture.png", packageName: R.package);
  /// asset: lib/assets/images/common/show_smart.png
      // ignore: non_constant_identifier_names
      final show_smart = const AssetResource("assets/images/common/show_smart.png", packageName: R.package);
  /// asset: lib/assets/images/common/smart_voice_bg.png
      // ignore: non_constant_identifier_names
      final smart_voice_bg = const AssetResource("assets/images/common/smart_voice_bg.png", packageName: R.package);
  /// asset: lib/assets/images/common/video_pause.png
      // ignore: non_constant_identifier_names
      final video_pause = const AssetResource("assets/images/common/video_pause.png", packageName: R.package);
  /// asset: lib/assets/images/common/video_pause_gray.png
      // ignore: non_constant_identifier_names
      final video_pause_gray = const AssetResource("assets/images/common/video_pause_gray.png", packageName: R.package);
  /// asset: lib/assets/images/common/video_playing.png
      // ignore: non_constant_identifier_names
      final video_playing = const AssetResource("assets/images/common/video_playing.png", packageName: R.package);
  /// asset: lib/assets/images/common/voice_bottom_vfx1.png
      // ignore: non_constant_identifier_names
      final voice_bottom_vfx1 = const AssetResource("assets/images/common/voice_bottom_vfx1.png", packageName: R.package);
  /// asset: lib/assets/images/common/voice_bottom_vfx2.png
      // ignore: non_constant_identifier_names
      final voice_bottom_vfx2 = const AssetResource("assets/images/common/voice_bottom_vfx2.png", packageName: R.package);
  /// asset: lib/assets/images/common/xr_small.png
      // ignore: non_constant_identifier_names
      final xr_small = const AssetResource("assets/images/common/xr_small.png", packageName: R.package);
  /// asset: lib/assets/images/report/date.png
      // ignore: non_constant_identifier_names
      final date = const AssetResource("assets/images/report/date.png", packageName: R.package);
  /// asset: lib/assets/images/report/time.png
      // ignore: non_constant_identifier_names
      final time = const AssetResource("assets/images/report/time.png", packageName: R.package);
  /// asset: lib/assets/images/setting/device_version.png
      // ignore: non_constant_identifier_names
      final device_version = const AssetResource("assets/images/setting/device_version.png", packageName: R.package);
  /// asset: lib/assets/images/setting/icon_failed.png
      // ignore: non_constant_identifier_names
      final icon_failed = const AssetResource("assets/images/setting/icon_failed.png", packageName: R.package);
  /// asset: lib/assets/images/setting/icon_selected.png
      // ignore: non_constant_identifier_names
      final icon_selected = const AssetResource("assets/images/setting/icon_selected.png", packageName: R.package);
  /// asset: lib/assets/images/setting/icon_success.png
      // ignore: non_constant_identifier_names
      final icon_success = const AssetResource("assets/images/setting/icon_success.png", packageName: R.package);
  /// asset: lib/assets/images/setting/icon_wired_selected.png
      // ignore: non_constant_identifier_names
      final icon_wired_selected = const AssetResource("assets/images/setting/icon_wired_selected.png", packageName: R.package);
  /// asset: lib/assets/images/setting/outside_blur.png
      // ignore: non_constant_identifier_names
      final outside_blur = const AssetResource("assets/images/setting/outside_blur.png", packageName: R.package);
  /// asset: lib/assets/images/setting/setting_icon.png
      // ignore: non_constant_identifier_names
      final setting_icon = const AssetResource("assets/images/setting/setting_icon.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/add_mark.png
      // ignore: non_constant_identifier_names
      final add_mark = const AssetResource("assets/images/surgery/add_mark.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/ai_detector.png
      // ignore: non_constant_identifier_names
      final ai_detector = const AssetResource("assets/images/surgery/ai_detector.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/bg_robot.png
      // ignore: non_constant_identifier_names
      final bg_robot = const AssetResource("assets/images/surgery/bg_robot.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/ble_device.png
      // ignore: non_constant_identifier_names
      final ble_device = const AssetResource("assets/images/surgery/ble_device.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/blood_mask.png
      // ignore: non_constant_identifier_names
      final blood_mask = const AssetResource("assets/images/surgery/blood_mask.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/close_icon.png
      // ignore: non_constant_identifier_names
      final close_icon = const AssetResource("assets/images/surgery/close_icon.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/create_surgery.png
      // ignore: non_constant_identifier_names
      final create_surgery = const AssetResource("assets/images/surgery/create_surgery.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/create_surgery_background.png
      // ignore: non_constant_identifier_names
      final create_surgery_background = const AssetResource("assets/images/surgery/create_surgery_background.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/cvs_average_background.png
      // ignore: non_constant_identifier_names
      final cvs_average_background = const AssetResource("assets/images/surgery/cvs_average_background.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/cvs_score.png
      // ignore: non_constant_identifier_names
      final cvs_score = const AssetResource("assets/images/surgery/cvs_score.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/default_avatar.png
      // ignore: non_constant_identifier_names
      final default_avatar = const AssetResource("assets/images/surgery/default_avatar.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/doctor_muted.png
      // ignore: non_constant_identifier_names
      final doctor_muted = const AssetResource("assets/images/surgery/doctor_muted.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/doctor_speaking.png
      // ignore: non_constant_identifier_names
      final doctor_speaking = const AssetResource("assets/images/surgery/doctor_speaking.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/doctor_unmuted.png
      // ignore: non_constant_identifier_names
      final doctor_unmuted = const AssetResource("assets/images/surgery/doctor_unmuted.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/error_icon.png
      // ignore: non_constant_identifier_names
      final error_icon = const AssetResource("assets/images/surgery/error_icon.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/file.png
      // ignore: non_constant_identifier_names
      final file = const AssetResource("assets/images/surgery/file.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/finger_large.png
      // ignore: non_constant_identifier_names
      final finger_large = const AssetResource("assets/images/surgery/finger_large.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/finger_small.png
      // ignore: non_constant_identifier_names
      final finger_small = const AssetResource("assets/images/surgery/finger_small.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/icon_robot.png
      // ignore: non_constant_identifier_names
      final icon_robot = const AssetResource("assets/images/surgery/icon_robot.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/luminance.png
      // ignore: non_constant_identifier_names
      final luminance = const AssetResource("assets/images/surgery/luminance.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/mic_off.png
      // ignore: non_constant_identifier_names
      final mic_off = const AssetResource("assets/images/surgery/mic_off.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/mic_on.png
      // ignore: non_constant_identifier_names
      final mic_on = const AssetResource("assets/images/surgery/mic_on.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/microphone_off.png
      // ignore: non_constant_identifier_names
      final microphone_off = const AssetResource("assets/images/surgery/microphone_off.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/microphone_on.png
      // ignore: non_constant_identifier_names
      final microphone_on = const AssetResource("assets/images/surgery/microphone_on.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/playback.png
      // ignore: non_constant_identifier_names
      final playback = const AssetResource("assets/images/surgery/playback.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/playback_no.png
      // ignore: non_constant_identifier_names
      final playback_no = const AssetResource("assets/images/surgery/playback_no.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/qr_background.png
      // ignore: non_constant_identifier_names
      final qr_background = const AssetResource("assets/images/surgery/qr_background.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/qr_code.png
      // ignore: non_constant_identifier_names
      final qr_code = const AssetResource("assets/images/surgery/qr_code.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/qrcode_bg.png
      // ignore: non_constant_identifier_names
      final qrcode_bg = const AssetResource("assets/images/surgery/qrcode_bg.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/qrcode_icon.png
      // ignore: non_constant_identifier_names
      final qrcode_icon = const AssetResource("assets/images/surgery/qrcode_icon.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/remote_control.png
      // ignore: non_constant_identifier_names
      final remote_control = const AssetResource("assets/images/surgery/remote_control.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/remote_control_no.png
      // ignore: non_constant_identifier_names
      final remote_control_no = const AssetResource("assets/images/surgery/remote_control_no.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/rtc_live.png
      // ignore: non_constant_identifier_names
      final rtc_live = const AssetResource("assets/images/surgery/rtc_live.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/rtm_live.png
      // ignore: non_constant_identifier_names
      final rtm_live = const AssetResource("assets/images/surgery/rtm_live.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/rtm_live_no.png
      // ignore: non_constant_identifier_names
      final rtm_live_no = const AssetResource("assets/images/surgery/rtm_live_no.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/rtm_live_on.png
      // ignore: non_constant_identifier_names
      final rtm_live_on = const AssetResource("assets/images/surgery/rtm_live_on.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/screen/screen_show_ai.png
      // ignore: non_constant_identifier_names
      final screen_show_ai = const AssetResource("assets/images/surgery/screen/screen_show_ai.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/screen/screen_show_ai_active.png
      // ignore: non_constant_identifier_names
      final screen_show_ai_active = const AssetResource("assets/images/surgery/screen/screen_show_ai_active.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/screen/screen_show_model.png
      // ignore: non_constant_identifier_names
      final screen_show_model = const AssetResource("assets/images/surgery/screen/screen_show_model.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/screen/screen_show_model_active.png
      // ignore: non_constant_identifier_names
      final screen_show_model_active = const AssetResource("assets/images/surgery/screen/screen_show_model_active.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/screen/screen_show_original.png
      // ignore: non_constant_identifier_names
      final screen_show_original = const AssetResource("assets/images/surgery/screen/screen_show_original.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/screen/screen_show_original_active.png
      // ignore: non_constant_identifier_names
      final screen_show_original_active = const AssetResource("assets/images/surgery/screen/screen_show_original_active.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/screen/screen_show_playback.png
      // ignore: non_constant_identifier_names
      final screen_show_playback = const AssetResource("assets/images/surgery/screen/screen_show_playback.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/screen/screen_show_playback_active.png
      // ignore: non_constant_identifier_names
      final screen_show_playback_active = const AssetResource("assets/images/surgery/screen/screen_show_playback_active.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/screen_displayable.png
      // ignore: non_constant_identifier_names
      final screen_displayable = const AssetResource("assets/images/surgery/screen_displayable.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/screen_displayed.png
      // ignore: non_constant_identifier_names
      final screen_displayed = const AssetResource("assets/images/surgery/screen_displayed.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/show_rtc_members.png
      // ignore: non_constant_identifier_names
      final show_rtc_members = const AssetResource("assets/images/surgery/show_rtc_members.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/show_rtc_qrcode.png
      // ignore: non_constant_identifier_names
      final show_rtc_qrcode = const AssetResource("assets/images/surgery/show_rtc_qrcode.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/stop.png
      // ignore: non_constant_identifier_names
      final stop = const AssetResource("assets/images/surgery/stop.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/success_icon.png
      // ignore: non_constant_identifier_names
      final success_icon = const AssetResource("assets/images/surgery/success_icon.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/surgery_cooperation.png
      // ignore: non_constant_identifier_names
      final surgery_cooperation = const AssetResource("assets/images/surgery/surgery_cooperation.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/surgery_cooperation_no.png
      // ignore: non_constant_identifier_names
      final surgery_cooperation_no = const AssetResource("assets/images/surgery/surgery_cooperation_no.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/surgery_creation_background.png
      // ignore: non_constant_identifier_names
      final surgery_creation_background = const AssetResource("assets/images/surgery/surgery_creation_background.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/surgery_stop.png
      // ignore: non_constant_identifier_names
      final surgery_stop = const AssetResource("assets/images/surgery/surgery_stop.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/system_setting.png
      // ignore: non_constant_identifier_names
      final system_setting = const AssetResource("assets/images/surgery/system_setting.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/tracking.png
      // ignore: non_constant_identifier_names
      final tracking = const AssetResource("assets/images/surgery/tracking.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/usb.png
      // ignore: non_constant_identifier_names
      final usb = const AssetResource("assets/images/surgery/usb.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/volume_off.png
      // ignore: non_constant_identifier_names
      final volume_off = const AssetResource("assets/images/surgery/volume_off.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/volume_on.png
      // ignore: non_constant_identifier_names
      final volume_on = const AssetResource("assets/images/surgery/volume_on.png", packageName: R.package);
  /// asset: lib/assets/images/surgery/what_mask.png
      // ignore: non_constant_identifier_names
      final what_mask = const AssetResource("assets/images/surgery/what_mask.png", packageName: R.package);
      }
// ignore: camel_case_types
    class _R_Svg_AssetResource {
      const _R_Svg_AssetResource();
    
  /// asset: lib/assets/images/common/cellular_connected.svg
      // ignore: non_constant_identifier_names
      final cellular_connected = const AssetResource("assets/images/common/cellular_connected.svg", packageName: R.package);
  /// asset: lib/assets/images/common/cellular_disconnected.svg
      // ignore: non_constant_identifier_names
      final cellular_disconnected = const AssetResource("assets/images/common/cellular_disconnected.svg", packageName: R.package);
  /// asset: lib/assets/images/common/icon_4g.svg
      // ignore: non_constant_identifier_names
      final icon_4g = const AssetResource("assets/images/common/icon_4g.svg", packageName: R.package);
  /// asset: lib/assets/images/common/icon_5g.svg
      // ignore: non_constant_identifier_names
      final icon_5g = const AssetResource("assets/images/common/icon_5g.svg", packageName: R.package);
  /// asset: lib/assets/images/common/logos.svg
      // ignore: non_constant_identifier_names
      final logos = const AssetResource("assets/images/common/logos.svg", packageName: R.package);
  /// asset: lib/assets/images/common/network_none.svg
      // ignore: non_constant_identifier_names
      final network_none = const AssetResource("assets/images/common/network_none.svg", packageName: R.package);
  /// asset: lib/assets/images/common/wifi_connected.svg
      // ignore: non_constant_identifier_names
      final wifi_connected = const AssetResource("assets/images/common/wifi_connected.svg", packageName: R.package);
  /// asset: lib/assets/images/common/wifi_disconnected.svg
      // ignore: non_constant_identifier_names
      final wifi_disconnected = const AssetResource("assets/images/common/wifi_disconnected.svg", packageName: R.package);
  /// asset: lib/assets/images/common/wired_connected.svg
      // ignore: non_constant_identifier_names
      final wired_connected = const AssetResource("assets/images/common/wired_connected.svg", packageName: R.package);
  /// asset: lib/assets/images/common/wired_disconnected.svg
      // ignore: non_constant_identifier_names
      final wired_disconnected = const AssetResource("assets/images/common/wired_disconnected.svg", packageName: R.package);
    }
// ignore: camel_case_types
    class _R_Text_AssetResource {
      const _R_Text_AssetResource();
    
  /// asset: lib/assets/texts/insight.json
      // ignore: non_constant_identifier_names
      final insight_json = const AssetResource("assets/texts/insight.json", packageName: R.package);
  /// asset: lib/assets/texts/lottie/event_detection.json
      // ignore: non_constant_identifier_names
      final event_detection_json = const AssetResource("assets/texts/lottie/event_detection.json", packageName: R.package);
  /// asset: lib/assets/texts/lottie/event_detection_end.json
      // ignore: non_constant_identifier_names
      final event_detection_end_json = const AssetResource("assets/texts/lottie/event_detection_end.json", packageName: R.package);
  /// asset: lib/assets/texts/lottie/event_detection_long.json
      // ignore: non_constant_identifier_names
      final event_detection_long_json = const AssetResource("assets/texts/lottie/event_detection_long.json", packageName: R.package);
  /// asset: lib/assets/texts/lottie/event_detection_start.json
      // ignore: non_constant_identifier_names
      final event_detection_start_json = const AssetResource("assets/texts/lottie/event_detection_start.json", packageName: R.package);
  /// asset: lib/assets/texts/medias/bleed_warning.wav.txt
      // ignore: non_constant_identifier_names
      final bleed_warning_wav_txt = const AssetResource("assets/texts/medias/bleed_warning.wav.txt", packageName: R.package);
  /// asset: lib/assets/texts/medias/enter_meeting.wav.txt
      // ignore: non_constant_identifier_names
      final enter_meeting_wav_txt = const AssetResource("assets/texts/medias/enter_meeting.wav.txt", packageName: R.package);
  /// asset: lib/assets/texts/medias/mark_screenshot.wav.txt
      // ignore: non_constant_identifier_names
      final mark_screenshot_wav_txt = const AssetResource("assets/texts/medias/mark_screenshot.wav.txt", packageName: R.package);
  /// asset: lib/assets/texts/medias/model-1.mp4.txt
      // ignore: non_constant_identifier_names
      final model_1_mp4_txt = const AssetResource("assets/texts/medias/model-1.mp4.txt", packageName: R.package);
  /// asset: lib/assets/texts/medias/model-2.mp4.txt
      // ignore: non_constant_identifier_names
      final model_2_mp4_txt = const AssetResource("assets/texts/medias/model-2.mp4.txt", packageName: R.package);
  /// asset: lib/assets/texts/medias/model-3.mp4.txt
      // ignore: non_constant_identifier_names
      final model_3_mp4_txt = const AssetResource("assets/texts/medias/model-3.mp4.txt", packageName: R.package);
  /// asset: lib/assets/texts/medias/model-4.mp4.txt
      // ignore: non_constant_identifier_names
      final model_4_mp4_txt = const AssetResource("assets/texts/medias/model-4.mp4.txt", packageName: R.package);
  /// asset: lib/assets/texts/medias/video_tutorial.mp4.txt
      // ignore: non_constant_identifier_names
      final video_tutorial_mp4_txt = const AssetResource("assets/texts/medias/video_tutorial.mp4.txt", packageName: R.package);
  /// asset: lib/assets/texts/polkit/10-networkmanager.pkla.txt
      // ignore: non_constant_identifier_names
      final a10_networkmanager_pkla_txt = const AssetResource("assets/texts/polkit/10-networkmanager.pkla.txt", packageName: R.package);
  /// asset: lib/assets/texts/sudoers/brightnessctl.txt
      // ignore: non_constant_identifier_names
      final brightnessctl_txt = const AssetResource("assets/texts/sudoers/brightnessctl.txt", packageName: R.package);
  /// asset: lib/assets/texts/sudoers/evtest.txt
      // ignore: non_constant_identifier_names
      final evtest_txt = const AssetResource("assets/texts/sudoers/evtest.txt", packageName: R.package);
  /// asset: lib/assets/texts/sudoers/nmcli.txt
      // ignore: non_constant_identifier_names
      final nmcli_txt = const AssetResource("assets/texts/sudoers/nmcli.txt", packageName: R.package);
  /// asset: lib/assets/texts/zips/bluez-5.64.txt
      // ignore: non_constant_identifier_names
      final bluez_5_64_txt = const AssetResource("assets/texts/zips/bluez-5.64.txt", packageName: R.package);
    }
/// This `_R_Image` class is generated and contains references to static non-svg type image asset resources.
    // ignore: camel_case_types
    class _R_Image {
      const _R_Image();
    
      final asset = const _R_Image_AssetResource();
    
  /// asset: lib/assets/images/common/add.png
      // ignore: non_constant_identifier_names
      AssetImage add() {
        return AssetImage(asset.add.keyName);
      }
  /// asset: lib/assets/images/common/await_smart.png
      // ignore: non_constant_identifier_names
      AssetImage await_smart() {
        return AssetImage(asset.await_smart.keyName);
      }
  /// asset: lib/assets/images/common/background.png
      // ignore: non_constant_identifier_names
      AssetImage background() {
        return AssetImage(asset.background.keyName);
      }
  /// asset: lib/assets/images/common/call_volume_off.png
      // ignore: non_constant_identifier_names
      AssetImage call_volume_off() {
        return AssetImage(asset.call_volume_off.keyName);
      }
  /// asset: lib/assets/images/common/call_volume_on.png
      // ignore: non_constant_identifier_names
      AssetImage call_volume_on() {
        return AssetImage(asset.call_volume_on.keyName);
      }
  /// asset: lib/assets/images/common/company_logo.png
      // ignore: non_constant_identifier_names
      AssetImage company_logo() {
        return AssetImage(asset.company_logo.keyName);
      }
  /// asset: lib/assets/images/common/icon_backspace.png
      // ignore: non_constant_identifier_names
      AssetImage icon_backspace() {
        return AssetImage(asset.icon_backspace.keyName);
      }
  /// asset: lib/assets/images/common/icon_shift.png
      // ignore: non_constant_identifier_names
      AssetImage icon_shift() {
        return AssetImage(asset.icon_shift.keyName);
      }
  /// asset: lib/assets/images/common/icon_shift_selected.png
      // ignore: non_constant_identifier_names
      AssetImage icon_shift_selected() {
        return AssetImage(asset.icon_shift_selected.keyName);
      }
  /// asset: lib/assets/images/common/listen_after_smart.png
      // ignore: non_constant_identifier_names
      AssetImage listen_after_smart() {
        return AssetImage(asset.listen_after_smart.keyName);
      }
  /// asset: lib/assets/images/common/listen_before_smart.png
      // ignore: non_constant_identifier_names
      AssetImage listen_before_smart() {
        return AssetImage(asset.listen_before_smart.keyName);
      }
  /// asset: lib/assets/images/common/listen_fail.png
      // ignore: non_constant_identifier_names
      AssetImage listen_fail() {
        return AssetImage(asset.listen_fail.keyName);
      }
  /// asset: lib/assets/images/common/listen_middle_smart.png
      // ignore: non_constant_identifier_names
      AssetImage listen_middle_smart() {
        return AssetImage(asset.listen_middle_smart.keyName);
      }
  /// asset: lib/assets/images/common/listen_success.png
      // ignore: non_constant_identifier_names
      AssetImage listen_success() {
        return AssetImage(asset.listen_success.keyName);
      }
  /// asset: lib/assets/images/common/logo.png
      // ignore: non_constant_identifier_names
      AssetImage logo() {
        return AssetImage(asset.logo.keyName);
      }
  /// asset: lib/assets/images/common/logo_only.png
      // ignore: non_constant_identifier_names
      AssetImage logo_only() {
        return AssetImage(asset.logo_only.keyName);
      }
  /// asset: lib/assets/images/common/logo_variant.png
      // ignore: non_constant_identifier_names
      AssetImage logo_variant() {
        return AssetImage(asset.logo_variant.keyName);
      }
  /// asset: lib/assets/images/common/minus.png
      // ignore: non_constant_identifier_names
      AssetImage minus() {
        return AssetImage(asset.minus.keyName);
      }
  /// asset: lib/assets/images/common/no_picture.png
      // ignore: non_constant_identifier_names
      AssetImage no_picture() {
        return AssetImage(asset.no_picture.keyName);
      }
  /// asset: lib/assets/images/common/show_smart.png
      // ignore: non_constant_identifier_names
      AssetImage show_smart() {
        return AssetImage(asset.show_smart.keyName);
      }
  /// asset: lib/assets/images/common/smart_voice_bg.png
      // ignore: non_constant_identifier_names
      AssetImage smart_voice_bg() {
        return AssetImage(asset.smart_voice_bg.keyName);
      }
  /// asset: lib/assets/images/common/video_pause.png
      // ignore: non_constant_identifier_names
      AssetImage video_pause() {
        return AssetImage(asset.video_pause.keyName);
      }
  /// asset: lib/assets/images/common/video_pause_gray.png
      // ignore: non_constant_identifier_names
      AssetImage video_pause_gray() {
        return AssetImage(asset.video_pause_gray.keyName);
      }
  /// asset: lib/assets/images/common/video_playing.png
      // ignore: non_constant_identifier_names
      AssetImage video_playing() {
        return AssetImage(asset.video_playing.keyName);
      }
  /// asset: lib/assets/images/common/voice_bottom_vfx1.png
      // ignore: non_constant_identifier_names
      AssetImage voice_bottom_vfx1() {
        return AssetImage(asset.voice_bottom_vfx1.keyName);
      }
  /// asset: lib/assets/images/common/voice_bottom_vfx2.png
      // ignore: non_constant_identifier_names
      AssetImage voice_bottom_vfx2() {
        return AssetImage(asset.voice_bottom_vfx2.keyName);
      }
  /// asset: lib/assets/images/common/xr_small.png
      // ignore: non_constant_identifier_names
      AssetImage xr_small() {
        return AssetImage(asset.xr_small.keyName);
      }
  /// asset: lib/assets/images/report/date.png
      // ignore: non_constant_identifier_names
      AssetImage date() {
        return AssetImage(asset.date.keyName);
      }
  /// asset: lib/assets/images/report/time.png
      // ignore: non_constant_identifier_names
      AssetImage time() {
        return AssetImage(asset.time.keyName);
      }
  /// asset: lib/assets/images/setting/device_version.png
      // ignore: non_constant_identifier_names
      AssetImage device_version() {
        return AssetImage(asset.device_version.keyName);
      }
  /// asset: lib/assets/images/setting/icon_failed.png
      // ignore: non_constant_identifier_names
      AssetImage icon_failed() {
        return AssetImage(asset.icon_failed.keyName);
      }
  /// asset: lib/assets/images/setting/icon_selected.png
      // ignore: non_constant_identifier_names
      AssetImage icon_selected() {
        return AssetImage(asset.icon_selected.keyName);
      }
  /// asset: lib/assets/images/setting/icon_success.png
      // ignore: non_constant_identifier_names
      AssetImage icon_success() {
        return AssetImage(asset.icon_success.keyName);
      }
  /// asset: lib/assets/images/setting/icon_wired_selected.png
      // ignore: non_constant_identifier_names
      AssetImage icon_wired_selected() {
        return AssetImage(asset.icon_wired_selected.keyName);
      }
  /// asset: lib/assets/images/setting/outside_blur.png
      // ignore: non_constant_identifier_names
      AssetImage outside_blur() {
        return AssetImage(asset.outside_blur.keyName);
      }
  /// asset: lib/assets/images/setting/setting_icon.png
      // ignore: non_constant_identifier_names
      AssetImage setting_icon() {
        return AssetImage(asset.setting_icon.keyName);
      }
  /// asset: lib/assets/images/surgery/add_mark.png
      // ignore: non_constant_identifier_names
      AssetImage add_mark() {
        return AssetImage(asset.add_mark.keyName);
      }
  /// asset: lib/assets/images/surgery/ai_detector.png
      // ignore: non_constant_identifier_names
      AssetImage ai_detector() {
        return AssetImage(asset.ai_detector.keyName);
      }
  /// asset: lib/assets/images/surgery/bg_robot.png
      // ignore: non_constant_identifier_names
      AssetImage bg_robot() {
        return AssetImage(asset.bg_robot.keyName);
      }
  /// asset: lib/assets/images/surgery/ble_device.png
      // ignore: non_constant_identifier_names
      AssetImage ble_device() {
        return AssetImage(asset.ble_device.keyName);
      }
  /// asset: lib/assets/images/surgery/blood_mask.png
      // ignore: non_constant_identifier_names
      AssetImage blood_mask() {
        return AssetImage(asset.blood_mask.keyName);
      }
  /// asset: lib/assets/images/surgery/close_icon.png
      // ignore: non_constant_identifier_names
      AssetImage close_icon() {
        return AssetImage(asset.close_icon.keyName);
      }
  /// asset: lib/assets/images/surgery/create_surgery.png
      // ignore: non_constant_identifier_names
      AssetImage create_surgery() {
        return AssetImage(asset.create_surgery.keyName);
      }
  /// asset: lib/assets/images/surgery/create_surgery_background.png
      // ignore: non_constant_identifier_names
      AssetImage create_surgery_background() {
        return AssetImage(asset.create_surgery_background.keyName);
      }
  /// asset: lib/assets/images/surgery/cvs_average_background.png
      // ignore: non_constant_identifier_names
      AssetImage cvs_average_background() {
        return AssetImage(asset.cvs_average_background.keyName);
      }
  /// asset: lib/assets/images/surgery/cvs_score.png
      // ignore: non_constant_identifier_names
      AssetImage cvs_score() {
        return AssetImage(asset.cvs_score.keyName);
      }
  /// asset: lib/assets/images/surgery/default_avatar.png
      // ignore: non_constant_identifier_names
      AssetImage default_avatar() {
        return AssetImage(asset.default_avatar.keyName);
      }
  /// asset: lib/assets/images/surgery/doctor_muted.png
      // ignore: non_constant_identifier_names
      AssetImage doctor_muted() {
        return AssetImage(asset.doctor_muted.keyName);
      }
  /// asset: lib/assets/images/surgery/doctor_speaking.png
      // ignore: non_constant_identifier_names
      AssetImage doctor_speaking() {
        return AssetImage(asset.doctor_speaking.keyName);
      }
  /// asset: lib/assets/images/surgery/doctor_unmuted.png
      // ignore: non_constant_identifier_names
      AssetImage doctor_unmuted() {
        return AssetImage(asset.doctor_unmuted.keyName);
      }
  /// asset: lib/assets/images/surgery/error_icon.png
      // ignore: non_constant_identifier_names
      AssetImage error_icon() {
        return AssetImage(asset.error_icon.keyName);
      }
  /// asset: lib/assets/images/surgery/file.png
      // ignore: non_constant_identifier_names
      AssetImage file() {
        return AssetImage(asset.file.keyName);
      }
  /// asset: lib/assets/images/surgery/finger_large.png
      // ignore: non_constant_identifier_names
      AssetImage finger_large() {
        return AssetImage(asset.finger_large.keyName);
      }
  /// asset: lib/assets/images/surgery/finger_small.png
      // ignore: non_constant_identifier_names
      AssetImage finger_small() {
        return AssetImage(asset.finger_small.keyName);
      }
  /// asset: lib/assets/images/surgery/icon_robot.png
      // ignore: non_constant_identifier_names
      AssetImage icon_robot() {
        return AssetImage(asset.icon_robot.keyName);
      }
  /// asset: lib/assets/images/surgery/luminance.png
      // ignore: non_constant_identifier_names
      AssetImage luminance() {
        return AssetImage(asset.luminance.keyName);
      }
  /// asset: lib/assets/images/surgery/mic_off.png
      // ignore: non_constant_identifier_names
      AssetImage mic_off() {
        return AssetImage(asset.mic_off.keyName);
      }
  /// asset: lib/assets/images/surgery/mic_on.png
      // ignore: non_constant_identifier_names
      AssetImage mic_on() {
        return AssetImage(asset.mic_on.keyName);
      }
  /// asset: lib/assets/images/surgery/microphone_off.png
      // ignore: non_constant_identifier_names
      AssetImage microphone_off() {
        return AssetImage(asset.microphone_off.keyName);
      }
  /// asset: lib/assets/images/surgery/microphone_on.png
      // ignore: non_constant_identifier_names
      AssetImage microphone_on() {
        return AssetImage(asset.microphone_on.keyName);
      }
  /// asset: lib/assets/images/surgery/playback.png
      // ignore: non_constant_identifier_names
      AssetImage playback() {
        return AssetImage(asset.playback.keyName);
      }
  /// asset: lib/assets/images/surgery/playback_no.png
      // ignore: non_constant_identifier_names
      AssetImage playback_no() {
        return AssetImage(asset.playback_no.keyName);
      }
  /// asset: lib/assets/images/surgery/qr_background.png
      // ignore: non_constant_identifier_names
      AssetImage qr_background() {
        return AssetImage(asset.qr_background.keyName);
      }
  /// asset: lib/assets/images/surgery/qr_code.png
      // ignore: non_constant_identifier_names
      AssetImage qr_code() {
        return AssetImage(asset.qr_code.keyName);
      }
  /// asset: lib/assets/images/surgery/qrcode_bg.png
      // ignore: non_constant_identifier_names
      AssetImage qrcode_bg() {
        return AssetImage(asset.qrcode_bg.keyName);
      }
  /// asset: lib/assets/images/surgery/qrcode_icon.png
      // ignore: non_constant_identifier_names
      AssetImage qrcode_icon() {
        return AssetImage(asset.qrcode_icon.keyName);
      }
  /// asset: lib/assets/images/surgery/remote_control.png
      // ignore: non_constant_identifier_names
      AssetImage remote_control() {
        return AssetImage(asset.remote_control.keyName);
      }
  /// asset: lib/assets/images/surgery/remote_control_no.png
      // ignore: non_constant_identifier_names
      AssetImage remote_control_no() {
        return AssetImage(asset.remote_control_no.keyName);
      }
  /// asset: lib/assets/images/surgery/rtc_live.png
      // ignore: non_constant_identifier_names
      AssetImage rtc_live() {
        return AssetImage(asset.rtc_live.keyName);
      }
  /// asset: lib/assets/images/surgery/rtm_live.png
      // ignore: non_constant_identifier_names
      AssetImage rtm_live() {
        return AssetImage(asset.rtm_live.keyName);
      }
  /// asset: lib/assets/images/surgery/rtm_live_no.png
      // ignore: non_constant_identifier_names
      AssetImage rtm_live_no() {
        return AssetImage(asset.rtm_live_no.keyName);
      }
  /// asset: lib/assets/images/surgery/rtm_live_on.png
      // ignore: non_constant_identifier_names
      AssetImage rtm_live_on() {
        return AssetImage(asset.rtm_live_on.keyName);
      }
  /// asset: lib/assets/images/surgery/screen/screen_show_ai.png
      // ignore: non_constant_identifier_names
      AssetImage screen_show_ai() {
        return AssetImage(asset.screen_show_ai.keyName);
      }
  /// asset: lib/assets/images/surgery/screen/screen_show_ai_active.png
      // ignore: non_constant_identifier_names
      AssetImage screen_show_ai_active() {
        return AssetImage(asset.screen_show_ai_active.keyName);
      }
  /// asset: lib/assets/images/surgery/screen/screen_show_model.png
      // ignore: non_constant_identifier_names
      AssetImage screen_show_model() {
        return AssetImage(asset.screen_show_model.keyName);
      }
  /// asset: lib/assets/images/surgery/screen/screen_show_model_active.png
      // ignore: non_constant_identifier_names
      AssetImage screen_show_model_active() {
        return AssetImage(asset.screen_show_model_active.keyName);
      }
  /// asset: lib/assets/images/surgery/screen/screen_show_original.png
      // ignore: non_constant_identifier_names
      AssetImage screen_show_original() {
        return AssetImage(asset.screen_show_original.keyName);
      }
  /// asset: lib/assets/images/surgery/screen/screen_show_original_active.png
      // ignore: non_constant_identifier_names
      AssetImage screen_show_original_active() {
        return AssetImage(asset.screen_show_original_active.keyName);
      }
  /// asset: lib/assets/images/surgery/screen/screen_show_playback.png
      // ignore: non_constant_identifier_names
      AssetImage screen_show_playback() {
        return AssetImage(asset.screen_show_playback.keyName);
      }
  /// asset: lib/assets/images/surgery/screen/screen_show_playback_active.png
      // ignore: non_constant_identifier_names
      AssetImage screen_show_playback_active() {
        return AssetImage(asset.screen_show_playback_active.keyName);
      }
  /// asset: lib/assets/images/surgery/screen_displayable.png
      // ignore: non_constant_identifier_names
      AssetImage screen_displayable() {
        return AssetImage(asset.screen_displayable.keyName);
      }
  /// asset: lib/assets/images/surgery/screen_displayed.png
      // ignore: non_constant_identifier_names
      AssetImage screen_displayed() {
        return AssetImage(asset.screen_displayed.keyName);
      }
  /// asset: lib/assets/images/surgery/show_rtc_members.png
      // ignore: non_constant_identifier_names
      AssetImage show_rtc_members() {
        return AssetImage(asset.show_rtc_members.keyName);
      }
  /// asset: lib/assets/images/surgery/show_rtc_qrcode.png
      // ignore: non_constant_identifier_names
      AssetImage show_rtc_qrcode() {
        return AssetImage(asset.show_rtc_qrcode.keyName);
      }
  /// asset: lib/assets/images/surgery/stop.png
      // ignore: non_constant_identifier_names
      AssetImage stop() {
        return AssetImage(asset.stop.keyName);
      }
  /// asset: lib/assets/images/surgery/success_icon.png
      // ignore: non_constant_identifier_names
      AssetImage success_icon() {
        return AssetImage(asset.success_icon.keyName);
      }
  /// asset: lib/assets/images/surgery/surgery_cooperation.png
      // ignore: non_constant_identifier_names
      AssetImage surgery_cooperation() {
        return AssetImage(asset.surgery_cooperation.keyName);
      }
  /// asset: lib/assets/images/surgery/surgery_cooperation_no.png
      // ignore: non_constant_identifier_names
      AssetImage surgery_cooperation_no() {
        return AssetImage(asset.surgery_cooperation_no.keyName);
      }
  /// asset: lib/assets/images/surgery/surgery_creation_background.png
      // ignore: non_constant_identifier_names
      AssetImage surgery_creation_background() {
        return AssetImage(asset.surgery_creation_background.keyName);
      }
  /// asset: lib/assets/images/surgery/surgery_stop.png
      // ignore: non_constant_identifier_names
      AssetImage surgery_stop() {
        return AssetImage(asset.surgery_stop.keyName);
      }
  /// asset: lib/assets/images/surgery/system_setting.png
      // ignore: non_constant_identifier_names
      AssetImage system_setting() {
        return AssetImage(asset.system_setting.keyName);
      }
  /// asset: lib/assets/images/surgery/tracking.png
      // ignore: non_constant_identifier_names
      AssetImage tracking() {
        return AssetImage(asset.tracking.keyName);
      }
  /// asset: lib/assets/images/surgery/usb.png
      // ignore: non_constant_identifier_names
      AssetImage usb() {
        return AssetImage(asset.usb.keyName);
      }
  /// asset: lib/assets/images/surgery/volume_off.png
      // ignore: non_constant_identifier_names
      AssetImage volume_off() {
        return AssetImage(asset.volume_off.keyName);
      }
  /// asset: lib/assets/images/surgery/volume_on.png
      // ignore: non_constant_identifier_names
      AssetImage volume_on() {
        return AssetImage(asset.volume_on.keyName);
      }
  /// asset: lib/assets/images/surgery/what_mask.png
      // ignore: non_constant_identifier_names
      AssetImage what_mask() {
        return AssetImage(asset.what_mask.keyName);
      }
    }
/// This `_R_Svg` class is generated and contains references to static svg type image asset resources.
    // ignore: camel_case_types
    class _R_Svg {
      const _R_Svg();
    
      final asset = const _R_Svg_AssetResource();
    
  /// asset: lib/assets/images/common/cellular_connected.svg
      // ignore: non_constant_identifier_names
      AssetSvg cellular_connected({required double width, required double height}) {
        final imageProvider = AssetSvg(asset.cellular_connected.keyName, width: width, height: height);
        return imageProvider;
      }
  /// asset: lib/assets/images/common/cellular_disconnected.svg
      // ignore: non_constant_identifier_names
      AssetSvg cellular_disconnected({required double width, required double height}) {
        final imageProvider = AssetSvg(asset.cellular_disconnected.keyName, width: width, height: height);
        return imageProvider;
      }
  /// asset: lib/assets/images/common/icon_4g.svg
      // ignore: non_constant_identifier_names
      AssetSvg icon_4g({required double width, required double height}) {
        final imageProvider = AssetSvg(asset.icon_4g.keyName, width: width, height: height);
        return imageProvider;
      }
  /// asset: lib/assets/images/common/icon_5g.svg
      // ignore: non_constant_identifier_names
      AssetSvg icon_5g({required double width, required double height}) {
        final imageProvider = AssetSvg(asset.icon_5g.keyName, width: width, height: height);
        return imageProvider;
      }
  /// asset: lib/assets/images/common/logos.svg
      // ignore: non_constant_identifier_names
      AssetSvg logos({required double width, required double height}) {
        final imageProvider = AssetSvg(asset.logos.keyName, width: width, height: height);
        return imageProvider;
      }
  /// asset: lib/assets/images/common/network_none.svg
      // ignore: non_constant_identifier_names
      AssetSvg network_none({required double width, required double height}) {
        final imageProvider = AssetSvg(asset.network_none.keyName, width: width, height: height);
        return imageProvider;
      }
  /// asset: lib/assets/images/common/wifi_connected.svg
      // ignore: non_constant_identifier_names
      AssetSvg wifi_connected({required double width, required double height}) {
        final imageProvider = AssetSvg(asset.wifi_connected.keyName, width: width, height: height);
        return imageProvider;
      }
  /// asset: lib/assets/images/common/wifi_disconnected.svg
      // ignore: non_constant_identifier_names
      AssetSvg wifi_disconnected({required double width, required double height}) {
        final imageProvider = AssetSvg(asset.wifi_disconnected.keyName, width: width, height: height);
        return imageProvider;
      }
  /// asset: lib/assets/images/common/wired_connected.svg
      // ignore: non_constant_identifier_names
      AssetSvg wired_connected({required double width, required double height}) {
        final imageProvider = AssetSvg(asset.wired_connected.keyName, width: width, height: height);
        return imageProvider;
      }
  /// asset: lib/assets/images/common/wired_disconnected.svg
      // ignore: non_constant_identifier_names
      AssetSvg wired_disconnected({required double width, required double height}) {
        final imageProvider = AssetSvg(asset.wired_disconnected.keyName, width: width, height: height);
        return imageProvider;
      }
    }
/// This `_R_Text` class is generated and contains references to static text asset resources.
    // ignore: camel_case_types
    class _R_Text {
      const _R_Text();
    
      final asset = const _R_Text_AssetResource();
    
  /// asset: lib/assets/texts/insight.json
      // ignore: non_constant_identifier_names
      Future<String> insight_json() {
        final str = rootBundle.loadString(asset.insight_json.keyName);
        return str;
      }
  /// asset: lib/assets/texts/lottie/event_detection.json
      // ignore: non_constant_identifier_names
      Future<String> event_detection_json() {
        final str = rootBundle.loadString(asset.event_detection_json.keyName);
        return str;
      }
  /// asset: lib/assets/texts/lottie/event_detection_end.json
      // ignore: non_constant_identifier_names
      Future<String> event_detection_end_json() {
        final str = rootBundle.loadString(asset.event_detection_end_json.keyName);
        return str;
      }
  /// asset: lib/assets/texts/lottie/event_detection_long.json
      // ignore: non_constant_identifier_names
      Future<String> event_detection_long_json() {
        final str = rootBundle.loadString(asset.event_detection_long_json.keyName);
        return str;
      }
  /// asset: lib/assets/texts/lottie/event_detection_start.json
      // ignore: non_constant_identifier_names
      Future<String> event_detection_start_json() {
        final str = rootBundle.loadString(asset.event_detection_start_json.keyName);
        return str;
      }
  /// asset: lib/assets/texts/medias/bleed_warning.wav.txt
      // ignore: non_constant_identifier_names
      Future<String> bleed_warning_wav_txt() {
        final str = rootBundle.loadString(asset.bleed_warning_wav_txt.keyName);
        return str;
      }
  /// asset: lib/assets/texts/medias/enter_meeting.wav.txt
      // ignore: non_constant_identifier_names
      Future<String> enter_meeting_wav_txt() {
        final str = rootBundle.loadString(asset.enter_meeting_wav_txt.keyName);
        return str;
      }
  /// asset: lib/assets/texts/medias/mark_screenshot.wav.txt
      // ignore: non_constant_identifier_names
      Future<String> mark_screenshot_wav_txt() {
        final str = rootBundle.loadString(asset.mark_screenshot_wav_txt.keyName);
        return str;
      }
  /// asset: lib/assets/texts/medias/model-1.mp4.txt
      // ignore: non_constant_identifier_names
      Future<String> model_1_mp4_txt() {
        final str = rootBundle.loadString(asset.model_1_mp4_txt.keyName);
        return str;
      }
  /// asset: lib/assets/texts/medias/model-2.mp4.txt
      // ignore: non_constant_identifier_names
      Future<String> model_2_mp4_txt() {
        final str = rootBundle.loadString(asset.model_2_mp4_txt.keyName);
        return str;
      }
  /// asset: lib/assets/texts/medias/model-3.mp4.txt
      // ignore: non_constant_identifier_names
      Future<String> model_3_mp4_txt() {
        final str = rootBundle.loadString(asset.model_3_mp4_txt.keyName);
        return str;
      }
  /// asset: lib/assets/texts/medias/model-4.mp4.txt
      // ignore: non_constant_identifier_names
      Future<String> model_4_mp4_txt() {
        final str = rootBundle.loadString(asset.model_4_mp4_txt.keyName);
        return str;
      }
  /// asset: lib/assets/texts/medias/video_tutorial.mp4.txt
      // ignore: non_constant_identifier_names
      Future<String> video_tutorial_mp4_txt() {
        final str = rootBundle.loadString(asset.video_tutorial_mp4_txt.keyName);
        return str;
      }
  /// asset: lib/assets/texts/polkit/10-networkmanager.pkla.txt
      // ignore: non_constant_identifier_names
      Future<String> a10_networkmanager_pkla_txt() {
        final str = rootBundle.loadString(asset.a10_networkmanager_pkla_txt.keyName);
        return str;
      }
  /// asset: lib/assets/texts/sudoers/brightnessctl.txt
      // ignore: non_constant_identifier_names
      Future<String> brightnessctl_txt() {
        final str = rootBundle.loadString(asset.brightnessctl_txt.keyName);
        return str;
      }
  /// asset: lib/assets/texts/sudoers/evtest.txt
      // ignore: non_constant_identifier_names
      Future<String> evtest_txt() {
        final str = rootBundle.loadString(asset.evtest_txt.keyName);
        return str;
      }
  /// asset: lib/assets/texts/sudoers/nmcli.txt
      // ignore: non_constant_identifier_names
      Future<String> nmcli_txt() {
        final str = rootBundle.loadString(asset.nmcli_txt.keyName);
        return str;
      }
  /// asset: lib/assets/texts/zips/bluez-5.64.txt
      // ignore: non_constant_identifier_names
      Future<String> bluez_5_64_txt() {
        final str = rootBundle.loadString(asset.bluez_5_64_txt.keyName);
        return str;
      }
    }
/// This `_R_FontFamily` class is generated and contains references to static font asset resources.
    // ignore: camel_case_types
    class _R_FontFamily {
      const _R_FontFamily();
    
    }