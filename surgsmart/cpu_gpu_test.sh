#!/bin/bash

# 函数：检查并安装工具
install_tool() {
    if ! command -v "$1" &>/dev/null; then
        echo "Installing $1..."
        sudo apt-get install -y "$1" >/dev/null 2>&1
        if [ $? -eq 0 ]; then
            echo "$1 installed successfully."
        else
            echo "Failed to install $1. Please check your internet connection and package manager settings."
            exit 1
        fi
    else
        echo "$1 is already installed."
    fi
}

# 检查并安装必要的工具
install_tool stress
install_tool cpulimit
install_tool glmark2
install_tool powertop

# 设置 GPU 功率限制（以 NVIDIA GPU 为例）
sudo nvidia-smi -pl 150 >/dev/null 2>&1

# 运行 GPU 压力测试
echo "Starting GPU stress test..."
glmark2 >/dev/null 2>&1 &

# 运行 CPU 压力测试并限制其功率使用
echo "Starting CPU stress test..."
stress --cpu 8 --timeout 3600s >/dev/null 2>&1 &
STRESS_PID=$(pgrep stress)
sudo cpulimit -p "$STRESS_PID" -l 50 >/dev/null 2>&1 &

# 监控 CPU 和 GPU 的使用率和功耗
echo "Monitoring CPU and GPU usage and power consumption..."
for i in {1..3600}; do
    # 获取 CPU 使用率
    CPU_USAGE=$(top -b -n1 | grep "Cpu(s)" | awk '{print $2 + $4}')

    # 获取功耗（如果支持）
    CPU_POWER=$(sudo powertop --csv 2>/dev/null | grep 'CPU' | grep 'W' | awk '{print $3}')

    # 获取 GPU 使用率和功耗
    GPU_USAGE=$(nvidia-smi --query-gpu=utilization.gpu --format=csv,noheader,nounits 2>/dev/null)
    GPU_POWER=$(nvidia-smi --query-gpu=power.draw --format=csv,noheader,nounits 2>/dev/null)

    # 打印监控信息
    echo "Time: $i sec | CPU Usage: $CPU_USAGE% | CPU Power: ${CPU_POWER:-N/A} W | GPU Usage: $GPU_USAGE% | GPU Power: $GPU_POWER W"

    # 每秒监控一次
    sleep 1
done

# 杀死所有后台进程
kill "$STRESS_PID"
kill $(pgrep glmark2)

echo "Stress test completed."
