#!/bin/bash

set -e

# 默认值
VERSION=""
BUILD=""
SCRIPT_NAME=$(basename "$0")

# 颜色设置
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # 无颜色

# 显示帮助信息的函数
show_help() {
    echo -e "${GREEN}使用方法: $SCRIPT_NAME [选项]"
    echo -e "选项:"
    echo -e "  ${NC}-h, --help               ${GREEN}显示帮助信息${NC}"
    echo -e "  ${NC}-v, --version VERSION    ${GREEN}设置版本号 (格式: X.Y.Z)${NC}"
    echo -e "  ${NC}-b, --build BUILD        ${GREEN}设置构建号 (必须为数字)${NC}"
    echo -e "  ${NC}--default                ${GREEN}默认使用 pubspec.yaml 中的版本号${NC}"
    echo -e "  ${NC}--install-completion     ${GREEN}安装自动补全函数到 .bashrc${NC}"
    echo
}

# 打印错误信息的函数
print_error() {
    echo -e "${RED}错误: $1${NC}"
    echo
}

# 安装自动补全函数的函数
install_completion() {
    local completion_script="
###-begin-${SCRIPT_NAME}-completion-###
_${SCRIPT_NAME}_completion() {
  local cur=\${COMP_WORDS[COMP_CWORD]}
  local prev=\${COMP_WORDS[COMP_CWORD-1]}
  
  case \"\$prev\" in
    -v|--version)
      COMPREPLY=(\$(compgen -W \"版本号\" -- \"\$cur\"))
      return
      ;;
    -b|--build)
      COMPREPLY=(\$(compgen -W \"构建号\" -- \"\$cur\"))
      return
      ;;
    *)
      COMPREPLY=(\$(compgen -W \"-h --help -v --version -b --build --default --install-completion\" -- \"\$cur\"))
      return
      ;;
  esac
}
complete -F _${SCRIPT_NAME}_completion $SCRIPT_NAME
###-end-${SCRIPT_NAME}-completion-###
"

    # 检查 .bashrc 中是否已经存在补全函数
    if ! grep -q "_${SCRIPT_NAME}_completion" ~/.bashrc; then
        echo "$completion_script" >>~/.bashrc
        echo -e "${GREEN}补全功能已安装到 ~/.bashrc。请重新启动终端以生效。${NC}"
    else
        echo -e "${GREEN}补全功能已经安装过。${NC}"
    fi
    echo
}

# 如果没有参数，则显示帮助信息
if [[ $# -eq 0 ]]; then
    show_help
    exit 0
fi

# 参数解析
while [[ $# -gt 0 ]]; do
    case "$1" in
    -h | --help)
        show_help
        exit 0
        ;;
    -v | --version)
        if [[ -n "$2" ]]; then
            if [[ "$2" =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                VERSION="$2"
            else
                print_error "版本号格式错误，应为 X.Y.Z"
                exit 1
            fi
            shift 2
        else
            print_error "版本号参数缺失"
            exit 1
        fi
        ;;
    -b | --build)
        if [[ -n "$2" ]]; then
            if [[ "$2" =~ ^[0-9]+$ ]]; then
                BUILD="$2"
            else
                print_error "构建号必须是数字"
                exit 1
            fi
            shift 2
        else
            print_error "构建号参数缺失"
            exit 1
        fi
        ;;
    --default)
        vv=$(yq eval '.version' ./pubspec.yaml)
        VERSION=$(echo "$vv" | cut -d "+" -f 1)
        BUILD=$(echo "$vv" | cut -d "+" -f 2)
        break
        ;;
    --install-completion)
        install_completion
        exit 0
        ;;
    *)
        print_error "未知选项: $1"
        show_help
        exit 1
        ;;
    esac
done

# 检查如果使用了版本号或构建号，则必须同时使用
if { [[ -n "$VERSION" ]] && [[ -z "$BUILD" ]]; } || { [[ -z "$VERSION" ]] && [[ -n "$BUILD" ]]; }; then
    print_error "必须同时指定版本号和构建号"
    exit 1
fi

# 显示设置的版本号和构建号（如果有的话）
if [[ -n "$VERSION" && -n "$BUILD" ]]; then
    echo -e "${GREEN}版本号: ${NC}$VERSION"
    echo -e "${GREEN}构建号: ${NC}$BUILD"
    echo
fi

VV="${VERSION}+${BUILD}"

# 预置编译版本信息
yq eval '.version = "'"$VV"'"' -i ./pubspec.yaml

flutter clean

if ! flutter build linux --release --dart-define=mode=release; then
    print_error "构建失败!"
    exit 1
fi

DEBDIR=./build/withai-surgsmart-"${VERSION}"+"${BUILD}"

cp -r ./withai-surgsmart "$DEBDIR"

# 预置deb包版本信息
yq eval '.Version = "'"$VV"'"' -i "$DEBDIR"/DEBIAN/control

# 预置deb包应用信息
crudini --set "$DEBDIR"/usr/share/applications/surgsmart.desktop "Desktop Entry" "Version" "$VV"

mkdir -p "$DEBDIR"/opt/withai/surgsmart
cp -r ./build/linux/x64/release/bundle/* "$DEBDIR"/opt/withai/surgsmart/

if ! dpkg-deb --build "$DEBDIR" "$DEBDIR".deb; then
    print_error "打包失败!"
    exit 1
fi

echo -e "${GREEN}✓ Archive Success!${NC}"

echo -e "${GREEN}✓ FilePath: $DEBDIR.deb"
