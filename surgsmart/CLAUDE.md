# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

SurgSmart是一个与睿创新工作站APP，基于Flutter开发的跨平台桌面应用程序，主要用于手术智能监控和管理。项目采用模块化架构，依赖于基础开发框架app_foundation。

## Development Commands

### 运行项目
```bash
# 开发模式运行
./run.sh

# 构建模式运行
./run.sh --build

# 发布模式运行  
./run.sh --release

# 调试模式
./run.sh --debug

# 详细输出模式
./run.sh --verbose
```

### 构建和打包
```bash
# 清理项目
flutter clean

# 获取依赖
flutter pub get

# 构建代码生成
dart run build_runner clean
dart run build_runner build

# 构建Linux发布版本
flutter build linux --release --dart-define=mode=development

# 打包发布 (需要指定模式和描述)
./archive-package.sh --mode dev --desc "描述信息"
./archive-package.sh --mode stage --desc "描述信息"  
./archive-package.sh --mode release --desc "描述信息"
```

### 国际化处理
```bash
# 生成多语言文件
./build_l10n.sh

# 或者使用Flutter命令
flutter gen-l10n
```

### 测试
```bash
# 运行所有测试
flutter test

# 运行特定测试文件
flutter test test/widget_test.dart

# 代码分析
flutter analyze
```

## Project Architecture

### 核心目录结构
- **lib/src/modules/**: 业务功能模块，采用MVC架构模式
  - 每个模块包含view.dart(视图)、controller.dart(控制器)、widgets/(组件)
  - 主要模块：home(首页)、surgery(手术)、setting(设置)、file(文件)、report(报告)等
- **lib/src/apis/**: 网络接口层，分为http和mqtt两类
- **lib/src/models/**: 数据模型定义
- **lib/src/routes/**: 路由配置，使用GoRouter
- **lib/src/tools/**: 通用工具类
- **lib/src/widgets/**: 通用UI组件

### 架构特点
- 基于app_foundation框架，自动生成路由和文档
- 使用GoRouter进行页面导航管理
- 支持多窗口显示(multiWindow模式)
- 采用Provider/Controller模式进行状态管理
- 集成MQTT和HTTP双重通信协议
- 支持多媒体处理(medias_kit)和实时通信

### 依赖关系
- 核心依赖app_foundation位于../app_foundation/
- 媒体处理依赖medias_kit位于../medias_kit/
- 腾讯云存储依赖tencent_cos位于../tencent_cos/

### 代码生成
项目使用自动代码生成：
- 路由文件go_routes.dart由框架自动生成，不要手动修改
- 模块文档modules/README.md由框架自动生成
- 国际化文件通过build_l10n.sh脚本生成
- 资源文件通过r_dart_library生成r.g.dart

### 环境配置
- 开发环境变量通过--dart-define=mode设置
- 支持development、staging、release三种模式
- 多媒体依赖需要配置XF_ASSETS和LD_LIBRARY_PATH环境变量
- Linux平台需要配置DISPLAY=:0

### 特殊文件
- `go_paths.dart`: 路由路径定义，需要完善注解信息
- `app_context.dart`: 应用上下文和环境配置
- `device_cmd.dart`: 设备控制命令工具
- `multi_window.dart`: 多窗口管理工具

## Development Guidelines

### 模块开发
- 新增功能模块需要在go_paths.dart中配置路由信息
- 遵循现有的MVC架构模式
- 每个模块需要包含README.md文档

### 代码规范
- 使用驼峰命名法(camelCase)进行编码
- 文件创建优先使用蛇形命名法(snake_case)
- 遵循analysis_options.yaml中的lint规则
- 生成的代码文件不要手动修改

### 资源管理
- 图片资源按功能分类存放在assets/images下
- 多语言文本通过CSV文件管理，使用build_l10n.sh生成
- 字体和其他资源使用r_dart_library自动生成引用

### 设备集成
- 支持Linux桌面环境
- 集成视频采集设备和音频设备监听
- 支持设备远程控制和电源管理
- 集成蓝牙设备扫描和连接功能