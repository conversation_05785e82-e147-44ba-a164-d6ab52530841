#!/bin/bash

set -e

# 颜色设置
GREEN='\033[0;32m'
NC='\033[0m' # 无颜色

rm -r /tmp/withai/surgsmart/install

# 更新应用菜单缓存
update-desktop-database /usr/share/applications
echo -e "${GREEN}✓ 装载应用菜单成功!${NC}"

# 更新应用图标缓存
gtk-update-icon-cache /usr/share/icons/hicolor
echo -e "${GREEN}✓ 装载应用图标成功!${NC}"

# 添加串口访问权限, 下面 service 中, Group=root或dialout, 应该可以省略用户组提权
# usermod -aG dialout withai
# echo -e "${GREEN}✓ 添加串口访问权限成功!${NC}"

# 配置sudoer权限
sudo chown root:root /etc/sudoers.d/*
echo -e "${GREEN}✓ 配置sudoer权限完成!${NC}"

# 创建开机启动项
if ! [ -f "/etc/systemd/system/withai-surgsmart.service" ]; then
    cat <<EOF >/etc/systemd/system/withai-surgsmart.service
[Unit]
Description=SurgSmart M3
After=graphical.target

[Service]
ExecStart=/opt/withai/surgsmart/surgsmart
Restart=always
RestartSec=100ms
User=withai
Group=root
Environment="DISPLAY=:0"

[Install]
WantedBy=graphical.target
EOF

    echo -e "${GREEN}✓ 创建开机启动项成功!${NC}"

    # 更新守护进程信息
    systemctl daemon-reload
    echo -e "${GREEN}✓ 更新守护进程信息成功!${NC}"

    # 开启开机自动启动
    systemctl enable withai-surgsmart
    echo -e "${GREEN}✓ 开启开机启动成功!${NC}"
fi

# 安装完成
echo -e "${GREEN}✓ 安装成功!🎉🎉🎉${NC}"

# 启动程序
echo -e "${GREEN}✓ 正在启动程序...${NC}"
sleep 2s
systemctl restart withai-surgsmart
