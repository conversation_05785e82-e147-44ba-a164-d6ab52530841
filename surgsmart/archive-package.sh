#!/bin/bash

set -e

if [[ "$#" -eq 0 ]]; then
    printf "\n\033[31mUsage: ./archive-package --mode [dev|stage|release] --desc [text]\033[0m\n"
    exit 1
fi

while [[ "$#" -gt 0 ]]; do
    case $1 in
    --mode)
        MODE="$2"
        shift
        ;;
    --desc)
        DESC="$2"
        shift
        ;;
    -h | --help)
        printf "\n\033[31mUsage: ./archive-package --mode [dev|stage|release] --desc [text]\033[0m\n"
        exit 1
        ;;
    *)
        printf "\n\033[31mUnknown parameter passed: %s\033[0m\n" "$1"
        exit 1
        ;;
    esac
    shift
done

case "${MODE}" in
"dev")
    MODE=development
    DOMAIN=https://api.dev.surgsmart.com
    BUNDLE_NAME=development
    ;;
"stage")
    MODE=staging
    DOMAIN=https://api.stage.surgsmart.com
    BUNDLE_NAME=staging
    ;;
"release")
    MODE=release
    DOMAIN=https://api.surgsmart.com
    BUNDLE_NAME=release
    ;;
*)
    printf "\n\033[31mUnknown mode passed: %s\033[0m\n" "$MODE"
    exit 1
    ;;
esac

printf "\033[32mChecking published version...\033[0m\n"

VERSION=$(yq eval '.version' ./pubspec.yaml | awk -F '+' '{print $1}')

BUILD=$(curl -s --location --request GET "$DOMAIN/device/version-build/?module_id=4&version=$VERSION" | jq -r '.build')

if [[ "${BUILD}" == null ]]; then
    BUILD=0
else
    BUILD=$((BUILD + 1))
fi

yq eval '.version = "'"$VERSION+$BUILD"'"' -i ./pubspec.yaml

ORIGINAL_UPGRADE_FILE=$(pwd)/upgrade.sh

printf "\033[32mBuilding version: %s+%s...\033[0m\n" "$VERSION" "$BUILD"

flutter clean

flutter pub get

flutter build linux --release --dart-define=mode="$MODE"

cd build/linux/x64/release/ || exit

mv bundle $BUNDLE_NAME

cd $BUNDLE_NAME || exit

mkdir upgrade_scripts

UPGRADE_SCRIPT_NAME=upgrade_$VERSION.$BUILD.sh

cp "$ORIGINAL_UPGRADE_FILE" upgrade_scripts/"$UPGRADE_SCRIPT_NAME"

cd ..

mv $BUNDLE_NAME/surgsmart $BUNDLE_NAME/surgsmart_operating_room_frontend

zip -q -r surgsmart-operating-room.zip $BUNDLE_NAME

SHA256=$(sha256sum surgsmart-operating-room.zip | awk '{print $1}')

printf "\033[32mUploading package...\033[0m\n SHA256: %s\n" "$SHA256"

URL="https://withai-generic.pkg.coding.net/surgsmart-workstation/operating-room/surgsmart-operating-room.zip?version=$MODE-$VERSION.$BUILD"

curl -H "X-Artifact-Checksum-Sha256:$SHA256" \
    -T 'surgsmart-operating-room.zip' \
    -u 'operating-room-1713164041870:95925b57537f31d0a9c006b4935b888b00ae0901' "$URL"

printf "\033[32mPublishing package...\033[0m\n"

RET=$(
    curl -s -H 'Content-Type:application/json' \
        -X POST "$DOMAIN/device/version-build/" \
        -d '{
        "module": 4,
        "upgrade_type": 1,
        "version": "'"$VERSION"'",
        "build": '"$BUILD"',
        "description": "'"$DESC"'",
        "source_url": "'"$URL"'",
        "source_hash": "'"$SHA256"'"
        }'
)

code=$(echo "$RET" | jq -r '.code')

message=$(echo "$RET" | jq -r '.message')

if [[ "${code}" == "null" ]]; then
    printf "\n\033[32mSuccess version: %s+%s\033[0m\n" "$VERSION" "$BUILD"
else
    printf "\n\033[31mError: %s\033[0m\n" "$message"
fi

# for build in {0..20}; do
#     echo "$build"
#     curl -X DELETE -u operating-room-1713164041870:95925b57537f31d0a9c006b4935b888b00ae0901 "https://withai-generic.pkg.coding.net/surgsmart-workstation/operating-room/surgsmart-operating-room.zip?version=release-1.0.1.$build"
# done
