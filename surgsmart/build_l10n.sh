#!/bin/bash

# 输入CSV文件
CSV_FILE="lib/assets/texts/intl.csv"

# 输出的 arb 文件
OUTPUT_ZH="lib/l10n/intl_zh.arb"
OUTPUT_EN="lib/l10n/intl_en.arb"

# 初始化两个空的 arb 对象
echo "{" >$OUTPUT_ZH
echo "{" >$OUTPUT_EN

# 跳过 CSV 文件的第一行（标题行），从第二行开始处理
tail -n +2 $CSV_FILE | while IFS=',' read -r col1 col2 col3 col4 col5 col6 col7; do
    # 去除外层双引号和尾部的换行符和制表符等特殊字符, 并将 {#} 替换为英文逗号
    col5=$(echo "$col5" | sed 's/[[:space:]]*$//' | sed 's/^"\(.*\)"$/\1/' | sed 's/{#}/,/g')
    col6=$(echo "$col6" | sed 's/[[:space:]]*$//' | sed 's/^"\(.*\)"$/\1/' | sed 's/{#}/,/g')

    echo "  \"$col1\": \"$col5\"," >>$OUTPUT_ZH
    echo "  \"$col1\": \"$col6\"," >>$OUTPUT_EN
done

# 移除最后一个多余的逗号，并添加闭合括号
sed -i '$ s/,$//' $OUTPUT_ZH
sed -i '$ s/,$//' $OUTPUT_EN
echo "}" >>$OUTPUT_ZH
echo "}" >>$OUTPUT_EN

echo "arb files have been generated: $OUTPUT_ZH and $OUTPUT_EN"
